import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime
import json
from typing import Dict, List, Optional
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import re
import dashscope as ds
import time

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from connector.download import config, ODPSDownloader

BASE_PATH = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class FeatureEngineer:
    def __init__(self):
        self.config = config
        self.downloader = ODPSDownloader(config)

    def download(self, download_config):
        if download_config.get("output_dir") is None:
            download_config["output_dir"] = os.path.join(BASE_PATH, "data", "ecs_deploy_key_metric_clean_noise")
        if download_config.get("output_filename") is None:
            download_config["output_filename"] = "feature.csv"
        if download_config.get("chunk_size") is None:
            download_config["chunk_size"] = 10000

        if os.path.exists(os.path.join(download_config["output_dir"], download_config["output_filename"])):
            print(f"File {download_config['output_filename']} already exists, skipping download.")
            return

        self.downloader.download_with_custom_params(
            sql_query=download_config["sql_query"],
            output_dir=download_config["output_dir"],
            output_filename=download_config["output_filename"],
            chunk_size=download_config["chunk_size"],
        )

    def load_feature(self):
        feature_path = os.path.join(BASE_PATH, "data", "ecs_deploy_key_metric_clean_noise", "feature.csv")
        if not os.path.exists(feature_path):
            print(f"Feature file {feature_path} does not exist.")
            return None

        df = pd.read_csv(feature_path)
        return df

    def generate_feature(self):
        """
        生成高级特征工程特征，包括：
        1. 时间序列特征
        2. 统计与比例特征
        3. 文本语义特征
        """
        df = self.load_feature()
        if df is None:
            return None

        print("开始生成特征...")

        # 1. 时间序列特征
        df = self._generate_temporal_features(df)

        # 2. 统计与比例特征
        df = self._generate_statistical_features(df)

        # 4. 结构化特征提取
        df = self._generate_structured_features(df)

        # 3. 文本语义特征
        df = self._generate_semantic_features(df)

        print("特征生成完成!")
        return df

    def _generate_temporal_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成时间序列特征"""
        print("生成时间序列特征...")

        # 转换时间字段
        df["deploy_time"] = pd.to_datetime(df["deploy_time"])
        df["exception_start_time"] = pd.to_datetime(df["exception_start_time"])
        df["exception_end_time"] = pd.to_datetime(df["exception_end_time"])

        # 1. is_pre_release_anomaly: 异常是否发生在发布之前
        df["is_pre_release_anomaly"] = df["exception_start_time"] < df["deploy_time"]

        # 2. time_of_day_deploy: 发布时间段
        df["deploy_hour"] = df["deploy_time"].dt.hour
        df["time_of_day_deploy"] = pd.cut(df["deploy_hour"], bins=[0, 6, 12, 18, 24], labels=["00-06", "06-12", "12-18", "18-24"], include_lowest=True)

        # 3. day_of_week_deploy: 发布星期
        df["day_of_week_deploy"] = df["deploy_time"].dt.dayofweek  # 0=Monday, 6=Sunday
        df["is_weekend_deploy"] = df["day_of_week_deploy"].isin([5, 6])  # Saturday, Sunday

        # 4. 异常持续时长相关特征
        df["exception_duration_minutes"] = (df["exception_end_time"] - df["exception_start_time"]).dt.total_seconds() / 60

        # 5. 发布到异常的绝对时间差
        df["deploy_exception_abs_duration"] = abs(df["deploy_exception_duration"])

        return df

    def _generate_statistical_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成统计与比例特征"""
        print("生成统计与比例特征...")

        # 1. release_hit_rate: 发布任务中异常的命中率
        df["release_hit_rate"] = np.where(df["deploy_nc_count"] > 0, df["deploy_exception_nc_count"] / df["deploy_nc_count"], 0)

        # 2. exception_noise_rate: 异常的背景噪音率
        # 计算每天总的NC数量（需要根据实际情况调整）
        daily_stats = (
            df.groupby("ds")
            .agg(
                {
                    "nc_id": "nunique",  # 每天唯一NC数量
                    "exception_nc_count": "first",  # 异常出现的NC数量
                    "exception_keymetric_nc_count": "first",  # 关联发布的异常NC数量
                }
            )
            .reset_index()
        )

        # 计算噪音率
        df["exception_noise_count"] = df["exception_nc_count"] - df["exception_keymetric_nc_count"]
        df["exception_noise_rate"] = np.where(df["exception_nc_count"] > 0, df["exception_noise_count"] / df["exception_nc_count"], 0)

        # 3. anomaly_density_on_family: 机型族异常密度
        family_stats = (
            df.groupby(["ds", "instance_type_family", "exception_name"])
            .agg({"instance_id": "nunique", "nc_id": "nunique"})  # 该机型族当天该异常的实例数  # 该机型族当天的总NC数
            .reset_index()
        )

        family_stats["anomaly_density_on_family"] = family_stats["instance_id"] / family_stats["nc_id"]

        # 合并回原DataFrame
        df = df.merge(
            family_stats[["ds", "instance_type_family", "exception_name", "anomaly_density_on_family"]],
            on=["ds", "instance_type_family", "exception_name"],
            how="left",
        )

        # 4. 异常严重程度相关特征
        df["is_long_duration_exception"] = df["exception_duration_minutes"] > 60  # 超过1小时的异常
        df["exception_severity_score"] = df["duration"] * df["release_hit_rate"]  # 异常严重程度得分

        return df

    def _generate_semantic_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成文本语义特征，使用大模型进行语义扩展和text-embedding"""
        print("生成文本语义特征...")

        # 填充空值
        df["deploy_content"] = df["deploy_content"].fillna("")
        df["exception_name"] = df["exception_name"].fillna("")
        df["xdc_component"] = df["xdc_component"].fillna("")
        df["xdc_subcomponent"] = df["xdc_subcomponent"].fillna("")
        df["tag_name"] = df["tag_name"].fillna("")
        df["break_reason"] = df["break_reason"].fillna("")

        # 创建异常语义描述
        df["exception_semantics"] = "异常名称: " + df["exception_name"] + " 部件类别: " + df["xdc_component"] + " 子部件类别: " + df["xdc_subcomponent"]

        # 使用大模型进行语义扩展
        print("使用大模型进行语义扩展...")
        df = self._enhance_with_llm_semantics(df)

        # 使用text-embedding获取高质量语义向量
        print("使用text-embedding计算语义相似度...")
        df = self._compute_embedding_similarity(
            df
        )  # embedding_similarity_deploy_exception, embedding_similarity_deploy_reason, embedding_similarity_exception_reason

        # 传统TF-IDF作为备选特征
        df = self._compute_tfidf_features(df)

        # 文本长度特征
        df["deploy_content_length"] = df["deploy_content"].str.len()
        df["exception_semantics_length"] = df["exception_semantics"].str.len()

        return df

    def _generate_structured_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成结构化特征"""
        print("生成结构化特征...")

        # 从deploy_content中提取结构化信息
        df["parsed_deploy_components"] = df["deploy_content"].apply(self._parse_deploy_content)

        # 展开结构化信息
        df["restarted_services_count"] = df["parsed_deploy_components"].apply(lambda x: len(x.get("restarted", [])))
        df["installed_rpms_count"] = df["parsed_deploy_components"].apply(lambda x: len(x.get("installed", [])))

        # 常见服务和RPM的标识
        common_services = ["nc", "agent", "kvm", "network", "storage"]
        common_rpms = ["houyi", "agent-hook", "kvm-config", "libvirt"]

        for service in common_services:
            df[f"involves_{service}_service"] = df["deploy_content"].str.contains(service, case=False, na=False)

        for rpm in common_rpms:
            df[f"involves_{rpm}_rpm"] = df["deploy_content"].str.contains(rpm, case=False, na=False)

        # 异常类型分类特征
        df["is_performance_exception"] = df["monitor_type"] == "performance"
        df["is_unavailable_exception"] = df["monitor_type"] == "unavailable"

        # 存储类型特征
        df["is_local_disk_bool"] = df["is_local_disk"] == "True"

        return df

    def _parse_deploy_content(self, content: str) -> Dict[str, List[str]]:
        """解析deploy_content中的结构化信息"""
        if pd.isna(content) or not content.strip():
            return {"restarted": [], "installed": []}

        result = {"restarted": [], "installed": []}

        # 以分号分割内容
        content_parts = content.split(";")

        # 查找重启的服务
        restart_patterns = [r"restart\s+([a-zA-Z0-9_-]+)", r"service\s+([a-zA-Z0-9_-]+)\s+restart", r"systemctl\s+restart\s+([a-zA-Z0-9_-]+)"]

        for part in content_parts:
            part = part.strip()
            for pattern in restart_patterns:
                matches = re.findall(pattern, part, re.IGNORECASE)
                result["restarted"].extend(matches)

        # 查找安装的RPM
        rpm_patterns = [r"rpm:([a-zA-Z0-9_-]+)", r"install\s+([a-zA-Z0-9_-]+\.rpm)", r"yum\s+install\s+([a-zA-Z0-9_-]+)"]

        for part in content_parts:
            part = part.strip()
            for pattern in rpm_patterns:
                matches = re.findall(pattern, part, re.IGNORECASE)
                result["installed"].extend(matches)

        # 去重
        result["restarted"] = list(set(result["restarted"]))
        result["installed"] = list(set(result["installed"]))

        return result

    def save_features(self, df: pd.DataFrame, output_path: str = None):
        """保存生成的特征到文件"""
        if output_path is None:
            output_path = os.path.join(BASE_PATH, "data", "ecs_deploy_key_metric_clean_noise", "features_enhanced.csv")

        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        df.to_csv(output_path, index=False)
        print(f"特征已保存到: {output_path}")

        # 打印特征统计信息
        print(f"总样本数: {len(df)}")
        print(f"总特征数: {df.shape[1]}")
        print(f"发布前异常比例: {df['is_pre_release_anomaly'].mean():.4f}")

        # 传统语义相似度统计
        if "feature_similarity_deploy_exception" in df.columns:
            print(f"平均传统语义相似度: {df['feature_similarity_deploy_exception'].mean():.4f}")

        # 大模型增强的语义相似度统计
        if "embedding_similarity_deploy_exception" in df.columns:
            print(f"平均embedding语义相似度: {df['embedding_similarity_deploy_exception'].mean():.4f}")
        if "embedding_similarity_deploy_reason" in df.columns:
            print(f"平均部署-故障原因相似度: {df['embedding_similarity_deploy_reason'].mean():.4f}")
        if "embedding_similarity_exception_reason" in df.columns:
            print(f"平均异常-故障原因相似度: {df['embedding_similarity_exception_reason'].mean():.4f}")

        # TF-IDF相似度统计
        if "tfidf_similarity_deploy_exception" in df.columns:
            print(f"平均TF-IDF相似度: {df['tfidf_similarity_deploy_exception'].mean():.4f}")

        # 大模型语义扩展统计
        if "llm_correlation_reasoning" in df.columns:
            non_empty_reasoning = df[df["llm_correlation_reasoning"] != ""]
            print(f"LLM语义扩展样本数: {len(non_empty_reasoning)}")
            if len(non_empty_reasoning) > 0:
                print(f"平均扩展文本长度: {non_empty_reasoning['llm_correlation_reasoning'].str.len().mean():.1f}")

        # 文本长度统计
        if "enhanced_deploy_content" in df.columns:
            print(f"增强后部署内容平均长度: {df['enhanced_deploy_content'].str.len().mean():.1f}")
        if "enhanced_exception_semantics" in df.columns:
            print(f"增强后异常语义平均长度: {df['enhanced_exception_semantics'].str.len().mean():.1f}")

        print(f"语义特征增强完成！新增特征包括:")
        print(f"- embedding_similarity_deploy_exception: 基于text-embedding的部署-异常相似度")
        print(f"- embedding_similarity_deploy_reason: 基于text-embedding的部署-故障原因相似度")
        print(f"- embedding_similarity_exception_reason: 基于text-embedding的异常-故障原因相似度")
        print(f"- tfidf_similarity_deploy_exception: 基于TF-IDF的部署-异常相似度")
        print(f"- tfidf_similarity_deploy_reason: 基于TF-IDF的部署-故障原因相似度")
        print(f"- llm_correlation_reasoning: 大模型关联性分析")
        print(f"- enhanced_deploy_content: 大模型增强的部署内容")
        print(f"- enhanced_exception_semantics: 大模型增强的异常语义")

    def _enhance_with_llm_semantics(self, df: pd.DataFrame) -> pd.DataFrame:
        """使用大模型对文本进行语义扩展和解释"""
        from concurrent.futures import ThreadPoolExecutor
        
        # 对前50条数据进行语义扩展（避免API调用过多）
        sample_size = len(df)
        sample_indices = np.random.choice(len(df), sample_size, replace=False) if len(df) > sample_size else range(len(df))

        enhanced_deploy_content = []
        enhanced_exception_semantics = []
        correlation_reasoning = []

        # 定义并发执行的函数
        def process_row(i):
            if i in sample_indices:
                row = df.iloc[i]
                deploy_content = row["deploy_content"]
                exception_semantics = row["exception_semantics"]
                break_reason = row["break_reason"]
                
                # 并发调用三个LLM函数
                with ThreadPoolExecutor(max_workers=3) as executor:
                    future_deploy = executor.submit(self._llm_enhance_deploy_content, deploy_content)
                    future_exception = executor.submit(self._llm_enhance_exception_semantics, exception_semantics)
                    future_reasoning = executor.submit(self._llm_analyze_correlation, deploy_content, exception_semantics, break_reason)
                    
                    enhanced_deploy = future_deploy.result()
                    enhanced_exception = future_exception.result()
                    reasoning = future_reasoning.result()
                
                return enhanced_deploy, enhanced_exception, reasoning
            else:
                # 对于未采样的数据，使用原始文本
                row = df.iloc[i]
                return row["deploy_content"], row["exception_semantics"], ""
        
        # 使用ThreadPoolExecutor并发处理所有行
        with ThreadPoolExecutor(max_workers=10) as executor:
            results = list(executor.map(process_row, range(len(df))))
            
        # 解包结果
        for enhanced_deploy, enhanced_exception, reasoning in results:
            enhanced_deploy_content.append(enhanced_deploy)
            enhanced_exception_semantics.append(enhanced_exception)
            correlation_reasoning.append(reasoning)

        df["enhanced_deploy_content"] = enhanced_deploy_content
        df["enhanced_exception_semantics"] = enhanced_exception_semantics
        df["llm_correlation_reasoning"] = correlation_reasoning

        return df

    def _llm_enhance_deploy_content(self, deploy_content: str) -> str:
        """使用大模型扩展部署内容的语义"""
        if not deploy_content.strip():
            return deploy_content

        prompt = f"""
        作为ECS运维专家，请分析以下部署内容，提取关键信息并进行语义扩展：

        部署内容：{deploy_content}

        请从以下角度进行分析：
        1. 涉及的系统组件和服务
        2. 操作类型（重启、安装、配置等）
        3. 可能的影响范围
        4. 风险等级评估

        请用简洁的中文总结，不超过100字：
        """

        try:
            response = ds.Generation.call(model="qwen-turbo", prompt=prompt, max_tokens=200)
            if response.status_code == 200:
                return response.output["text"].strip()
            else:
                return deploy_content
        except Exception as e:
            print(f"LLM enhance deploy content error: {e}")
            return deploy_content

    def _llm_enhance_exception_semantics(self, exception_semantics: str) -> str:
        """使用大模型扩展异常语义"""
        if not exception_semantics.strip():
            return exception_semantics

        prompt = f"""
        作为ECS运维专家，请分析以下异常信息，提取关键特征并进行语义扩展：

        异常信息：{exception_semantics}

        请从以下角度进行分析：
        1. 异常类型和严重程度
        2. 可能的根本原因
        3. 影响的系统层面
        4. 典型的解决方案

        请用简洁的中文总结，不超过100字：
        """

        try:
            response = ds.Generation.call(model="qwen-turbo", prompt=prompt, max_tokens=200)
            if response.status_code == 200:
                return response.output["text"].strip()
            else:
                return exception_semantics
        except Exception as e:
            print(f"LLM enhance exception semantics error: {e}")
            return exception_semantics

    def _llm_analyze_correlation(self, deploy_content: str, exception_semantics: str, break_reason: str) -> str:
        """使用大模型分析部署与异常的关联性"""
        if not deploy_content.strip() or not exception_semantics.strip():
            return ""

        prompt = f"""
        作为ECS运维专家，请分析部署操作与异常之间的关联性：

        部署内容：{deploy_content}
        异常信息：{exception_semantics}
        故障原因：{break_reason}

        请分析：
        1. 部署操作是否可能导致该异常？(高/中/低/无关联)
        2. 关联的技术原理是什么？
        3. 置信度评估(0-10分)

        请用简洁的中文回答，不超过80字：
        """

        try:
            response = ds.Generation.call(model="qwen-turbo", prompt=prompt, max_tokens=150)
            if response.status_code == 200:
                return response.output["text"].strip()
            else:
                return ""
        except Exception as e:
            print(f"LLM analyze correlation error: {e}")
            return ""

    def _compute_embedding_similarity(self, df: pd.DataFrame) -> pd.DataFrame:
        """使用text-embedding计算高质量语义相似度"""

        # 获取文本embedding
        print("获取deploy_content的embedding...")
        deploy_embeddings = self._get_batch_embeddings(df["enhanced_deploy_content"].tolist())

        print("获取exception_semantics的embedding...")
        exception_embeddings = self._get_batch_embeddings(df["enhanced_exception_semantics"].tolist())

        print("获取break_reason的embedding...")
        break_reason_embeddings = self._get_batch_embeddings(df["break_reason"].tolist())

        # 计算语义相似度
        similarity_deploy_exception = []
        similarity_deploy_reason = []
        similarity_exception_reason = []

        for i in range(len(df)):
            # 计算deploy_content和exception_semantics的相似度
            sim_de = self._cosine_similarity(deploy_embeddings[i], exception_embeddings[i])
            similarity_deploy_exception.append(sim_de)

            # 计算deploy_content和break_reason的相似度
            sim_dr = self._cosine_similarity(deploy_embeddings[i], break_reason_embeddings[i])
            similarity_deploy_reason.append(sim_dr)

            # 计算exception_semantics和break_reason的相似度
            sim_er = self._cosine_similarity(exception_embeddings[i], break_reason_embeddings[i])
            similarity_exception_reason.append(sim_er)

        df["embedding_similarity_deploy_exception"] = similarity_deploy_exception
        df["embedding_similarity_deploy_reason"] = similarity_deploy_reason
        df["embedding_similarity_exception_reason"] = similarity_exception_reason

        return df

    def _get_batch_embeddings(self, texts: List[str], batch_size: int = 10) -> List[np.ndarray]:
        """批量获取文本embedding"""
        embeddings = []

        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i : i + batch_size]
            batch_embeddings = []

            for text in batch_texts:
                embedding = self._get_single_embedding(text)
                batch_embeddings.append(embedding)

            embeddings.extend(batch_embeddings)

            # 添加延时避免API限制
            import time

            time.sleep(0.1)

        return embeddings

    def _get_single_embedding(self, text: str) -> np.ndarray:
        """获取单个文本的embedding"""
        if not text.strip():
            return np.zeros(1024)  # text-embedding-v4的向量维度

        try:
            response = ds.TextEmbedding.call(model="text-embedding-v4", input=text)
            if response.status_code == 200:
                return np.array(response.output["embeddings"][0]["embedding"])
            else:
                print(f"Embedding API error: {response.status_code}")
                return np.zeros(1024)
        except Exception as e:
            print(f"Embedding error: {e}")
            return np.zeros(1024)

    def _cosine_similarity(self, vec1: np.ndarray, vec2: np.ndarray) -> float:
        """计算两个向量的余弦相似度"""
        if np.linalg.norm(vec1) == 0 or np.linalg.norm(vec2) == 0:
            return 0.0
        return np.dot(vec1, vec2) / (np.linalg.norm(vec1) * np.linalg.norm(vec2))

    def _compute_tfidf_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算TF-IDF特征作为备选"""

        # 使用TF-IDF向量化文本
        vectorizer = TfidfVectorizer(max_features=1000, stop_words="english", ngram_range=(1, 2))

        # 合并所有文本进行向量化
        all_texts = list(df["enhanced_deploy_content"]) + list(df["enhanced_exception_semantics"]) + list(df["break_reason"])
        all_texts = [text for text in all_texts if text.strip()]

        if len(all_texts) > 0:
            # 训练向量化器
            vectorizer.fit(all_texts)

            # 向量化各个文本字段
            deploy_content_vec = vectorizer.transform(df["enhanced_deploy_content"])
            exception_semantics_vec = vectorizer.transform(df["enhanced_exception_semantics"])
            break_reason_vec = vectorizer.transform(df["break_reason"])

            # 计算TF-IDF相似度
            tfidf_similarity_deploy_exception = []
            tfidf_similarity_deploy_reason = []

            for i in range(len(df)):
                # 计算deploy_content和exception_semantics的TF-IDF相似度
                sim_de = cosine_similarity(deploy_content_vec[i], exception_semantics_vec[i])[0][0]
                tfidf_similarity_deploy_exception.append(sim_de)

                # 计算deploy_content和break_reason的TF-IDF相似度
                sim_dr = cosine_similarity(deploy_content_vec[i], break_reason_vec[i])[0][0]
                tfidf_similarity_deploy_reason.append(sim_dr)

            df["tfidf_similarity_deploy_exception"] = tfidf_similarity_deploy_exception
            df["tfidf_similarity_deploy_reason"] = tfidf_similarity_deploy_reason
        else:
            df["tfidf_similarity_deploy_exception"] = 0.0
            df["tfidf_similarity_deploy_reason"] = 0.0

        return df


if __name__ == "__main__":
    # 下载数据
    sql = "SELECT * FROM ecs_dw.ecs_deploy_key_metric_clean_noise"
    feature = FeatureEngineer()
    df = feature.download({"sql_query": sql})

    # 特征工程
    df = feature.generate_feature()

    feature.save_features(df)
