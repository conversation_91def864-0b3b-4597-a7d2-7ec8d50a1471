#!/usr/bin/env python3
"""
日志聚类方法使用示例

这个脚本展示了如何使用两种日志聚类方法：
1. LLM模板提取聚类
2. Embedding向量聚类

使用前请确保：
1. 设置 DASHSCOPE_API_KEY 环境变量
2. 安装必要的依赖包：pip install scikit-learn scipy rich
"""

import sys
import os
import pandas as pd
import numpy as np

# 添加路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'control_log_diagnose/src2'))

from log_cluster import LogTemplateExtractor

def load_sample_data():
    """加载示例数据或从parquet文件加载"""
    
    # 首先尝试从parquet文件加载
    data_path = os.path.join(os.path.dirname(__file__), "output", "control_log_diagnose", "processed_data")
    if os.path.exists(data_path):
        import glob
        pattern = os.path.join(data_path, "*.parquet")
        parquet_files = glob.glob(pattern)
        
        if parquet_files:
            print(f"从parquet文件加载数据: {parquet_files[0]}")
            df = pd.read_parquet(parquet_files[0], engine="pyarrow")
            logs = df["content"].tolist()[:200]  # 取前200条避免API调用过多
            return logs, "real_data"
    
    # 如果没有parquet文件，使用示例数据
    print("使用示例数据")
    sample_logs = [
        "2025-01-15 10:30:25 INFO User admin login successful from *************",
        "2025-01-15 10:30:26 INFO User admin login successful from *************", 
        "2025-01-15 10:30:27 INFO User guest login successful from ********",
        "2025-01-15 10:30:28 ERROR User invalid login failed from *************",
        "2025-01-15 10:30:29 ERROR User timeout login failed from *********",
        "2025-01-15 10:31:01 INFO Database connection established to mysql://localhost:3306/app",
        "2025-01-15 10:31:02 INFO Database connection established to mysql://db.example.com:3306/prod",
        "2025-01-15 10:31:03 ERROR Database connection failed to mysql://localhost:3306/app timeout",
        "2025-01-15 10:31:10 INFO Processing request req_12345 with status success duration 150ms",
        "2025-01-15 10:31:11 INFO Processing request req_67890 with status success duration 200ms",
        "2025-01-15 10:31:12 ERROR Processing request req_11111 with status failed error timeout",
        "2025-01-15 10:32:01 INFO System startup completed in 5.2 seconds memory 2GB",
        "2025-01-15 10:32:02 INFO System startup completed in 3.8 seconds memory 4GB",
        "2025-01-15 10:33:01 WARN Memory usage: 85% of 16GB available 2.4GB",
        "2025-01-15 10:33:02 WARN Memory usage: 92% of 32GB available 2.5GB",
        "2025-01-15 10:33:03 INFO CPU usage: 45% on 8 cores load 3.6",
        "2025-01-15 10:33:04 WARN CPU usage: 78% on 16 cores load 12.5",
        "2025-01-15 10:34:01 ERROR File not found /tmp/data_20250115.txt permission denied",
        "2025-01-15 10:34:02 ERROR File not found /var/log/app.log disk full",
        "2025-01-15 10:34:03 WARN Disk space low on /dev/sda1 usage 95% available 500MB",
        # 添加更多相似的日志来测试聚类效果
        "2025-01-15 10:35:01 INFO User manager login successful from 172.16.0.10",
        "2025-01-15 10:35:02 INFO User operator login successful from 172.16.0.11",
        "2025-01-15 10:35:03 ERROR User expired login failed from 172.16.0.20",
        "2025-01-15 10:36:01 INFO Database connection established to postgresql://db.internal:5432/analytics",
        "2025-01-15 10:36:02 ERROR Database connection failed to redis://cache.internal:6379 connection refused",
        "2025-01-15 10:37:01 INFO Processing request req_99999 with status success duration 89ms",
        "2025-01-15 10:37:02 ERROR Processing request req_88888 with status failed error validation",
        "2025-01-15 10:38:01 INFO System shutdown initiated gracefully",
        "2025-01-15 10:38:02 WARN System shutdown forced after timeout",
        "2025-01-15 10:39:01 WARN Memory usage: 78% of 8GB available 1.8GB",
    ]
    return sample_logs, "sample_data"

def demonstrate_llm_clustering(clusterer, processed_logs, data_type):
    """演示LLM模板聚类"""
    print("\n" + "="*80)
    print("方法1: LLM模板提取聚类")
    print("="*80)
    
    if data_type == "real_data":
        # 对于真实数据，使用更保守的参数
        max_iterations = 3
        min_match_ratio = 0.85
        sample_size = min(50, len(processed_logs))
    else:
        # 对于示例数据，使用更积极的参数
        max_iterations = 5
        min_match_ratio = 0.9
        sample_size = len(processed_logs)
    
    sample_logs = processed_logs[:sample_size]
    print(f"使用样本大小: {sample_size}")
    
    try:
        llm_clusters = clusterer.cluster_logs_with_llm_templates(
            sample_logs, 
            max_iterations=max_iterations, 
            min_match_ratio=min_match_ratio
        )
        
        print(f"\n=== LLM聚类结果分析 ===")
        print(f"总聚类数: {len(llm_clusters)}")
        
        # 按聚类大小排序
        sorted_clusters = sorted(llm_clusters.items(), key=lambda x: len(x[1]), reverse=True)
        
        print(f"\n前10个最大聚类:")
        for i, (template, indices) in enumerate(sorted_clusters[:10]):
            print(f"\n{i+1}. 模板: {template[:100]}{'...' if len(template) > 100 else ''}")
            print(f"   匹配日志数: {len(indices)}")
            if indices:
                print(f"   示例: {sample_logs[indices[0]][:80]}{'...' if len(sample_logs[indices[0]]) > 80 else ''}")
        
        return llm_clusters
        
    except Exception as e:
        print(f"LLM聚类失败: {e}")
        print("可能的原因:")
        print("1. 未设置DASHSCOPE_API_KEY环境变量")
        print("2. API密钥无效或余额不足")
        print("3. 网络连接问题")
        return None

def demonstrate_embedding_clustering(clusterer, processed_logs, data_type):
    """演示Embedding聚类"""
    print("\n" + "="*80)
    print("方法2: Embedding向量聚类")
    print("="*80)
    
    if data_type == "real_data":
        sample_size = min(30, len(processed_logs))  # 更小的样本以节省API调用
        methods = ['hierarchical']  # 只使用一种方法
    else:
        sample_size = min(20, len(processed_logs))  # 示例数据也限制大小
        methods = ['hierarchical', 'kmeans']
    
    sample_logs = processed_logs[:sample_size]
    print(f"使用样本大小: {sample_size}")
    
    try:
        embedding_clusters = clusterer.cluster_logs_with_embeddings(
            sample_logs, 
            methods=methods
        )
        
        print(f"\n=== Embedding聚类结果分析 ===")
        for method, clusters in embedding_clusters.items():
            print(f"\n{method.upper()} 方法结果:")
            print(f"总聚类数: {len(clusters)}")
            
            # 按聚类大小排序
            sorted_clusters = sorted(clusters.items(), key=lambda x: len(x[1]), reverse=True)
            
            print(f"前5个最大聚类:")
            for i, (cluster_id, indices) in enumerate(sorted_clusters[:5]):
                print(f"  {i+1}. {cluster_id}: {len(indices)} 条日志")
                if indices:
                    print(f"     示例: {sample_logs[indices[0]][:60]}{'...' if len(sample_logs[indices[0]]) > 60 else ''}")
        
        return embedding_clusters
        
    except Exception as e:
        print(f"Embedding聚类失败: {e}")
        print("可能的原因:")
        print("1. 未设置DASHSCOPE_API_KEY环境变量")
        print("2. API密钥无效或余额不足")
        print("3. 网络连接问题")
        return None

def compare_clustering_results(llm_clusters, embedding_clusters, processed_logs):
    """比较两种聚类方法的结果"""
    print("\n" + "="*80)
    print("聚类方法对比分析")
    print("="*80)
    
    total_logs = len(processed_logs)
    
    if llm_clusters:
        llm_cluster_count = len(llm_clusters)
        llm_sizes = [len(indices) for indices in llm_clusters.values()]
        llm_avg_size = np.mean(llm_sizes)
        llm_max_size = max(llm_sizes)
        llm_min_size = min(llm_sizes)
        
        print(f"LLM模板聚类:")
        print(f"  - 聚类数量: {llm_cluster_count}")
        print(f"  - 平均聚类大小: {llm_avg_size:.1f}")
        print(f"  - 最大聚类大小: {llm_max_size}")
        print(f"  - 最小聚类大小: {llm_min_size}")
        print(f"  - 压缩比: {total_logs / llm_cluster_count:.1f}:1")
        print(f"  - 聚类效果: {'优秀' if llm_cluster_count < total_logs * 0.3 else '良好' if llm_cluster_count < total_logs * 0.6 else '需要优化'}")
    
    if embedding_clusters:
        print(f"\nEmbedding向量聚类:")
        for method, clusters in embedding_clusters.items():
            cluster_count = len(clusters)
            sizes = [len(indices) for indices in clusters.values()]
            avg_size = np.mean(sizes)
            max_size = max(sizes)
            min_size = min(sizes)
            
            print(f"  {method.upper()}方法:")
            print(f"    - 聚类数量: {cluster_count}")
            print(f"    - 平均聚类大小: {avg_size:.1f}")
            print(f"    - 最大聚类大小: {max_size}")
            print(f"    - 最小聚类大小: {min_size}")
            print(f"    - 压缩比: {total_logs / cluster_count:.1f}:1")
            print(f"    - 聚类效果: {'优秀' if cluster_count < total_logs * 0.3 else '良好' if cluster_count < total_logs * 0.6 else '需要优化'}")

def main():
    """主函数"""
    print("日志聚类方法演示")
    print("="*80)
    
    # 检查API密钥
    api_key = os.getenv('DASHSCOPE_API_KEY')
    if not api_key:
        print("⚠️  警告: 未设置DASHSCOPE_API_KEY环境变量")
        print("   LLM聚类和Embedding聚类将无法正常工作")
        print("   请设置环境变量: export DASHSCOPE_API_KEY=your_api_key")
        print()
    
    # 加载数据
    logs, data_type = load_sample_data()
    print(f"加载了 {len(logs)} 条日志 (数据类型: {data_type})")
    
    # 初始化聚类器
    clusterer = LogTemplateExtractor()
    
    # 预处理日志
    print("\n预处理日志...")
    processed_logs = clusterer.batch_preprocess_logs(logs)
    print(f"预处理完成，保留 {len(processed_logs)} 条有效日志")
    
    # 显示预处理示例
    print(f"\n预处理示例 (前3条):")
    for i in range(min(3, len(logs))):
        print(f"原始: {logs[i]}")
        if i < len(processed_logs):
            print(f"处理: {processed_logs[i]}")
        print("-" * 60)
    
    # 方法1: LLM模板聚类
    llm_clusters = demonstrate_llm_clustering(clusterer, processed_logs, data_type)
    
    # 方法2: Embedding聚类
    embedding_clusters = demonstrate_embedding_clustering(clusterer, processed_logs, data_type)
    
    # 比较结果
    if llm_clusters or embedding_clusters:
        compare_clustering_results(llm_clusters, embedding_clusters, processed_logs)
    
    print(f"\n演示完成！")
    print(f"建议:")
    print(f"1. 对于需要高质量模板的场景，推荐使用LLM模板聚类")
    print(f"2. 对于大规模日志快速聚类，推荐使用Embedding聚类")
    print(f"3. 可以结合两种方法：先用Embedding粗聚类，再用LLM精细化")

if __name__ == "__main__":
    main()
