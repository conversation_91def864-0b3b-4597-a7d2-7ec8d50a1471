name: algo
channels:
  - defaults
  - conda-forge
dependencies:
  - _py-xgboost-mutex=2.0=cpu_2
  - absl-py=2.1.0=py312hca03da5_0
  - accelerate=1.8.1=pyhe01879c_0
  - adagio=0.2.4=py312hca03da5_0
  - aiohttp-cors=0.7.0=pyhd3eb1b0_0
  - alembic=1.16.2=py312hca03da5_0
  - annotated-types=0.6.0=py312hca03da5_0
  - antlr-python-runtime=4.9.3=pyhd8ed1ab_1
  - antlr4-python3-runtime=4.11.1=py312hca03da5_0
  - aom=3.6.0=h313beb8_0
  - appdirs=1.4.4=pyhd3eb1b0_0
  - arrow=1.3.0=py312hca03da5_0
  - arrow-cpp=19.0.0=h49da57b_3
  - autogluon=1.3.1=pyhd8ed1ab_0
  - autogluon.common=1.3.1=pyhd8ed1ab_0
  - autogluon.core=1.3.1=pyha770c72_0
  - autogluon.features=1.3.1=pyhd8ed1ab_0
  - autogluon.multimodal=1.3.1=pyha770c72_1
  - autogluon.tabular=1.3.1=pyha770c72_0
  - autogluon.timeseries=1.3.1=pyhd8ed1ab_0
  - aws-c-auth=0.8.5=h80987f9_0
  - aws-c-cal=0.8.5=h80987f9_0
  - aws-c-common=0.11.1=h80987f9_0
  - aws-c-compression=0.3.1=h80987f9_0
  - aws-c-event-stream=0.5.4=h313beb8_0
  - aws-c-http=0.9.3=h80987f9_0
  - aws-c-io=0.17.0=h80987f9_0
  - aws-c-mqtt=0.12.2=h80987f9_0
  - aws-c-s3=0.7.11=h80987f9_0
  - aws-c-sdkutils=0.2.3=h80987f9_0
  - aws-checksums=0.2.3=h80987f9_0
  - aws-crt-cpp=0.31.0=h313beb8_0
  - aws-sdk-cpp=1.11.528=hdd7fb2f_0
  - beartype=0.21.0=pyhd8ed1ab_0
  - beautifulsoup4=4.13.4=py312hca03da5_0
  - binaryornot=0.4.4=pyhd3eb1b0_1
  - blas=1.0=openblas
  - boost-cpp=1.82.0=h48ca7d4_2
  - boto3=1.37.10=py312hca03da5_0
  - botocore=1.37.10=py312hca03da5_0
  - bottleneck=1.4.2=py312ha86b861_0
  - brotli-python=1.0.9=py312h313beb8_9
  - brotlicffi=*******=py312h313beb8_1
  - bzip2=1.0.8=h80987f9_6
  - c-ares=1.19.1=h80987f9_0
  - ca-certificates=2025.2.25=hca03da5_0
  - cachetools=5.5.1=py312hca03da5_0
  - cairo=1.16.0=hd886ae0_6
  - catalogue=2.0.10=py312hca03da5_0
  - catboost=1.2.8=py312h167317f_0
  - certifi=2025.7.14=py312hca03da5_0
  - cffi=1.17.1=py312h3eb5a62_1
  - chardet=4.0.0=py312hca03da5_1003
  - click=8.1.8=py312hca03da5_0
  - cloudpathlib=0.21.0=py312hca03da5_0
  - cloudpickle=3.1.1=py312hca03da5_0
  - colorama=0.4.6=py312hca03da5_0
  - colorful=0.5.4=pyhd3eb1b0_0
  - colorlog=5.0.1=py312hca03da5_1
  - confection=0.1.5=py312hd096484_0
  - contourpy=1.3.1=py312h48ca7d4_0
  - cookiecutter=2.6.0=py312hca03da5_1
  - coreforecast=********=py312h4721b07_0
  - cpython=3.12.11=py312hd8ed1ab_0
  - cycler=0.11.0=pyhd3eb1b0_0
  - cymem=2.0.6=py312h313beb8_1
  - cython-blis=1.0.1=py312h80987f9_0
  - datasets=2.14.4=pyhd8ed1ab_0
  - dav1d=1.2.1=h80987f9_0
  - defusedxml=0.7.1=pyhd3eb1b0_0
  - deprecated=1.2.13=py312hca03da5_0
  - dill=0.3.7=py312hca03da5_0
  - distlib=0.3.8=py312hca03da5_0
  - evaluate=0.4.3=py312h6e05fcc_0
  - expat=2.7.1=h313beb8_0
  - fastai=2.8.2=pyhd8ed1ab_0
  - fastcore=1.8.5=pyhe01879c_0
  - fastdownload=0.0.7=py312hca03da5_2
  - fastprogress=1.0.3=pyhd8ed1ab_1
  - fasttransform=0.0.2=pyhd8ed1ab_0
  - filelock=3.17.0=py312hca03da5_0
  - font-ttf-dejavu-sans-mono=2.37=hd3eb1b0_0
  - font-ttf-inconsolata=2.001=hcb22688_0
  - font-ttf-source-code-pro=2.030=hd3eb1b0_0
  - font-ttf-ubuntu=0.83=h8b1ccd4_0
  - fontconfig=2.14.1=h6402c1e_3
  - fonts-anaconda=1=h8fa9717_0
  - fonts-conda-ecosystem=1=hd3eb1b0_0
  - fonttools=4.55.3=py312h80987f9_0
  - freetype=2.13.3=h47d26ad_0
  - fribidi=1.0.10=h1a28f6b_0
  - fs=2.4.16=py312hca03da5_0
  - fsspec=2025.5.1=py312hd096484_0
  - fugue=0.9.1=pyhd8ed1ab_1
  - future=1.0.0=py312hca03da5_0
  - gdk-pixbuf=2.42.10=h969f7ef_2
  - gdown=5.2.0=pyhd8ed1ab_1
  - gettext=0.21.0=hbdbcc25_2
  - gflags=2.2.2=h313beb8_1
  - giflib=5.2.2=h80987f9_0
  - glib=2.84.2=hc880cf1_0
  - glib-tools=2.84.2=hc880cf1_0
  - glog=0.5.0=h313beb8_1
  - gluonts=0.16.0=py312hca03da5_0
  - gmp=6.3.0=h313beb8_0
  - gmpy2=2.2.1=py312h5c1b81f_0
  - google-api-core=2.24.2=py312hca03da5_0
  - google-auth=2.38.0=py312hca03da5_1
  - googleapis-common-protos=1.69.2=py312hca03da5_0
  - graphite2=1.3.14=hc377ac9_1
  - graphviz=2.50.0=hdc6c5f9_2
  - greenlet=3.1.1=py312h313beb8_0
  - grpcio=1.71.0=py312h313beb8_0
  - gts=0.7.6=hde733a8_3
  - harfbuzz=10.2.0=he637ebf_1
  - huggingface_hub=0.31.2=py312hca03da5_0
  - hyperopt=0.2.7=pyhd8ed1ab_1
  - icu=73.1=h313beb8_0
  - imageio=2.37.0=py312hca03da5_0
  - importlib-metadata=8.5.0=py312hca03da5_0
  - jinja2=3.1.6=py312hca03da5_0
  - jmespath=1.0.1=py312hca03da5_0
  - joblib=1.4.2=py312hca03da5_0
  - jpeg=9e=h80987f9_3
  - jsonschema=4.23.0=py312hca03da5_0
  - jsonschema-specifications=2023.7.1=py312hca03da5_0
  - kiwisolver=1.4.8=py312h313beb8_0
  - krb5=1.21.3=h7414a1f_1
  - langcodes=3.3.0=pyhd3eb1b0_0
  - lazy_loader=0.4=py312hca03da5_0
  - lcms2=2.16=he26ebf3_1
  - leptonica=1.82.0=hdedf53e_3
  - lerc=4.0.0=h313beb8_0
  - libabseil=20250127.0=cxx17_h313beb8_0
  - libarchive=3.7.7=h8f13d7a_0
  - libavif=1.1.1=h80987f9_0
  - libboost=1.82.0=h0bc93f9_2
  - libbrotlicommon=1.0.9=h80987f9_9
  - libbrotlidec=1.0.9=h80987f9_9
  - libbrotlienc=1.0.9=h80987f9_9
  - libcurl=8.14.1=hb586df2_0
  - libcxx=17.0.6=he5c5206_4
  - libdeflate=1.22=h80987f9_0
  - libedit=3.1.20230828=h80987f9_0
  - libev=4.33=h1a28f6b_1
  - libevent=2.1.12=h02f6b3c_1
  - libexpat=2.7.1=hec049ff_0
  - libffi=3.4.4=hca03da5_1
  - libgd=2.3.3=hd3b0a14_4
  - libgfortran=5.0.0=11_3_0_hca03da5_28
  - libgfortran5=11.3.0=h009349e_28
  - libglib=2.84.2=hdc2269c_0
  - libgrpc=1.71.0=h62f6fdd_0
  - libiconv=1.16=h80987f9_3
  - libnghttp2=1.57.0=h62f6fdd_0
  - libopenblas=0.3.29=hea593b9_0
  - libpng=1.6.39=h80987f9_0
  - libprotobuf=5.29.3=h14f15fd_1
  - libre2-11=2024.07.02=h313beb8_0
  - librsvg=2.56.3=h38920c8_2
  - libsentencepiece=0.2.0=hc1ab07e_4
  - libsqlite=3.46.0=hfb93653_0
  - libssh2=1.11.1=h3e2b118_0
  - libthrift=0.15.0=h73c2103_2
  - libtiff=4.7.0=h91aec0a_0
  - libtool=2.4.7=h313beb8_0
  - libtorch=2.5.1=gpu_mps_h7b1bc93_204
  - libuv=1.48.0=h80987f9_0
  - libwebp=1.3.2=hf40cdb4_1
  - libwebp-base=1.3.2=h80987f9_1
  - libxgboost=3.0.1=h313beb8_0
  - libxml2=2.13.8=h0b34f26_0
  - libzlib=1.2.13=hfb2fe0b_6
  - lightgbm=4.6.0=py312h313beb8_0
  - lightning=2.3.3=py312hca03da5_0
  - lightning-utilities=0.11.9=py312hca03da5_0
  - llvm-openmp=17.0.6=h2f17746_0
  - llvmlite=0.44.0=py312heb35c27_1
  - lz4-c=1.9.4=h313beb8_1
  - mako=1.2.3=py312hca03da5_0
  - markdown=3.8=py312hca03da5_0
  - markdown-it-py=2.2.0=py312hca03da5_1
  - markupsafe=3.0.2=py312h80987f9_0
  - matplotlib-base=3.10.0=py312h7ef442a_0
  - mdurl=0.1.0=py312hca03da5_0
  - mlforecast=0.13.6=pyhff2d567_0
  - model-index=0.1.11=pyhd8ed1ab_1
  - mpc=1.3.1=h80987f9_0
  - mpfr=4.2.1=h80987f9_0
  - mpmath=1.3.0=py312hca03da5_0
  - msgpack-python=1.1.1=py312h313beb8_0
  - multiprocess=0.70.15=py312hca03da5_0
  - murmurhash=1.0.12=py312h313beb8_0
  - narwhals=1.31.0=py312hca03da5_1
  - ncurses=6.4=h313beb8_0
  - networkx=3.4.2=py312hca03da5_0
  - nlpaug=1.1.11=pyhd8ed1ab_2
  - nltk=3.9.1=py312hca03da5_0
  - nomkl=3.0=0
  - nspr=4.35=h313beb8_0
  - nss=3.89.1=h313beb8_0
  - numba=0.61.2=py312h32e2601_0
  - numexpr=2.11.0=py312h8c72970_0
  - numpy=2.0.0=py312h7f4fdc5_1
  - numpy-base=2.0.0=py312he047099_1
  - omegaconf=2.3.0=pyhd8ed1ab_0
  - opencensus=0.11.3=py312hca03da5_1
  - opencensus-context=0.1.3=py312hca03da5_0
  - openjpeg=2.5.2=hba36e21_1
  - openmim=0.3.7=pyhd8ed1ab_1
  - openssl=3.5.1=h81ee809_0
  - opentelemetry-api=1.30.0=py312hca03da5_0
  - optuna=4.4.0=pyhd8ed1ab_0
  - orc=2.1.1=h55d209b_0
  - ordered-set=4.1.0=py312hca03da5_1
  - orjson=3.10.14=py312h2aea54e_0
  - packaging=24.2=py312hca03da5_0
  - pandas=2.2.3=py312hcf29cfe_0
  - pango=1.50.7=hf2328af_2
  - patsy=1.0.1=py312hca03da5_0
  - pcre2=10.42=hb066dcc_1
  - pdf2image=1.17.0=py312hca03da5_0
  - pillow=11.3.0=py312h6dd570d_0
  - pip=25.1=pyhc872135_2
  - pixman=0.40.0=h1a28f6b_0
  - platformdirs=4.3.7=py312hca03da5_0
  - plotly=6.0.1=py312hd096484_0
  - plum-dispatch=2.5.7=pyhd8ed1ab_0
  - poppler=24.09.0=hba2596d_3
  - poppler-data=0.4.11=hca03da5_1
  - preshed=3.0.9=py312h313beb8_0
  - prometheus_client=0.21.1=py312hca03da5_0
  - proto-plus=1.26.1=py312hca03da5_0
  - protobuf=5.29.3=py312h514c7bf_0
  - psutil=5.9.0=py312h80987f9_1
  - py-spy=0.3.14=h2aea54e_1
  - py-xgboost=3.0.1=py312hca03da5_0
  - py4j=********=py312hca03da5_0
  - pyarrow=19.0.0=py312h313beb8_1
  - pyasn1=0.6.1=py312hca03da5_0
  - pyasn1-modules=0.4.2=py312hca03da5_0
  - pydantic=2.11.7=py312hca03da5_0
  - pydantic-core=2.33.2=py312h5e3a92f_0
  - pygments=2.19.1=py312hca03da5_0
  - pyopenssl=25.0.0=py312h9e2d7d8_0
  - pyparsing=3.2.0=py312hca03da5_0
  - pysocks=1.7.1=py312hca03da5_0
  - pytesseract=0.3.10=py312hca03da5_1
  - python=3.12.2=hdf0ec26_0_cpython
  - python-dateutil=2.9.0post0=py312hca03da5_2
  - python-graphviz=0.20.1=py312hca03da5_1
  - python-slugify=5.0.2=pyhd3eb1b0_0
  - python-tzdata=2025.2=pyhd3eb1b0_0
  - python-xxhash=3.5.0=py312h80987f9_0
  - python_abi=3.12=7_cp312
  - pytorch=2.5.1=gpu_mps_py312hbd1d365_204
  - pytorch-lightning=2.3.3=pyhd8ed1ab_0
  - pytorch-metric-learning=2.8.1=pyh101cb37_1
  - pytz=2025.2=py312hca03da5_0
  - pyyaml=6.0.2=py312h80987f9_0
  - ray-core=2.37.0=py312hba06682_0
  - ray-default=2.37.0=py312hca03da5_0
  - ray-tune=2.37.0=py312hca03da5_0
  - re2=2024.07.02=h48ca7d4_0
  - readline=8.2=h1a28f6b_0
  - referencing=0.30.2=py312hca03da5_0
  - regex=2024.11.6=py312h80987f9_0
  - requests=2.32.4=py312hca03da5_0
  - rich=13.9.4=py312hca03da5_0
  - rpds-py=0.22.3=py312h2aea54e_0
  - rsa=4.7.2=pyhd3eb1b0_1
  - s3transfer=0.11.2=py312hca03da5_0
  - safetensors=0.5.3=py312h7805bc0_0
  - scikit-image=0.25.2=py312h6e33161_0
  - scikit-learn=1.6.1=py312h313beb8_0
  - scipy=1.15.3=py312hb81b3df_0
  - seaborn=0.13.2=py312hca03da5_3
  - sentencepiece=0.2.0=hca03da5_4
  - sentencepiece-python=0.2.0=py312h48ca7d4_4
  - sentencepiece-spm=0.2.0=h48ca7d4_4
  - seqeval=1.2.2=pyhd3deb0d_0
  - setproctitle=1.2.2=py312h80987f9_2
  - setuptools=78.1.1=py312hca03da5_0
  - shellingham=1.5.0=py312hca03da5_0
  - six=1.17.0=py312hca03da5_0
  - sleef=3.5.1=h80987f9_2
  - smart_open=5.2.1=py312hca03da5_0
  - snappy=1.2.1=h313beb8_0
  - soupsieve=2.5=py312hca03da5_0
  - spacy=3.8.2=py312h48ca7d4_0
  - spacy-legacy=3.0.12=py312hca03da5_0
  - spacy-loggers=1.0.4=py312hca03da5_0
  - sqlalchemy=2.0.41=py312hbe2cdee_0
  - sqlite=3.50.2=h79febb2_1
  - srsly=2.5.1=py312h313beb8_0
  - statsforecast=1.7.8=py312h6142ec9_0
  - statsmodels=0.14.4=py312h80987f9_0
  - sympy=1.14.0=pyh2585a3b_105
  - tabulate=0.9.0=py312hca03da5_0
  - tbb=2021.8.0=h48ca7d4_0
  - tensorboard=2.19.0=py312hca03da5_0
  - tensorboard-data-server=0.7.0=py312ha6e5c4f_1
  - tensorboardx=2.2=pyhd3eb1b0_0
  - tesseract=5.2.0=h313beb8_2
  - text-unidecode=1.3=pyhd3eb1b0_0
  - thinc=8.3.2=py312h48ca7d4_1
  - threadpoolctl=3.5.0=py312h989b03a_0
  - tifffile=2025.2.18=py312hca03da5_0
  - timm=0.9.16=pyhd8ed1ab_0
  - tk=8.6.14=h6ba3021_1
  - tokenizers=0.21.0=py312he2d9c3e_0
  - toolz=0.12.0=py312hca03da5_0
  - torchmetrics=1.2.1=py312hca03da5_0
  - torchvision=0.20.1=mps_py312h620d032_102
  - tqdm=4.67.1=py312h989b03a_0
  - transformers=4.49.0=py312hca03da5_0
  - triad=0.9.8=py312hca03da5_0
  - typer=0.9.0=py312hca03da5_0
  - typing-inspection=0.4.0=py312hca03da5_0
  - typing_extensions=4.12.2=py312hca03da5_0
  - tzdata=2025b=h04d1e81_0
  - unicodedata2=15.1.0=py312h80987f9_1
  - unidecode=1.3.8=py312hca03da5_0
  - urllib3=2.5.0=py312hca03da5_0
  - utf8proc=2.6.1=h80987f9_1
  - utilsforecast=0.2.10=pyhff2d567_0
  - virtualenv=20.28.0=py312hca03da5_0
  - wasabi=0.9.1=py312hca03da5_0
  - weasel=0.4.1=py312hca03da5_0
  - werkzeug=3.1.3=py312hca03da5_0
  - wheel=0.45.1=py312hca03da5_0
  - window-ops=0.0.15=pyhd8ed1ab_1
  - wrapt=1.17.0=py312h80987f9_0
  - xgboost=3.0.1=py312hca03da5_0
  - xxhash=0.8.0=h1a28f6b_3
  - xz=5.6.4=h80987f9_1
  - yaml=0.2.5=h1a28f6b_0
  - zipp=3.21.0=py312hca03da5_0
  - zlib=1.2.13=hfb2fe0b_6
  - zstd=1.5.6=hfb09047_0
  - pip:
      - aiohappyeyeballs==2.6.1
      - aiohttp==3.12.14
      - aiosignal==1.4.0
      - attrs==25.3.0
      - charset-normalizer==3.4.2
      - cryptography==45.0.5
      - dashscope==1.23.8
      - frozenlist==1.7.0
      - idna==3.10
      - multidict==6.6.3
      - propcache==0.3.2
      - pycparser==2.22
      - pyodps==0.12.4
      - typing-extensions==4.14.1
      - websocket-client==1.8.0
      - yarl==1.20.1
prefix: /opt/homebrew/Caskroom/mambaforge/base/envs/algo
