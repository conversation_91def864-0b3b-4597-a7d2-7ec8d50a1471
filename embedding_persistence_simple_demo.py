#!/usr/bin/env python3
"""
简化的Embedding持久化演示

不依赖外部API，展示embedding缓存的核心概念
"""

import pandas as pd
import numpy as np
import os
import re
from collections import defaultdict

class SimpleEmbeddingClusterer:
    """简化的embedding聚类器，用于演示持久化概念"""
    
    def __init__(self):
        self.embedding_dim = 128  # 简化的embedding维度
    
    def preprocess_log(self, log_text):
        """简单的日志预处理"""
        if not log_text:
            return ""
        
        # 去除时间戳
        cleaned = re.sub(r"\d{4}-\d{2}-\d{2}[T\s]\d{2}:\d{2}:\d{2}(?:\.\d{3})?", "", log_text)
        
        # 替换IP地址
        cleaned = re.sub(r"\b(?:\d{1,3}\.){3}\d{1,3}\b", "<IP>", cleaned)
        
        # 替换数字
        cleaned = re.sub(r"\b\d{4,}\b", "<NUM>", cleaned)
        
        # 清理空格
        cleaned = re.sub(r"\s+", " ", cleaned).strip()
        
        return cleaned
    
    def get_mock_embedding(self, text):
        """生成模拟embedding（基于文本哈希）"""
        # 使用文本哈希生成确定性的embedding
        hash_value = hash(text)
        np.random.seed(abs(hash_value) % (2**32))
        embedding = np.random.rand(self.embedding_dim)
        return embedding
    
    def get_embeddings_for_logs(self, processed_logs, df=None, save_path=None):
        """获取日志embedding（支持缓存）"""
        print("开始计算日志embedding...")
        
        # 去重
        unique_logs = list(set(processed_logs))
        print(f"总日志数: {len(processed_logs)}, 唯一日志数: {len(unique_logs)}")
        
        # 检查缓存
        embedding_cache = {}
        if df is not None and 'embedding' in df.columns:
            print("检测到已有embedding缓存，正在加载...")
            cached_count = 0
            
            for idx, row in df.iterrows():
                if pd.notna(row.get('embedding')) and row.get('processed_content') in unique_logs:
                    try:
                        # 解析embedding字符串
                        if isinstance(row['embedding'], str):
                            # 移除数组格式字符
                            embedding_str = row['embedding'].strip('[]').replace('\n', '').replace(' ', '')
                            embedding = np.fromstring(embedding_str, sep=',')
                        else:
                            embedding = np.array(row['embedding'])
                        
                        if len(embedding) == self.embedding_dim:
                            embedding_cache[row['processed_content']] = embedding
                            cached_count += 1
                    except Exception as e:
                        print(f"解析embedding失败: {e}")
                        continue
            
            print(f"从缓存加载了 {cached_count} 个embedding")
        
        # 计算缺失的embedding
        embeddings = []
        new_embeddings = {}
        
        for log in unique_logs:
            if log in embedding_cache:
                embeddings.append(embedding_cache[log])
                print(f"使用缓存: {log[:50]}...")
            else:
                embedding = self.get_mock_embedding(log)
                embeddings.append(embedding)
                new_embeddings[log] = embedding
                print(f"新计算: {log[:50]}...")
        
        print(f"新计算了 {len(new_embeddings)} 个embedding")
        
        # 保存新的embedding
        if df is not None and save_path is not None and new_embeddings:
            print("保存新的embedding到DataFrame...")
            self._save_embeddings_to_df(df, new_embeddings, save_path)
        
        embeddings_matrix = np.array(embeddings)
        log_to_embedding_idx = {log: idx for idx, log in enumerate(unique_logs)}
        
        print(f"Embedding计算完成，维度: {embeddings_matrix.shape}")
        return embeddings_matrix, unique_logs, log_to_embedding_idx
    
    def _save_embeddings_to_df(self, df, new_embeddings, save_path):
        """保存embedding到DataFrame"""
        try:
            # 确保有embedding列
            if 'embedding' not in df.columns:
                df['embedding'] = None
            
            # 更新embedding
            updated_count = 0
            for idx, row in df.iterrows():
                processed_content = row.get('processed_content')
                if processed_content in new_embeddings:
                    # 转换为字符串格式
                    embedding_str = np.array2string(
                        new_embeddings[processed_content], 
                        separator=',', 
                        max_line_width=np.inf
                    )
                    df.at[idx, 'embedding'] = embedding_str
                    updated_count += 1
            
            print(f"更新了 {updated_count} 行的embedding")
            
            # 保存文件
            df.to_parquet(save_path, engine="pyarrow", index=False)
            print(f"DataFrame已保存到: {save_path}")
            
        except Exception as e:
            print(f"保存embedding失败: {e}")
    
    def simple_clustering(self, embeddings_matrix, unique_logs, threshold=0.8):
        """简单的基于相似度的聚类"""
        from sklearn.metrics.pairwise import cosine_similarity
        
        print(f"开始聚类，相似度阈值: {threshold}")
        
        # 计算相似度矩阵
        similarity_matrix = cosine_similarity(embeddings_matrix)
        
        # 简单聚类算法
        clusters = {}
        visited = set()
        cluster_id = 0
        
        for i in range(len(unique_logs)):
            if i in visited:
                continue
            
            # 找到相似的日志
            similar_indices = []
            for j in range(len(unique_logs)):
                if similarity_matrix[i][j] >= threshold:
                    similar_indices.append(j)
                    visited.add(j)
            
            if similar_indices:
                clusters[f"cluster_{cluster_id}"] = similar_indices
                cluster_id += 1
        
        print(f"聚类完成，共 {len(clusters)} 个聚类")
        return clusters

def demo_embedding_persistence():
    """演示embedding持久化"""
    print("Embedding持久化演示")
    print("="*60)
    
    # 创建示例数据
    sample_data = {
        'content': [
            "2025-01-15 10:30:25 INFO User admin login successful from *************",
            "2025-01-15 10:30:26 INFO User manager login successful from *************", 
            "2025-01-15 10:30:27 INFO User guest login successful from ********",
            "2025-01-15 10:30:28 ERROR User invalid login failed from *************",
            "2025-01-15 10:30:29 ERROR User timeout login failed from *********",
            "2025-01-15 10:31:01 INFO Database connection established to mysql://localhost:3306",
            "2025-01-15 10:31:02 INFO Database connection established to mysql://db.example.com:3306",
            "2025-01-15 10:31:03 ERROR Database connection failed to mysql://localhost:3306",
        ]
    }
    
    df = pd.DataFrame(sample_data)
    save_path = "demo_logs_with_embeddings.parquet"
    
    print(f"创建了 {len(df)} 条示例日志")
    
    # 初始化聚类器
    clusterer = SimpleEmbeddingClusterer()
    
    # 预处理日志
    print("\n预处理日志...")
    processed_logs = []
    for log in df['content']:
        processed = clusterer.preprocess_log(log)
        processed_logs.append(processed)
    
    df['processed_content'] = processed_logs
    
    print("预处理结果:")
    for i, (original, processed) in enumerate(zip(df['content'], processed_logs)):
        print(f"{i+1}. 原始: {original}")
        print(f"   处理: {processed}")
        print()
    
    # 第一次运行：计算embedding
    print("="*60)
    print("第一次运行：计算所有embedding")
    print("="*60)
    
    embeddings_matrix, unique_logs, log_to_idx = clusterer.get_embeddings_for_logs(
        processed_logs, df=df, save_path=save_path
    )
    
    # 进行聚类
    clusters = clusterer.simple_clustering(embeddings_matrix, unique_logs)
    
    print(f"\n第一次聚类结果:")
    for cluster_id, indices in clusters.items():
        print(f"{cluster_id}: {len(indices)} 条日志")
        for idx in indices[:2]:  # 显示前2个
            log = unique_logs[idx]
            print(f"  - {log}")
        if len(indices) > 2:
            print(f"  ... 还有 {len(indices) - 2} 条")
        print()
    
    # 第二次运行：从缓存加载
    print("="*60)
    print("第二次运行：从缓存加载embedding")
    print("="*60)
    
    if os.path.exists(save_path):
        # 重新加载DataFrame
        df_loaded = pd.read_parquet(save_path, engine="pyarrow")
        print(f"重新加载DataFrame，包含 {len(df_loaded)} 条记录")
        
        # 再次计算embedding（应该全部从缓存加载）
        embeddings_matrix_2, unique_logs_2, log_to_idx_2 = clusterer.get_embeddings_for_logs(
            df_loaded['processed_content'].tolist(), df=df_loaded, save_path=save_path
        )
        
        print("第二次运行完成，所有embedding都从缓存加载！")
    
    # 第三次运行：添加新数据
    print("="*60)
    print("第三次运行：添加新数据")
    print("="*60)
    
    if os.path.exists(save_path):
        df_loaded = pd.read_parquet(save_path, engine="pyarrow")
        
        # 添加新日志
        new_logs = [
            "2025-01-15 10:32:01 INFO System startup completed in 5.2 seconds",
            "2025-01-15 10:33:01 WARN Memory usage: 85% of 16GB available",
        ]
        
        print(f"添加 {len(new_logs)} 条新日志:")
        for log in new_logs:
            print(f"  - {log}")
        
        # 预处理新日志
        new_processed = []
        for log in new_logs:
            processed = clusterer.preprocess_log(log)
            new_processed.append(processed)
        
        # 创建新行
        new_rows = []
        for content, processed in zip(new_logs, new_processed):
            new_rows.append({
                'content': content,
                'processed_content': processed,
                'embedding': None
            })
        
        # 合并数据
        df_combined = pd.concat([df_loaded, pd.DataFrame(new_rows)], ignore_index=True)
        print(f"\n合并后共 {len(df_combined)} 条日志")
        
        # 计算embedding（只会为新数据计算）
        embeddings_matrix_3, unique_logs_3, log_to_idx_3 = clusterer.get_embeddings_for_logs(
            df_combined['processed_content'].tolist(), df=df_combined, save_path=save_path
        )
        
        # 最终聚类
        clusters_3 = clusterer.simple_clustering(embeddings_matrix_3, unique_logs_3)
        
        print(f"\n最终聚类结果:")
        for cluster_id, indices in clusters_3.items():
            print(f"{cluster_id}: {len(indices)} 条日志")
            for idx in indices[:2]:
                log = unique_logs_3[idx]
                print(f"  - {log}")
            if len(indices) > 2:
                print(f"  ... 还有 {len(indices) - 2} 条")
            print()
    
    print("="*60)
    print("演示完成！")
    print("\n关键特性验证:")
    print("✅ 第一次运行：计算所有embedding")
    print("✅ 第二次运行：从缓存加载所有embedding")
    print("✅ 第三次运行：只为新数据计算embedding")
    print("✅ 自动保存和加载embedding")
    print("✅ 支持增量更新")

if __name__ == "__main__":
    demo_embedding_persistence()
