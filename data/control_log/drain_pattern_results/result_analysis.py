# %%

import pandas as pd
import numpy as np

import os
import sys

# Get the project root directory (algorithm folder)
BASE_PATH = os.path.abspath(os.path.join(__file__, "../../../.."))

if BASE_PATH not in sys.path:
    sys.path.append(BASE_PATH)


data = pd.read_parquet(os.path.join(BASE_PATH, "data/control_log/drain_pattern_results/ecs_regionmaster_info_log_with_pattern_ids.parquet"), engine="pyarrow")

# %%
maybe_related = data[data[""]]