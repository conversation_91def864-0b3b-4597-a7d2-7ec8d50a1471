#!/usr/bin/env python3
"""
Embedding持久化演示

展示如何使用embedding缓存功能，避免重复计算：
1. 第一次运行时计算embedding并保存到DataFrame
2. 后续运行时从DataFrame加载已有embedding
3. 只对新的processed_content计算embedding
"""

import sys
import os
import pandas as pd
import numpy as np
import glob

# 添加路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'control_log_diagnose/src2'))

from log_cluster import LogTemplateExtractor

def create_sample_data():
    """创建示例数据"""
    data = {
        'content': [
            "2025-01-15 10:30:25 INFO User admin login successful from *************",
            "2025-01-15 10:30:26 INFO User manager login successful from *************", 
            "2025-01-15 10:30:27 INFO User guest login successful from ********",
            "2025-01-15 10:30:28 ERROR User invalid login failed from *************",
            "2025-01-15 10:30:29 ERROR User timeout login failed from *********",
            "2025-01-15 10:31:01 INFO Database connection established to mysql://localhost:3306/app",
            "2025-01-15 10:31:02 INFO Database connection established to mysql://db.example.com:3306/prod",
            "2025-01-15 10:31:03 ERROR Database connection failed to mysql://localhost:3306/app timeout",
            "2025-01-15 10:31:10 INFO Processing request req_12345 with status success duration 150ms",
            "2025-01-15 10:31:11 INFO Processing request req_67890 with status success duration 200ms",
        ]
    }
    return pd.DataFrame(data)

def demo_embedding_persistence():
    """演示embedding持久化功能"""
    
    print("Embedding持久化演示")
    print("="*60)
    
    # 创建示例数据
    df = create_sample_data()
    save_path = "sample_logs_with_embeddings.parquet"
    
    print(f"创建了 {len(df)} 条示例日志")
    
    # 初始化聚类器
    clusterer = LogTemplateExtractor()
    
    # 预处理日志
    print("\n预处理日志...")
    processed_logs = clusterer.batch_preprocess_logs(df['content'].tolist())
    df['processed_content'] = processed_logs
    
    print(f"预处理完成，共 {len(processed_logs)} 条日志")
    
    # 第一次运行：计算embedding并保存
    print(f"\n{'='*60}")
    print("第一次运行：计算embedding并保存")
    print("="*60)
    
    try:
        # 使用embedding聚类（会计算并保存embedding）
        embedding_clusters = clusterer.cluster_logs_with_embeddings(
            processed_logs, 
            methods=['hierarchical'], 
            df=df, 
            save_path=save_path
        )
        
        print(f"\n第一次聚类完成，结果:")
        for method, clusters in embedding_clusters.items():
            print(f"{method}: {len(clusters)} 个聚类")
            
    except Exception as e:
        print(f"第一次聚类失败: {e}")
        print("可能原因: 未设置DASHSCOPE_API_KEY或网络问题")
        
        # 模拟保存embedding（用于演示）
        print("\n模拟保存embedding数据...")
        df['embedding'] = None
        for i in range(len(df)):
            # 创建模拟embedding
            mock_embedding = np.random.rand(1024)
            embedding_str = np.array2string(mock_embedding, separator=',', max_line_width=np.inf)
            df.at[i, 'embedding'] = embedding_str
        
        df.to_parquet(save_path, engine="pyarrow", index=False)
        print(f"模拟数据已保存到: {save_path}")
    
    # 第二次运行：从缓存加载embedding
    print(f"\n{'='*60}")
    print("第二次运行：从缓存加载embedding")
    print("="*60)
    
    # 重新加载DataFrame
    if os.path.exists(save_path):
        df_loaded = clusterer.load_df_with_embeddings(save_path)
        
        if df_loaded is not None:
            print(f"成功加载DataFrame，包含 {len(df_loaded)} 条记录")
            
            # 再次运行聚类（应该从缓存加载embedding）
            try:
                embedding_clusters_2 = clusterer.cluster_logs_with_embeddings(
                    df_loaded['processed_content'].tolist(), 
                    methods=['hierarchical'], 
                    df=df_loaded, 
                    save_path=save_path
                )
                
                print(f"\n第二次聚类完成，结果:")
                for method, clusters in embedding_clusters_2.items():
                    print(f"{method}: {len(clusters)} 个聚类")
                    
            except Exception as e:
                print(f"第二次聚类失败: {e}")
    
    # 第三次运行：添加新数据
    print(f"\n{'='*60}")
    print("第三次运行：添加新数据")
    print("="*60)
    
    if os.path.exists(save_path):
        df_loaded = clusterer.load_df_with_embeddings(save_path)
        
        if df_loaded is not None:
            # 添加新的日志数据
            new_logs = [
                "2025-01-15 10:32:01 INFO System startup completed in 5.2 seconds",
                "2025-01-15 10:33:01 WARN Memory usage: 85% of 16GB available",
            ]
            
            print(f"添加 {len(new_logs)} 条新日志")
            
            # 预处理新日志
            new_processed = clusterer.batch_preprocess_logs(new_logs)
            
            # 创建新的DataFrame行
            new_rows = []
            for i, (content, processed) in enumerate(zip(new_logs, new_processed)):
                new_rows.append({
                    'content': content,
                    'processed_content': processed,
                    'embedding': None  # 新数据没有embedding
                })
            
            # 合并数据
            new_df = pd.concat([df_loaded, pd.DataFrame(new_rows)], ignore_index=True)
            
            print(f"合并后共 {len(new_df)} 条日志")
            
            # 运行聚类（只会为新数据计算embedding）
            try:
                embedding_clusters_3 = clusterer.cluster_logs_with_embeddings(
                    new_df['processed_content'].tolist(), 
                    methods=['hierarchical'], 
                    df=new_df, 
                    save_path=save_path
                )
                
                print(f"\n第三次聚类完成，结果:")
                for method, clusters in embedding_clusters_3.items():
                    print(f"{method}: {len(clusters)} 个聚类")
                    
            except Exception as e:
                print(f"第三次聚类失败: {e}")

def demo_with_real_data():
    """使用真实数据演示"""
    print(f"\n{'='*60}")
    print("真实数据演示")
    print("="*60)
    
    # 查找parquet文件
    data_path = os.path.join(os.path.dirname(__file__), "output", "control_log_diagnose", "processed_data")
    if os.path.exists(data_path):
        pattern = os.path.join(data_path, "*.parquet")
        parquet_files = glob.glob(pattern)
        
        if parquet_files:
            file_path = parquet_files[0]
            print(f"找到真实数据文件: {file_path}")
            
            # 初始化聚类器
            clusterer = LogTemplateExtractor()
            
            # 加载数据
            df = clusterer.load_df_with_embeddings(file_path)
            
            if df is not None:
                print(f"加载了 {len(df)} 条真实日志")
                
                # 如果没有processed_content列，进行预处理
                if 'processed_content' not in df.columns:
                    print("预处理日志...")
                    processed_logs = clusterer.batch_preprocess_logs(df['content'].tolist())
                    df['processed_content'] = processed_logs
                
                # 取样本进行演示
                sample_size = min(50, len(df))
                df_sample = df.head(sample_size).copy()
                
                print(f"使用样本数据: {len(df_sample)} 条日志")
                
                try:
                    # 运行embedding聚类
                    embedding_clusters = clusterer.cluster_logs_with_embeddings(
                        df_sample['processed_content'].tolist(), 
                        methods=['hierarchical'], 
                        df=df_sample, 
                        save_path=file_path  # 保存回原文件
                    )
                    
                    print(f"\n真实数据聚类完成:")
                    for method, clusters in embedding_clusters.items():
                        print(f"{method}: {len(clusters)} 个聚类")
                        
                        # 显示几个聚类示例
                        sorted_clusters = sorted(clusters.items(), key=lambda x: len(x[1]), reverse=True)
                        for i, (cluster_id, indices) in enumerate(sorted_clusters[:3]):
                            print(f"  聚类{i+1} ({cluster_id}): {len(indices)} 条日志")
                            if indices:
                                example_idx = indices[0]
                                if example_idx < len(df_sample):
                                    example_log = df_sample.iloc[example_idx]['content']
                                    print(f"    示例: {example_log[:80]}...")
                        
                except Exception as e:
                    print(f"真实数据聚类失败: {e}")
            
        else:
            print("未找到parquet文件")
    else:
        print("数据目录不存在")

def main():
    """主函数"""
    print("Embedding持久化功能演示")
    print("="*80)
    
    # 检查API密钥
    api_key = os.getenv('DASHSCOPE_API_KEY')
    if not api_key:
        print("⚠️  警告: 未设置DASHSCOPE_API_KEY环境变量")
        print("   将使用模拟数据进行演示")
        print()
    
    # 演示embedding持久化
    demo_embedding_persistence()
    
    # 演示真实数据（如果有的话）
    demo_with_real_data()
    
    print(f"\n{'='*80}")
    print("演示完成！")
    print("\n主要功能:")
    print("1. ✅ 自动检测已有embedding，避免重复计算")
    print("2. ✅ 新数据只计算缺失的embedding")
    print("3. ✅ 自动保存embedding到DataFrame")
    print("4. ✅ 支持增量更新")
    print("\n使用建议:")
    print("- 第一次运行会计算所有embedding（较慢）")
    print("- 后续运行会复用已有embedding（很快）")
    print("- 添加新数据时只计算新的embedding")

if __name__ == "__main__":
    main()
