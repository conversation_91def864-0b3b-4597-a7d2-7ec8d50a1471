# Drain算法模板提取和Pattern ID使用指南

## 概述

本功能实现了使用Drain算法从日志中提取模板，并将每条日志匹配的模板ID（`pattern_id`）写回原始日志表格的新列中，便于后续分析和处理。

## 功能特性

### 🔧 核心功能
1. **Drain算法模板提取**: 自动从大量日志中发现通用模板
2. **模板ID化**: 为每个模板分配唯一的`drain_pattern_X`标识符
3. **原始数据增强**: 在原始日志表格中添加`pattern_id`列
4. **完整映射保存**: 保存模板ID到具体模板内容的映射关系

### 📊 输出文件
1. **数据文件**: `{logstore}_with_pattern_ids.parquet/.csv` - 包含pattern_id列的原始数据
2. **模板映射**: `{logstore}_pattern_mapping.json` - 模板ID到模板内容的映射和统计信息

## 使用方法

### 1. 基本使用

```python
from template_extractor import LogTemplateExtractor

# 初始化提取器
extractor = LogTemplateExtractor()

# 执行Drain模板提取并保存pattern_id
stats = extractor.extract_templates_with_drain_and_save_ids(
    data_path="data/control_log/processed_data",  # 输入数据路径
    output_dir=None,  # 使用默认输出目录
    logstores=["console_log"],  # 处理指定的logstore
    drain_params={
        'depth': 4,
        'sim_th': 0.4, 
        'max_children': 100
    },
    verbose=True,
    enable_preprocessing=True,
    enable_content_replacement=True
)
```

### 2. 快速开始脚本

运行预配置的脚本:
```bash
python run_drain_pattern_extraction.py
```

### 3. 分析结果

使用提供的分析脚本查看结果:
```bash
python analyze_patterns.py
```

## 参数说明

### Drain算法参数
- `depth`: Drain树的深度 (默认: 4)
- `sim_th`: 相似度阈值 (默认: 0.4)
- `max_children`: 每个节点的最大子节点数 (默认: 100)

### 预处理参数
- `enable_preprocessing`: 是否启用日志预处理 (默认: True)
- `enable_content_replacement`: 是否启用内容替换 (默认: True)

## 示例输出

### CSV数据文件示例
```csv
content,log_time,label,timestamp,pattern_id
"QEMU | No machine 'qemu-i-j6c3hy027sbq648valvs' known",1753179057000,0,1753179057000,drain_pattern_1
"Connection failed to 192.168.1.100 on port 22",1753179058000,1,1753179058000,drain_pattern_2
```

### 模板映射文件示例
```json
{
  "logstore": "console_log",
  "drain_params": {
    "depth": 4,
    "sim_th": 0.4,
    "max_children": 100
  },
  "template_count": 2,
  "pattern_mappings": {
    "drain_pattern_1": "QEMU | No machine 'qemu-<*>' known",
    "drain_pattern_2": "Connection failed to <*> on port <*>"
  },
  "statistics": {
    "total_logs": 1000,
    "valid_logs": 995,
    "matched_logs": 990,
    "unmatched_logs": 5,
    "coverage_rate": 0.995
  }
}
```

## 实际应用场景

### 🔍 日志分析
```python
import pandas as pd

# 加载带有pattern_id的数据
df = pd.read_csv("console_log_with_pattern_ids.csv")

# 按模板分组分析
pattern_analysis = df.groupby('pattern_id').agg({
    'content': 'count',
    'label': ['mean', 'std']
}).round(3)

print("各模板的统计信息:")
print(pattern_analysis)
```

### 📈 异常检测
```python
# 识别异常模式
rare_patterns = df['pattern_id'].value_counts()
rare_threshold = 10
rare_patterns = rare_patterns[rare_patterns < rare_threshold]

print("罕见模式 (可能的异常):")
for pattern_id, count in rare_patterns.items():
    print(f"  {pattern_id}: {count} 条日志")
```

### 🎯 特定模板分析
```python
# 分析特定模板的日志
target_pattern = "drain_pattern_5"
target_logs = df[df['pattern_id'] == target_pattern]

print(f"模板 {target_pattern} 的详细分析:")
print(f"  日志数量: {len(target_logs)}")
print(f"  时间范围: {target_logs['timestamp'].min()} - {target_logs['timestamp'].max()}")
print(f"  标签分布: {target_logs['label'].value_counts().to_dict()}")
```

## 技术特性

### ✅ 优势
- **高效处理**: Drain算法适合大规模日志处理
- **无损保存**: 原始数据完整保留，只添加pattern_id列
- **灵活配置**: 支持多种参数调整以适应不同场景
- **完整映射**: 提供详细的模板映射和统计信息

### ⚠️ 注意事项
- 预处理会影响模板提取效果，建议根据日志特点调整
- Drain参数需要根据日志复杂度进行调优
- 大量日志可能需要较长处理时间

## 文件结构

```
data/control_log/drain_pattern_results/
├── console_log_with_pattern_ids.parquet  # 主数据文件(Parquet格式)
├── console_log_with_pattern_ids.csv      # 主数据文件(CSV格式)
└── console_log_pattern_mapping.json     # 模板映射和统计信息
```

## 后续使用建议

1. **模型训练**: 使用`pattern_id`作为分类特征
2. **异常检测**: 基于模板出现频率识别异常
3. **趋势分析**: 分析不同模板随时间的变化
4. **故障诊断**: 根据模板快速定位问题类型

---

*本功能为日志分析和处理提供了强大的模板识别能力，便于后续的数据分析和机器学习应用。*