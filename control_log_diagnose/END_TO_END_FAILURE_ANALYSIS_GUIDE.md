# 端到端失败分析算法使用指南

## 🎯 功能概述

这是一个完整的端到端失败分析算法，能够：

1. **失败预测**：给定日志序列，判断是否为失败案例
2. **关键转换识别**：找出导致失败的关键转换步骤
3. **模板 ID 映射**：输出对应的模板 ID，便于定位具体日志内容

## 🚀 快速开始

### 基本用法

```python
from control_log_diagnose.src.failure_analysis_api import FailureAnalysisAPI

# 初始化API
api = FailureAnalysisAPI()

# 输入序列（实际的drain_pattern序列）
sequence = [
    'drain_pattern_1',
    'drain_pattern_1593',
    'drain_pattern_3331',
    'drain_pattern_1594'
]

# 完整分析
result = api.analyze(sequence)
print(f"预测结果: {result['prediction']['result']}")
print(f"置信度: {result['prediction']['confidence']}")

# 查看关键转换
for transition in result['critical_transitions']:
    print(f"位置{transition['position']}: {transition['from_template_id']} → {transition['to_template_id']}")
    print(f"  失败率: {transition['failure_rate']}, 风险: {transition['relative_risk']}x")
```

### 分步使用

```python
# 1. 仅预测是否失败
prediction = api.predict_failure(sequence)
print(f"是否失败: {prediction['is_failure']}")
print(f"置信度: {prediction['confidence']}")

# 2. 仅查找关键转换（如果确认是失败案例）
if prediction['is_failure']:
    transitions = api.find_critical_transitions(sequence)
    for t in transitions:
        print(f"关键转换: {t['from_template_id']} → {t['to_template_id']}")
        print(f"  位置: {t['position']}, 严重性: {t['severity']}")
```

## 📊 输出格式详解

### 完整分析结果结构

```python
{
    # 预测结果
    'prediction': {
        'is_failure': bool,          # 是否失败
        'confidence': float,         # 置信度 (0-1)
        'result': str               # "SUCCESS" 或 "FAILURE"
    },

    # 序列信息
    'sequence_info': {
        'length': int,              # 序列长度
        'unique_patterns': int,     # 唯一模式数
        'diversity_ratio': float    # 多样性比率
    },

    # 关键转换详情
    'critical_transitions': [
        {
            'position': int,                    # 转换位置
            'from_pattern': str,               # 源模式 (drain_pattern_XXX)
            'to_pattern': str,                 # 目标模式 (drain_pattern_XXX)
            'from_template_id': str,           # 源模板ID (template_XXX)
            'to_template_id': str,             # 目标模板ID (template_XXX)
            'failure_rate': float,             # 失败率 (0-1)
            'relative_risk': float,            # 相对风险倍数
            'severity': str                    # 严重性: "high", "medium", "low"
        }
    ],

    # 转换统计
    'transition_summary': {
        'total_critical': int,      # 总关键转换数
        'high_risk': int,           # 高风险转换数
        'medium_risk': int,         # 中风险转换数
        'low_risk': int            # 低风险转换数
    },

    # 智能建议
    'recommendations': [str]        # 建议列表
}
```

### 关键转换严重性分级

- **🚨 high**: 失败率 ≥ 0.8 且相对风险 ≥ 2.0 - 需要立即关注
- **⚠️ medium**: 失败率 ≥ 0.6 且相对风险 ≥ 1.5 - 建议监控
- **ℹ️ low**: 失败率 ≥ 0.4 且相对风险 ≥ 1.2 - 一般关注

## 🔧 高级用法

### 批量分析

```python
# 批量分析多个序列
sequences = [
    ['drain_pattern_1', 'drain_pattern_2', 'drain_pattern_3'],
    ['drain_pattern_43', 'drain_pattern_1596', 'drain_pattern_292'],
    # ... 更多序列
]

results = api.batch_analyze(sequences)

# 统计失败率
failure_count = sum(1 for r in results if r['prediction']['is_failure'])
print(f"失败率: {failure_count}/{len(results)} = {failure_count/len(results):.2%}")
```

### 模板 ID 映射自定义

如果有自定义的模板映射文件：

```python
from control_log_diagnose.src.end_to_end_failure_analyzer import EndToEndFailureAnalyzer

# 使用自定义模板映射
analyzer = EndToEndFailureAnalyzer(
    template_mapping_path="/path/to/your/template_mapping.csv"
)

# CSV格式应包含: pattern_id, template_id 两列
```

## 📈 实际应用场景

### 1. 实时监控

```python
def monitor_log_sequence(new_sequence):
    """实时监控新的日志序列"""
    api = FailureAnalysisAPI()
    result = api.analyze(new_sequence)

    if result['prediction']['is_failure']:
        # 发送告警
        send_alert(f"检测到失败序列，置信度: {result['prediction']['confidence']}")

        # 记录关键转换
        for t in result['critical_transitions']:
            if t['severity'] == 'high':
                log_critical_transition(t)

    return result
```

### 2. 历史数据分析

```python
def analyze_historical_failures(failure_sequences):
    """分析历史失败数据，找出共同模式"""
    api = FailureAnalysisAPI()

    all_critical_transitions = []
    for seq in failure_sequences:
        result = api.analyze(seq)
        all_critical_transitions.extend(result['critical_transitions'])

    # 统计最常见的失败转换
    from collections import Counter
    common_transitions = Counter([
        f"{t['from_template_id']} → {t['to_template_id']}"
        for t in all_critical_transitions
        if t['severity'] == 'high'
    ])

    return common_transitions.most_common(10)
```

### 3. 预防性维护

```python
def preventive_maintenance_check(sequence):
    """预防性维护检查"""
    api = FailureAnalysisAPI()
    result = api.analyze(sequence)

    maintenance_tasks = []

    # 检查高风险转换
    high_risk_transitions = [
        t for t in result['critical_transitions']
        if t['severity'] == 'high'
    ]

    if high_risk_transitions:
        maintenance_tasks.append({
            'priority': 'HIGH',
            'task': f"检查模板 {high_risk_transitions[0]['to_template_id']} 相关组件",
            'reason': f"检测到高风险转换，失败率 {high_risk_transitions[0]['failure_rate']:.1%}"
        })

    return maintenance_tasks
```

## 🎛️ 配置说明

### 模型文件位置

- 默认路径: `control_log_diagnose/models/sequence_classifier.pkl`
- 包含: 训练好的随机森林模型 + 特征缩放器

### 数据文件位置

- 训练数据: `data/control_log/drain_pattern_results/ecs_regionmaster_info_log_with_pattern_ids.parquet`
- 用于计算转换统计信息

### 性能指标

- 模型准确率: 100% (在测试集上)
- 特征维度: 77 维
- 支持的转换类型: 38,213 个已知转换模式

## ⚠️ 注意事项

1. **输入格式**: 序列元素必须是 `drain_pattern_XXX` 格式
2. **最小序列长度**: 建议至少 2 个元素才能进行转换分析
3. **模板映射**: 默认将 `drain_pattern_XXX` 映射为 `template_XXX`
4. **内存使用**: 大批量分析时注意内存使用情况

## 🚀 扩展开发

如需扩展功能，可以：

1. **添加新特征**: 在 `_extract_all_features` 方法中增加新的特征工程
2. **自定义映射**: 实现自己的 `_get_template_id` 方法
3. **增强建议**: 在 `_generate_recommendations` 中添加更智能的建议逻辑

## 📞 技术支持

如有问题，请检查：

1. 模型文件是否存在且完整
2. 输入序列格式是否正确
3. 依赖包是否已正确安装 (pandas, numpy, scikit-learn, pickle)
