#!/usr/bin/env python3
"""
日志embedding聚类使用示例
"""

import pandas as pd
import os
from log_embedding_clustering import LogEmbeddingClustering, ClusteringConfig

def create_sample_data():
    """创建示例日志数据"""
    sample_logs = [
        "用户登录成功，用户ID: 12345",
        "用户登录失败，密码错误",
        "用户注册成功，邮箱: <EMAIL>", 
        "系统内存使用率达到85%",
        "数据库连接超时",
        "API调用成功，响应时间: 120ms",
        "文件上传完成，大小: 2.5MB",
        "用户登录成功，用户ID: 67890",
        "系统CPU使用率达到90%",
        "数据库查询执行成功",
        "用户注册失败，邮箱已存在",
        "API调用失败，服务器错误",
        "文件下载开始",
        "用户登录失败，用户名不存在",
        "系统磁盘空间不足",
        "数据库连接恢复正常",
        "API调用成功，响应时间: 89ms",
        "文件上传失败，文件过大",
        "用户修改密码成功",
        "系统重启完成"
    ]
    
    df = pd.DataFrame({
        'log_id': range(len(sample_logs)),
        'log_content': sample_logs,
        'timestamp': pd.date_range('2025-01-01', periods=len(sample_logs), freq='H')
    })
    
    return df

def example_basic_usage():
    """基础使用示例"""
    print("=== 基础使用示例 ===")
    
    # 创建示例数据
    df = create_sample_data()
    sample_file = "sample_logs.csv"
    df.to_csv(sample_file, index=False)
    
    # 配置聚类参数
    config = ClusteringConfig(
        api_key=os.getenv("DASHSCOPE_API_KEY"),  # 从环境变量获取API Key
        log_column="log_content",
        clustering_methods=["kmeans", "dbscan"],
        batch_size=20,
        output_dir="basic_clustering_results",
        dimension=512,  # 使用较小的维度以节省成本
        auto_select_clusters=True,
        # 预处理配置
        enable_preprocessing=True,
        preserve_structure=True,
        enable_content_replacement=True,
        preprocess_before_embedding=True
    )
    
    # 创建聚类器
    clusterer = LogEmbeddingClustering(config)
    
    try:
        # 运行完整流程
        results = clusterer.run_full_pipeline(sample_file)
        
        print("\n聚类完成！")
        print("评估结果:")
        print(results["evaluation"])
        
        # 获取最佳聚类方法
        best_method = results["evaluation"].loc[
            results["evaluation"]["silhouette_score"].idxmax(), "method"
        ]
        print(f"\n最佳聚类方法: {best_method}")
        
        # 显示聚类摘要
        summary = clusterer.get_cluster_summary(best_method)
        print("\n聚类摘要:")
        for _, row in summary.iterrows():
            print(f"聚类 {row['cluster_id']}: {row['size']} 条日志 ({row['percentage']:.1f}%)")
            print(f"  示例: {row['sample_logs'][0]}")
        
    except Exception as e:
        print(f"Error: {e}")
        print("请确保设置了正确的DASHSCOPE_API_KEY环境变量")
    
    finally:
        # 清理示例文件
        if os.path.exists(sample_file):
            os.remove(sample_file)

def example_advanced_usage():
    """高级使用示例"""
    print("\n=== 高级使用示例 ===")
    
    # 创建更大的示例数据
    logs = []
    # 登录相关日志
    for i in range(10):
        logs.extend([
            f"用户 user_{i} 登录成功",
            f"用户 user_{i} 登录失败，密码错误",
            f"用户 user_{i} 注册成功"
        ])
    
    # 系统监控日志
    for i in range(10):
        logs.extend([
            f"CPU使用率: {70 + i}%",
            f"内存使用率: {60 + i}%",
            f"磁盘使用率: {50 + i}%"
        ])
    
    # API调用日志
    for i in range(10):
        logs.extend([
            f"API /users 调用成功，响应时间: {100 + i*10}ms",
            f"API /orders 调用失败，错误码: 500",
            f"API /products 调用成功，响应时间: {80 + i*5}ms"
        ])
    
    df = pd.DataFrame({
        'log_id': range(len(logs)),
        'log_content': logs,
        'level': ['INFO'] * len(logs),
        'service': ['web', 'system', 'api'] * (len(logs) // 3)
    })
    
    sample_file = "advanced_logs.csv"
    df.to_csv(sample_file, index=False)
    
    # 高级配置
    config = ClusteringConfig(
        api_key=os.getenv("DASHSCOPE_API_KEY"),
        log_column="log_content",
        clustering_methods=["kmeans", "dbscan", "hierarchical", "gmm"],
        batch_size=30,
        output_dir="advanced_clustering_results",
        dimension=1024,
        auto_select_clusters=True,
        max_clusters=15
    )
    
    clusterer = LogEmbeddingClustering(config)
    
    try:
        # 分步执行
        print("1. 加载数据...")
        clusterer.load_data(sample_file)
        
        print("2. 生成embeddings...")
        embeddings = clusterer.generate_embeddings()
        print(f"生成了 {embeddings.shape} 的embedding矩阵")
        
        print("3. 执行聚类...")
        clustering_results = clusterer.perform_clustering()
        
        print("4. 评估结果...")
        evaluation = clusterer.evaluate_clustering()
        print(evaluation)
        
        print("5. 保存结果...")
        saved_files = clusterer.save_results()
        print(f"保存了 {len(saved_files)} 个文件")
        
        # 对比不同方法
        print("\n不同聚类方法对比:")
        for method, result in clustering_results.items():
            print(f"{method}: {result['n_clusters']} 个聚类")
            if 'silhouette_score' in result:
                print(f"  轮廓系数: {result['silhouette_score']:.3f}")
        
    except Exception as e:
        print(f"Error: {e}")
    
    finally:
        # 清理示例文件
        if os.path.exists(sample_file):
            os.remove(sample_file)

def example_custom_analysis():
    """自定义分析示例"""
    print("\n=== 自定义分析示例 ===")
    
    # 加载已有的聚类结果进行分析
    config = ClusteringConfig(
        output_dir="custom_analysis_results"
    )
    
    clusterer = LogEmbeddingClustering(config)
    
    # 这里展示如何手动分析特定类型的日志
    error_logs = [
        "数据库连接失败",
        "网络超时错误", 
        "文件读取异常",
        "内存溢出错误",
        "权限验证失败"
    ]
    
    success_logs = [
        "订单创建成功",
        "支付处理完成",
        "数据同步成功",
        "备份任务完成",
        "用户验证通过"
    ]
    
    all_logs = error_logs + success_logs
    
    print(f"分析 {len(all_logs)} 条日志...")
    
    # 这个示例需要API Key，如果没有则跳过
    if os.getenv("DASHSCOPE_API_KEY"):
        try:
            embeddings = clusterer.generate_embeddings(all_logs)
            
            # 使用2个聚类（错误vs成功）
            config.clustering_methods = ["kmeans"]
            clusterer.embeddings = embeddings
            
            # 模拟日志数据
            clusterer.log_data = pd.DataFrame({
                'log_content': all_logs,
                'expected_type': ['error'] * len(error_logs) + ['success'] * len(success_logs)
            })
            
            # 手动设置聚类数为2
            from sklearn.cluster import KMeans
            kmeans_result = clusterer._apply_kmeans(embeddings, n_clusters=2)
            
            # 分析聚类准确性
            labels = kmeans_result['labels']
            df_analysis = pd.DataFrame({
                'log': all_logs,
                'expected': ['error'] * len(error_logs) + ['success'] * len(success_logs),
                'cluster': labels
            })
            
            print("\n聚类结果分析:")
            print(df_analysis.groupby(['expected', 'cluster']).size().unstack(fill_value=0))
            
        except Exception as e:
            print(f"需要API Key进行embedding生成: {e}")
    else:
        print("请设置DASHSCOPE_API_KEY环境变量以运行此示例")

def example_preprocessing_comparison():
    """预处理效果对比示例"""
    print("\n=== 预处理效果对比示例 ===")
    
    # 创建包含各种复杂日志的示例
    complex_logs = [
        "2025-01-15 10:30:45 [INFO] [pid=5580,iso=0] User login successful: user_id=12345, ip=*************, session=abc123def456",
        "2025-01-15T10:31:20.123456Z [ERROR] Database connection timeout to mysql://************:3306/app_db after 30000ms",
        "Mon Jan 15 10:32:00 CST 2025 [WARN] VM instance i-2ze9c4ik6b5vx3zqy3jq CPU usage: 85%, memory: 2048MB",
        "2025-01-15 10:33:15 [DEBUG] API call to http://api.example.com:8080/v1/users?id=123&action=update succeeded in 120ms",
        "2025-01-15 10:34:30 [FATAL] Exception at 0x7fff12345678: NullPointerException in /opt/app/src/main/java/Service.java:142"
    ]
    
    # 配置不同的预处理选项
    configs = [
        ("无预处理", ClusteringConfig(enable_preprocessing=False)),
        ("基础预处理", ClusteringConfig(
            enable_preprocessing=True,
            preserve_structure=True,
            enable_content_replacement=False
        )),
        ("完整预处理", ClusteringConfig(
            enable_preprocessing=True,
            preserve_structure=True,
            enable_content_replacement=True
        )),
        ("简化预处理", ClusteringConfig(
            enable_preprocessing=True,
            preserve_structure=False,
            enable_content_replacement=True
        ))
    ]
    
    print("原始日志样例:")
    for i, log in enumerate(complex_logs, 1):
        print(f"{i}. {log}")
    
    print("\n预处理结果对比:")
    
    for config_name, config in configs:
        print(f"\n{config_name}:")
        print("-" * (len(config_name) + 1))
        
        try:
            # 创建聚类器（不需要API Key用于预处理演示）
            clusterer = LogEmbeddingClustering(config)
            
            if config.enable_preprocessing and clusterer.preprocessor is not None:
                processed_logs = []
                for log in complex_logs:
                    processed = clusterer.preprocessor.preprocess_log(
                        log,
                        preserve_structure=config.preserve_structure,
                        enable_content_replacement=config.enable_content_replacement
                    )
                    processed_logs.append(processed)
                
                for i, (original, processed) in enumerate(zip(complex_logs, processed_logs), 1):
                    print(f"{i}. {processed}")
            else:
                for i, log in enumerate(complex_logs, 1):
                    print(f"{i}. {log}")
                    
        except Exception as e:
            print(f"配置 {config_name} 处理失败: {e}")
    
    print("\n预处理优势:")
    print("1. 标准化时间戳格式，移除具体时间值")
    print("2. 统一IP地址、端口、UUID等变量为占位符")
    print("3. 简化日志结构，提取核心语义信息")
    print("4. 提高聚类质量，减少噪声干扰")
    print("5. 降低embedding计算成本")

if __name__ == "__main__":
    print("日志Embedding聚类使用示例")
    print("=" * 50)
    
    # 检查API Key
    if not os.getenv("DASHSCOPE_API_KEY"):
        print("警告: 未检测到DASHSCOPE_API_KEY环境变量")
        print("请设置: export DASHSCOPE_API_KEY='your-api-key'")
        print("或在代码中直接设置config.api_key")
        print()
    
    # 运行示例
    example_preprocessing_comparison()  # 首先展示预处理效果
    example_basic_usage()
    example_advanced_usage() 
    example_custom_analysis()
    
    print("\n示例运行完成！")
    print("\n主要功能:")
    print("1. 支持CSV和Parquet格式输入")
    print("2. 多种聚类算法: KMeans, DBSCAN, 层次聚类, OPTICS, 高斯混合模型")
    print("3. 自动参数调优和聚类数选择")
    print("4. 完整的评估指标")
    print("5. 可视化结果（t-SNE, PCA, UMAP）")
    print("6. 保存embedding和聚类结果")
    print("7. 聚类摘要和分析报告")