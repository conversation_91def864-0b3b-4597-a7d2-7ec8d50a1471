# 日志字段提取配置文件
# 定义不同logstore的字段提取规则

logstore_configs:
  ecs_regionmaster_info_log:
    content_field: "content"
    time_field: "__time__"
    additional_fields:
      - "request_id"
      - "briefTraceInfo"
      - "level"
      - "file"

  ecs_regionmaster_error_log:
    content_field: "content"
    time_field: "__time__"
    additional_fields:
      - "request_id"
      - "briefTraceInfo"
      - "level"
      - "file"

  schedule_trace:
    content_field: "resultCode"
    time_field: "__time__"
    additional_fields:
      - "requestId"
      - "taskId"

  ecs_pync_log:
    content_field: "content"
    time_field: "__time__"
    additional_fields:
      - "level"
      - "__source__"
      - "request_id"

  libvirt_log:
    content_field: "detail"
    time_field: "__time__"
    additional_fields:
      - "level"
      - "line"

  iohub_pcie_log:
    content_field: "content"
    time_field: "__time__"
    additional_fields: []

  console_log:
    content_field: "content"
    time_field: "__time__"
    additional_fields:
      - "__source__"

  alisyslog_conman_moc:
    content_field: "content"
    time_field: "__time__"
    additional_fields: []

  iohub-server:
    content_field: "content"
    time_field: "__time__"
    additional_fields:
      - "__source__"

  cn_power_op:
    content_field: "content"
    time_field: "__time__"
    additional_fields:
      - "__source__"

# 通用字段提取规则（当特定logstore没有定义时使用）
default_config:
  content_fields: ["content", "message", "detail"]
  time_fields: ["__time__", "timestamp", "time", "logTime", "gmt_create"]
  default_additional_fields: []
