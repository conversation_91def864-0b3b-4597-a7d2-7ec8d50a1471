# 启停诊断 LogStore 配置

logstores:
  # project: "151"
  - logstore: ecs_regionmaster_info_log
    project: "151"
    key_fields:
      - content
    request_id: full

  - logstore: ecs_regionmaster_error_log
    project: "151"
    key_fields:
      - content
    request_id: full

  - logstore: schedule_trace
    project: "151"
    key_fields:
      - resultCode
    request_id: full

  - logstore: ecs_pync_log
    project: "151"
    key_fields:
      - content
      - level
    request_id: none

  # project: ecs-xunjian
  - logstore: libvirt_log
    project: ecs-xunjian
    key_fields:
      - detail
      - level
    request_id: none

  - logstore: iohub_pcie_log
    project: ecs-xunjian
    key_fields:
      - content
    request_id: none

  - logstore: console_log
    project: ecs-xunjian
    key_fields:
      - content
    note: 量巨大，1小时0.7B条日志
    request_id: none

  - logstore: alisyslog_conman_moc
    project: ecs-xunjian
    key_fields:
      - content
      - __source__
    note: 量很大，1小时5000万日志
    request_id: none

  # project: ecs-xunjian2
  - logstore: iohub-server
    project: ecs-xunjian2
    key_fields:
      - content
    request_id: none

  - logstore: cn_power_op
    project: ecs-xunjian2
    key_fields:
      - content
      - __source__
    request_id: none

log_clusters:
  ecs_regionmaster_info_log: ecs_regionmaster_info_log_union_155
  ecs_regionmaster_error_log: ecs_regionmaster_error_log_union_133
  ecs_pync_log: ecs_pync_log_union_136
  libvirt_log: libvirt_log_union_9
  iohub_pcie_log: iohub_pcie_log_union_103
  console_log: console_log_union_169
  alisyslog_conman_moc: alisyslog_conman_moc_union_170
  iohub-server: iohub-server_union_105
  cn_power_op: cn_power_op_union_160
