# %%
import re
import pandas as pd
import numpy as np
import dashscope
from rich.progress import track
from sklearn.cluster import AgglomerativeClustering, KMeans
from sklearn.neighbors import NearestNeighbors
from sklearn.metrics.pairwise import cosine_similarity
from scipy.cluster.hierarchy import dendrogram, linkage, fcluster
from scipy.spatial.distance import pdist, squareform
from collections import defaultdict, Counter
import matplotlib.pyplot as plt
import seaborn as sns

import os
import sys
import gzip
import json
import glob
import time
from dotenv import load_dotenv

BASE_PATH = os.path.abspath(os.path.join(__file__, "../../.."))
if BASE_PATH not in sys.path:
    sys.path.append(BASE_PATH)

data_path = os.path.join(BASE_PATH, "output", "control_log_diagnose", "processed_data")
load_dotenv()

DASHSCOPE_API_KEY = os.getenv("DASHSCOPE_API_KEY")


class LogTemplateExtractor:
    """日志模板提取器"""

    def __init__(self, api_key=DASHSCOPE_API_KEY, model="qwen3-coder-plus"):
        """初始化模板提取器

        Args:
            api_key: DashScope API密钥
            model: 使用的LLM模型
        """
        self.api_key = api_key
        self.model = model
        # 预编译正则表达式以提高性能
        self.preprocess_patterns = self._compile_preprocess_patterns()

    def _compile_preprocess_patterns(self):
        """预编译用于日志预处理的正则表达式"""
        return {
            # 时间戳模式 (多种格式)
            "timestamps": [
                re.compile(r"\d{4}-\d{2}-\d{2}[T\s]\d{2}:\d{2}:\d{2}(?:\.\d{3})?(?:[+-]\d{4}|\.\d+[+-]\d{4}|Z)?"),  # ISO格式
                re.compile(r"\d{4}/\d{2}/\d{2}\s+\d{2}:\d{2}:\d{2}"),  # 斜杠分隔
                re.compile(r"\d{2}/\d{2}/\d{4}\s+\d{2}:\d{2}:\d{2}"),  # MM/dd/yyyy
                re.compile(r"\d{10,13}"),  # Unix时间戳
                re.compile(
                    r"(?:Fri|Sat|Sun|Thu|Mon|Tue|Wed)\s+(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+\d{1,2}\s+\d{2}:\d{2}:\d{2}\s+CST\s+\d{2,4}"
                ),  # CST格式
                re.compile(r"(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+\d{1,2}\s+[0-2]\d(?::[0-5]\d){2}"),  # syslog格式
                re.compile(r"\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3,6}(?:Z|[+-]\d{2}:\d{2})?"),  # 高精度ISO
            ],
            # 方括号内容模式
            "brackets": [
                re.compile(r"\[pid=\d+[^\]]*\]"),  # [pid=5580,iso=0]
                re.compile(r"\[\d{4}/\d{2}/\d{2}\s+\d{2}:\d{2}:\d{2}\]"),  # [2025/07/22 18:10:57]
                re.compile(r"\[\d{10,13}\]"),  # [1753179057331]
                re.compile(r"\[(?:debug|info|warn|error|fatal|trace)\]", re.IGNORECASE),  # [info], [ERROR]等
                re.compile(r"\[[^\]]*\d+[^\]]*\]"),  # 包含数字的其他方括号内容
            ],
            # 日志级别模式 (独立出现)
            "log_levels": re.compile(r"\b(?:DEBUG|INFO|WARN|WARNING|ERROR|FATAL|TRACE)\b:?\s*", re.IGNORECASE),
            # 进程/线程ID
            "process_info": [
                re.compile(r"\bpid[=:]\s*\d+\b", re.IGNORECASE),
                re.compile(r"\btid[=:]\s*\d+\b", re.IGNORECASE),
                re.compile(r"\bthread[=:]\s*\d+\b", re.IGNORECASE),
            ],
            # 其他常见的元信息
            "metadata": [
                re.compile(r"\b\w+\s*=\s*[a-f0-9-]{8,}\b"),  # 类似 requestId=abc123def
                re.compile(r"\b\w+Id\s*=\s*[\w-]+"),  # xxxId=something
            ],
            # === 新增的内容替换模式 ===
            # IP地址模式
            "ip_addresses": [
                re.compile(r"\b(?:\d{1,3}\.){3}\d{1,3}\b"),  # IPv4
                re.compile(r"\b(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}\b"),  # IPv6完整
                re.compile(r"\b(?:[0-9a-fA-F]{1,4}:){1,7}:(?:[0-9a-fA-F]{1,4})?"),  # IPv6简化
            ],
            # 实例ID和资源ID模式
            "instance_ids": [
                re.compile(r"\bi-[a-z0-9]{17,20}\b"),  # ECS实例ID: i-2ze9c4ik6b5vx3zqy3jq (17-20字符)
                re.compile(r"\bINSTANCE_i-[a-z0-9]{17,20}\b"),  # INSTANCE_i-xxx
                re.compile(r"\bslb-[a-z0-9]{17,20}\b"),  # SLB实例ID
                re.compile(r"\brds-[a-z0-9]{17,20}\b"),  # RDS实例ID
                re.compile(r"\bvolume-[a-z0-9]{17,20}\b"),  # 磁盘卷ID
                # 云服务器VM实例ID (多种前缀)
                re.compile(
                    r"(?:(?<=[^A-Za-z0-9-_])|^)(?:i-|hbm-|AY|eci-|cp-|ic-|BVT|bvt|wh|cn|wy|qy|hm|VM|al|uh|ay|tmpvm|ace|vm|houyiecsay)[-]?[a-zA-Z0-9-_]{8,62}(?=(?:[^A-Z0-9])|$)"
                ),
                # 更多VM实例格式
                re.compile(
                    r"(?:qemu-BVT-CTRL-EcsBasic|qemu-iso-i|qemu-i|eci-ops|vnd|acs|iso-i|iso-BVT-DATARS|qemu|eni|i|dh|gds|ri-cr|sg|sgr|hbm|AY|eci|cp|ic|BVT|wh|cn|wy|qy|hm|VM|al|uh|ay|tmpvm|ace|vm|sd|e)-[a-zA-Z0-9]{8,24}|qemu-virT_CheCK_enV_VM|qemu-CNforVM"
                ),
                re.compile(r"(?:vm:|Vm:|VM:|vmName:|performance)[a-zA-Z0-9-]{8,36}"),
                # 测试VM实例
                re.compile(r"(?:BVT|(?:qemu-|fc-|zhj-|jingshu-|ebs)test|qemu-test-net)([a-zA-Z0-9]){0,10}(-[a-zA-Z0-9]{1,20}){1,6}"),
            ],
            # 文件路径模式
            "file_paths": [
                re.compile(r'[a-zA-Z]:\\(?:[^\\/:*?"<>|\r\n]+\\)*[^\\/:*?"<>|\r\n]*'),  # Windows路径
                re.compile(r"/(?:[^/\0\n\r\t]+/)*[^/\0\n\r\t]*"),  # Unix路径
                re.compile(r"\\\\[^\\]+\\[^\\]+(?:\\[^\\]*)*"),  # UNC路径
            ],
            # 端口号模式
            "ports": [
                re.compile(r"\bport\s+(\d{1,5})\b", re.IGNORECASE),
                re.compile(r":(\d{1,5})\b"),  # :8080
            ],
            # UUID和哈希值模式
            "uuids": [
                re.compile(r"\b[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\b"),  # UUID
                re.compile(r"\b[0-9a-fA-F]{32}\b"),  # MD5哈希
                re.compile(r"\b[0-9a-fA-F]{40}\b"),  # SHA1哈希
                re.compile(r"\b[0-9a-fA-F]{64}\b"),  # SHA256哈希
            ],
            # 数字和编号模式
            "numbers": [
                re.compile(r"\b\d{4,}\b"),  # 长数字 (4位以上)
                re.compile(r"\b\d+\.\d+\.\d+(?:\.\d+)*\b"),  # 版本号
                re.compile(r"\b\d+ms\b|\b\d+s\b|\b\d+MB\b|\b\d+KB\b", re.IGNORECASE),  # 数量单位
            ],
            # 会话和认证令牌
            "tokens": [
                re.compile(r"\b[A-Za-z0-9+/]{20,}={0,2}\b"),  # Base64编码
                re.compile(r"\btoken[_=:]\s*[A-Za-z0-9._-]+", re.IGNORECASE),
                re.compile(r"\bsessionid[_=:]\s*[A-Za-z0-9._-]+", re.IGNORECASE),
                re.compile(r"access_?[kK]ey_?[iI]d=(?:[\\dA-Za-z=\'/\\+]{16,128})?(?:\s|,|;|$)"),  # Access Key ID
                re.compile(r"sign=(?:[\\dA-Za-z=\'/\\+]{16,128})?(?:\s|,|;|$)"),  # 签名
            ],
            # 阿里云特定资源标识
            "alibaba_resources": [
                re.compile(r"(?:(?:MOCK-)?ASW-)[A-Z0-9-.]{12,}"),  # ASW资源
                re.compile(r"(?:[^a-zA-Z0-9_-]|^)(?:[a-zA-Z0-9_-]+\.)+(?:alibaba-inc|aliyun-inc|aliyuncs)\.com"),  # 阿里云内网域名
                re.compile(r".+aliyuncs\.com"),  # 阿里云服务域名
                re.compile(r"(?:cn|ap|me|eu|us|rus)(-[A-Z0-9a-z]+)+"),  # 地域信息
                re.compile(r"[0-9a-z]{7,12}\.cloud\.[0-9a-z]{3,6}(?:\.na\d{1,4})?"),  # 主机名
                re.compile(r"(?:(?:CN\d{0,3}\.|)[0-9a-z]{8,12}\.(?:cloud|sqa)|ecs-onebox[0-9]{8,15})\.[0-9a-z]{3,6}(?:\.na\d{1,4})?"),  # CN节点ID
            ],
            # 网络和硬件标识
            "network_hardware": [
                re.compile(r"[\da-fA-F]{2}(?::[\da-fA-F]{2}){5}"),  # MAC地址
                re.compile(r"(?:Ethernet)[0-9]{0,3}"),  # 以太网接口
                re.compile(r"drive-virtio-disk[0-9]{1,3}"),  # 虚拟磁盘设备
                re.compile(r"(?:virt|virtio-disk|net|vcpu|cache|nvme-disk|vf\.|pci\.\d{1,3}-net|pci\.\d{1,3}-(?:virtio|nvme)-disk)\d{1,6}"),  # 虚拟设备ID
                re.compile(r"[0-9a-f]{2,4}(?::[0-9a-f]{2,6}){5,10}"),  # 设备ID
                re.compile(r"[0-9]{4}:[0-9a-f]{2}:[0-9a-f]{2}\.[0-9]"),  # PCI设备ID
            ],
            # 内存地址和指针
            "memory_addresses": [
                re.compile(r"(at |\[<{0,1})*([0-9a-f]{8}`{0,1}[0-9a-f]{8})(>{0,1}\])*"),  # 内存地址
                re.compile(r"\+*0x[0-9a-f]{1,16}(/0x[0-9a-f]{1,16})*"),  # 十六进制地址
                re.compile(r"(?:^|[ \[\(=])0x[A-Fa-f0-9]{1,16}(?:[: ,\]\)]|$)"),  # 十六进制值 (必须有0x前缀)
                # 只匹配明确的十六进制地址格式，避免误匹配普通数字
                re.compile(
                    r"(?:^|[< ,\[\-:])(?:(?!CC|add|fade|be|bad|Add|ADD|Bad|BAD|BE|Be|Fade|FADE)[a-fA-F]{1,}[0-9a-fA-F]{7,19}(?:[ \]\-,.>:]|$)){1,10}"
                ),  # 通用十六进制 (必须包含字母)
            ],
            # Java异常堆栈和反射
            "java_exceptions": [
                re.compile(
                    r"\s(?:at )?[\d\w]{1,50}(?:\.[<>/\w\d$]+){1,10}\((?:(?:[$\w\d]{1,100}\.java(?::\d{1,5})?)|(?:<generated>)|(?:Native Method)|(?:Unknown Source))\)"
                ),  # Java堆栈
                re.compile(r"\t\.\.\. \d{1,5} (?:more|common frames omitted)"),  # 省略的堆栈
                re.compile(r"sun\.reflect\.GeneratedConstructorAccessor[0-9]{0,5}\.newInstance"),  # 反射构造器
                re.compile(r"sun\.reflect\.GeneratedMethodAccessor[0-9]{0,5}\.invoke"),  # 反射方法
                re.compile(r" ~?\[na: ?\d+\.\d+\.\d+(?:_\d+)?\]"),  # jar版本信息
                re.compile(
                    r" ~?\[(?:[a-z_0-9]+\-)+\d+\.\d+\.\d+(?:[\.-][\d-]+)?(?:\-FASTJSON|\-SNAPSHOT|\.RELEASE|\.noneautotype|\-ahas|-bugfix\.?\d*|-stable\.?\d*|(?:\-[a-z_0-9]+)+)?\.jar(?:!/)?:(?:na|\d+\.\d+\.\d+(?:\.RELEASE|-SNAPSHOT)?)\]"
                ),
            ],
            # 容器和集群标识
            "containers": [
                re.compile(r"docker[0-9]{12}_na[0-9]{2}_xdragon_diagnose_center(_[0-9]{4}){2}"),  # Docker容器ID
                re.compile(r"DEFAULT\.[a-f0-9]{12}-[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}"),  # 作业ID
                re.compile(r"re-[a-f0-9]{8}(?:-[a-f0-9]{4}){3}-[a-f0-9]{12}"),  # 发布ID
            ],
            # URL和服务端点
            "urls_endpoints": [
                re.compile(
                    r"(?:http[s]?|dubbo)://(?:\d{1,3}(?:\.\d{1,3}){3}|[\w-]{2,20}(?:\.[\w-]{2,20}){0,4})(?::\d{1,5})?(?:/[\w\-#\.]{1,50}){1,10}"
                ),  # HTTP/Dubbo URL
                re.compile(
                    r'["\'(,]?(?:https?|dubbo)://\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}(?::\d{1,5})?(?:/\w{1,32}){0,10}(?:\.\w{1,50}){0,20}["\',:)]?'
                ),  # IP格式URL
                re.compile(r"(?:dubbo|aliyun-nacos-ns)://.+/.+\?[_a-zA-Z0-9.-]+=[_a-zA-Z0-9-.]+(?:&[_a-zA-Z0-9.-]+=[^=&]{0,})+(?:, | |$)"),  # Dubbo配置URL
                re.compile(r"\?[^&= ]{1,20}=[^& ]{0,100}(?:(?:(?:&[^&= ]{1,30}=[^& ]{0,1000}){1,100})|\])(?:[, ;\.]|$)"),  # URL参数
            ],
        }

    def preprocess_log(self, log_text, preserve_structure=True, enable_content_replacement=True):
        """预处理单条日志

        Args:
            log_text: 原始日志文本
            preserve_structure: 是否保留基本结构（如冒号、等号等）
            enable_content_replacement: 是否启用内容替换（将显著内容替换为占位符）

        Returns:
            str: 预处理后的日志文本
        """
        if not log_text or not isinstance(log_text, str):
            return ""

        # 1. 去除换行符和回车符
        cleaned = log_text.replace("\r\n", " ").replace("\r", " ").replace("\n", " ")

        # 2. 先去除时间戳（避免时间戳被其他模式错误匹配）
        for timestamp_pattern in self.preprocess_patterns["timestamps"]:
            cleaned = timestamp_pattern.sub("", cleaned)

        # 3. 内容替换阶段（在去除时间戳之后进行）
        if enable_content_replacement:
            # 替换IP地址
            for ip_pattern in self.preprocess_patterns["ip_addresses"]:
                cleaned = ip_pattern.sub("<IP>", cleaned)

            # 替换实例ID和资源ID
            for instance_pattern in self.preprocess_patterns["instance_ids"]:
                cleaned = instance_pattern.sub("<INSTANCE_ID>", cleaned)

            # 替换文件路径
            for path_pattern in self.preprocess_patterns["file_paths"]:
                cleaned = path_pattern.sub("<PATH>", cleaned)

            # 替换端口号
            for port_pattern in self.preprocess_patterns["ports"]:
                if "port" in port_pattern.pattern.lower():
                    cleaned = port_pattern.sub(r"port <PORT>", cleaned)
                else:
                    cleaned = port_pattern.sub(r":<PORT>", cleaned)

            # 替换UUID和哈希值
            for uuid_pattern in self.preprocess_patterns["uuids"]:
                cleaned = uuid_pattern.sub("<UUID>", cleaned)

            # 替换数字和编号
            for number_pattern in self.preprocess_patterns["numbers"]:
                if "ms\\b|s\\b|MB\\b|KB\\b" in number_pattern.pattern:
                    # 保留单位，只替换数字
                    cleaned = re.sub(r"\b(\d+)(ms|s|MB|KB)\b", r"<NUM>\2", cleaned, flags=re.IGNORECASE)
                elif r"\d+\.\d+\.\d+" in number_pattern.pattern:
                    cleaned = number_pattern.sub("<VERSION>", cleaned)
                else:
                    cleaned = number_pattern.sub("<NUM>", cleaned)

            # 替换令牌和会话信息
            for token_pattern in self.preprocess_patterns["tokens"]:
                if "token" in token_pattern.pattern.lower():
                    cleaned = token_pattern.sub("token=<TOKEN>", cleaned)
                elif "sessionid" in token_pattern.pattern.lower():
                    cleaned = token_pattern.sub("sessionid=<SESSION>", cleaned)
                elif "access" in token_pattern.pattern.lower():
                    cleaned = token_pattern.sub("access_key_id=<ACCESS_KEY>", cleaned)
                elif "sign" in token_pattern.pattern.lower():
                    cleaned = token_pattern.sub("sign=<SIGN>", cleaned)
                else:
                    cleaned = token_pattern.sub("<TOKEN>", cleaned)

            # 替换阿里云特定资源标识
            for alibaba_pattern in self.preprocess_patterns["alibaba_resources"]:
                if "ASW" in alibaba_pattern.pattern:
                    cleaned = alibaba_pattern.sub("<ASW_RESOURCE>", cleaned)
                elif "aliyuncs" in alibaba_pattern.pattern or "alibaba-inc" in alibaba_pattern.pattern:
                    cleaned = alibaba_pattern.sub("<ALIBABA_DOMAIN>", cleaned)
                elif "cloud" in alibaba_pattern.pattern:
                    cleaned = alibaba_pattern.sub("<CLOUD_HOST>", cleaned)
                else:
                    cleaned = alibaba_pattern.sub("<REGION>", cleaned)

            # 替换网络和硬件标识
            for network_pattern in self.preprocess_patterns["network_hardware"]:
                if "da-fA-F" in network_pattern.pattern and ":" in network_pattern.pattern:
                    cleaned = network_pattern.sub("<MAC>", cleaned)
                elif "Ethernet" in network_pattern.pattern:
                    cleaned = network_pattern.sub("<ETHERNET>", cleaned)
                elif "drive-virtio" in network_pattern.pattern:
                    cleaned = network_pattern.sub("<VDISK>", cleaned)
                elif "pci" in network_pattern.pattern or "virtio" in network_pattern.pattern:
                    cleaned = network_pattern.sub("<DEVICE>", cleaned)
                else:
                    cleaned = network_pattern.sub("<DEVICE_ID>", cleaned)

            # 替换内存地址和指针
            for addr_pattern in self.preprocess_patterns["memory_addresses"]:
                cleaned = addr_pattern.sub("<ADDR>", cleaned)

            # 替换Java异常堆栈
            for java_pattern in self.preprocess_patterns["java_exceptions"]:
                if "at " in java_pattern.pattern:
                    cleaned = java_pattern.sub("\tat <JAVA_STACK>", cleaned)
                elif "..." in java_pattern.pattern:
                    cleaned = java_pattern.sub("\t... <MORE_FRAMES>", cleaned)
                elif "reflect" in java_pattern.pattern:
                    cleaned = java_pattern.sub("<REFLECTION>", cleaned)
                else:
                    cleaned = java_pattern.sub("<JAR_INFO>", cleaned)

            # 替换容器和集群标识
            for container_pattern in self.preprocess_patterns["containers"]:
                if "docker" in container_pattern.pattern:
                    cleaned = container_pattern.sub("<DOCKER_ID>", cleaned)
                elif "DEFAULT" in container_pattern.pattern:
                    cleaned = container_pattern.sub("<JOB_ID>", cleaned)
                else:
                    cleaned = container_pattern.sub("<RELEASE_ID>", cleaned)

            # 替换URL和服务端点
            for url_pattern in self.preprocess_patterns["urls_endpoints"]:
                if "dubbo" in url_pattern.pattern:
                    cleaned = url_pattern.sub("<DUBBO_URL>", cleaned)
                elif "?" in url_pattern.pattern and "&" in url_pattern.pattern:
                    cleaned = url_pattern.sub("<URL_PARAMS>", cleaned)
                else:
                    cleaned = url_pattern.sub("<URL>", cleaned)

        # 4. 去除方括号内容
        for bracket_pattern in self.preprocess_patterns["brackets"]:
            cleaned = bracket_pattern.sub("", cleaned)

        # 5. 去除日志级别
        cleaned = self.preprocess_patterns["log_levels"].sub("", cleaned)

        # 6. 去除进程/线程信息
        for process_pattern in self.preprocess_patterns["process_info"]:
            cleaned = process_pattern.sub("", cleaned)

        # 7. 去除其他元信息（可选）
        if not preserve_structure:
            for metadata_pattern in self.preprocess_patterns["metadata"]:
                cleaned = metadata_pattern.sub("", cleaned)

        # 8. 清理多余空格
        cleaned = re.sub(r"\s+", " ", cleaned).strip()

        # 9. 去除开头和结尾的特殊字符
        cleaned = cleaned.strip(" .,;:-_=")

        # 10. 最终清理连续的占位符
        cleaned = re.sub(r"(<[^>]+>)\s+\1", r"\1", cleaned)  # 去除重复的相同占位符

        return cleaned

    def batch_preprocess_logs(self, logs, show_examples=True, enable_content_replacement=True):
        """批量预处理日志

        Args:
            logs: 日志列表
            show_examples: 是否显示预处理示例
            enable_content_replacement: 是否启用内容替换

        Returns:
            list: 预处理后的日志列表
        """
        if not logs:
            return []

        preprocessed_logs = []
        skipped_count = 0

        for log in logs:
            processed = self.preprocess_log(log, enable_content_replacement=enable_content_replacement)

            if processed:  # 只保留非空的处理结果
                preprocessed_logs.append(processed)

                # 显示前几个预处理示例
                if show_examples and len(preprocessed_logs) <= 3:
                    print(f"预处理示例 {len(preprocessed_logs)}:")
                    print(f"  原始: {log}")
                    print(f"  处理后: {processed}")
                    print()
            else:
                skipped_count += 1

        replacement_status = "启用内容替换" if enable_content_replacement else "仅清理格式"
        print(f"预处理完成 ({replacement_status}): 处理 {len(logs)} 条 -> 保留 {len(preprocessed_logs)} 条 (跳过 {skipped_count} 条空日志)")

        return preprocessed_logs

    def extract_log_template_with_llm(self, log_text):
        """使用LLM提取单条日志的模板

        Args:
            log_text: 预处理后的日志文本

        Returns:
            str: 提取的日志模板
        """
        messages = [
            {
                "role": "system",
                "content": """你是一个日志模板提取专家。请从给定的日志文本中提取通用模板。

规则：
1. 保留关键的静态文本和结构
2. 将变量部分用占位符替换：<VAR>
3. 保持日志的核心语义和结构
4. 模板应该能匹配同类型的其他日志
5. 不要过度泛化，避免一个模板匹配所有日志

示例：
输入: "User john logged in from <IP> at port <PORT>"
输出: "User <VAR> logged in from <IP> at port <PORT>"

输入: "Processing request <NUM> with status <VAR>"
输出: "Processing request <NUM> with status <VAR>"

请只返回模板，不要其他解释。""",
            },
            {"role": "user", "content": f"请为以下日志提取模板：\n{log_text}"},
        ]

        try:
            response = dashscope.Generation.call(model=self.model, messages=messages, result_format="message", api_key=self.api_key)
            template = response.output.choices[0].message.content.strip()
            return template if template and template != "None" else None
        except Exception as e:
            print(f"LLM调用失败: {e}")
            return None

    def match_template_to_log(self, template, log_text):
        """检查模板是否匹配日志（基于单词顺序匹配）

        Args:
            template: 日志模板
            log_text: 日志文本

        Returns:
            bool: 是否匹配
        """
        if not template or not log_text:
            return False

        # 将模板和日志分词
        template_words = template.split()
        log_words = log_text.split()

        # 如果长度差异太大，不匹配
        if abs(len(template_words) - len(log_words)) > max(len(template_words), len(log_words)) * 0.5:
            return False

        # 检查关键词匹配度
        template_static_words = [w for w in template_words if not w.startswith("<") or not w.endswith(">")]
        log_static_words = [w for w in log_words if not w.startswith("<") or not w.endswith(">")]

        if not template_static_words:
            return False

        # 计算静态词匹配率
        matched_words = sum(1 for word in template_static_words if word in log_static_words)
        match_ratio = matched_words / len(template_static_words)

        return match_ratio >= 0.7  # 70%的静态词匹配

    def cluster_logs_with_llm_templates(self, processed_logs, max_iterations=5, min_match_ratio=0.9):
        """使用LLM提取的模板对日志进行聚类

        Args:
            processed_logs: 预处理后的日志列表
            max_iterations: 最大迭代次数
            min_match_ratio: 最小匹配率阈值

        Returns:
            dict: 聚类结果 {template: [log_indices]}
        """
        print(f"开始使用LLM模板进行日志聚类，目标匹配率: {min_match_ratio:.1%}")

        # 去重，只对唯一的日志提取模板
        unique_logs = list(set(processed_logs))
        log_to_indices = defaultdict(list)
        for i, log in enumerate(processed_logs):
            log_to_indices[log].append(i)

        templates = {}  # template -> set of matched logs
        unmatched_logs = set(unique_logs)

        for iteration in range(max_iterations):
            print(f"\n=== 迭代 {iteration + 1} ===")
            print(f"待处理日志数: {len(unmatched_logs)}")

            if not unmatched_logs:
                break

            # 选择一些代表性日志提取模板
            sample_logs = list(unmatched_logs)[: min(50, len(unmatched_logs))]

            new_templates = {}
            for log in track(sample_logs, description="提取模板"):
                template = self.extract_log_template_with_llm(log)
                if template and template not in templates:
                    new_templates[template] = set()

            print(f"本轮提取了 {len(new_templates)} 个新模板")

            # 用新模板匹配所有未匹配的日志
            for template in new_templates:
                matched_logs = set()
                for log in unmatched_logs:
                    if self.match_template_to_log(template, log):
                        matched_logs.add(log)

                if matched_logs:
                    new_templates[template] = matched_logs
                    print(f"模板 '{template[:50]}...' 匹配了 {len(matched_logs)} 条日志")

            # 更新模板集合和未匹配日志
            for template, matched_logs in new_templates.items():
                if matched_logs:
                    templates[template] = matched_logs
                    unmatched_logs -= matched_logs

            # 检查匹配率
            total_matched = sum(len(logs) for logs in templates.values())
            match_ratio = total_matched / len(unique_logs)
            print(f"当前匹配率: {match_ratio:.1%} ({total_matched}/{len(unique_logs)})")

            if match_ratio >= min_match_ratio:
                print(f"达到目标匹配率 {min_match_ratio:.1%}，停止迭代")
                break

        # 处理剩余未匹配的日志
        if unmatched_logs:
            print(f"\n处理剩余 {len(unmatched_logs)} 条未匹配日志")
            for log in unmatched_logs:
                # 为每个未匹配日志创建单独模板
                templates[f"UNIQUE_{hash(log) % 10000}"] = {log}

        # 转换为最终结果格式
        result = {}
        for template, matched_logs in templates.items():
            indices = []
            for log in matched_logs:
                indices.extend(log_to_indices[log])
            result[template] = indices

        print(f"\n聚类完成:")
        print(f"- 总模板数: {len(result)}")
        print(f"- 总日志数: {len(processed_logs)}")
        print(f"- 平均每模板日志数: {len(processed_logs) / len(result):.1f}")

        return result

    def get_text_embedding(self, text):
        """获取文本的embedding向量

        Args:
            text: 输入文本

        Returns:
            np.array: embedding向量
        """
        try:
            response = dashscope.TextEmbedding.call(model="text-embedding-v4", input=text, api_key=self.api_key)
            if response.status_code == 200:
                return np.array(response.output["embeddings"][0]["embedding"])
            else:
                print(f"Embedding API error: {response.status_code}")
                return np.zeros(1024)
        except Exception as e:
            print(f"获取embedding失败: {e}")
            return np.zeros(1024)

    def get_embeddings_for_logs(self, processed_logs, df=None, save_path=None):
        """为日志列表获取embedding向量（相同内容只计算一次，支持持久化）

        Args:
            processed_logs: 预处理后的日志列表
            df: 原始DataFrame，如果提供则会尝试从中读取已有embedding
            save_path: 保存路径，如果提供则会保存embedding到DataFrame

        Returns:
            tuple: (embeddings_matrix, unique_logs, log_to_embedding_idx)
        """
        print("开始计算日志embedding...")

        # 去重，只对唯一的日志计算embedding
        unique_logs = list(set(processed_logs))
        print(f"总日志数: {len(processed_logs)}, 唯一日志数: {len(unique_logs)}")

        # 检查是否已有embedding缓存
        embedding_cache = {}
        if df is not None and "embedding" in df.columns:
            print("检测到已有embedding缓存，正在加载...")
            cached_count = 0
            for idx, row in df.iterrows():
                if pd.notna(row.get("embedding")) and row.get("processed_content") in unique_logs:
                    try:
                        # 尝试解析embedding（可能是字符串格式）
                        if isinstance(row["embedding"], str):
                            embedding = np.fromstring(row["embedding"].strip("[]"), sep=",")
                        else:
                            embedding = np.array(row["embedding"])

                        if len(embedding) > 0:  # 确保embedding有效
                            embedding_cache[row["processed_content"]] = embedding
                            cached_count += 1
                    except Exception as e:
                        print(f"解析embedding失败: {e}")
                        continue

            print(f"从缓存加载了 {cached_count} 个embedding")

        # 计算缺失的embedding
        embeddings = []
        new_embeddings = {}

        for log in track(unique_logs, description="计算embedding"):
            if log in embedding_cache:
                embeddings.append(embedding_cache[log])
            else:
                embedding = self.get_text_embedding(log)
                embeddings.append(embedding)
                new_embeddings[log] = embedding
                time.sleep(0.1)  # 避免API限流

        print(f"新计算了 {len(new_embeddings)} 个embedding")

        embeddings_matrix = np.array(embeddings)

        # 如果提供了DataFrame和保存路径，更新embedding并保存
        if df is not None and save_path is not None and new_embeddings:
            print("保存新的embedding到DataFrame...")
            self._save_embeddings_to_df(df, new_embeddings, save_path)

        # 创建日志到embedding索引的映射
        log_to_embedding_idx = {log: idx for idx, log in enumerate(unique_logs)}

        print(f"Embedding计算完成，维度: {embeddings_matrix.shape}")
        return embeddings_matrix, unique_logs, log_to_embedding_idx

    def _save_embeddings_to_df(self, df, new_embeddings, save_path):
        """保存新的embedding到DataFrame

        Args:
            df: 原始DataFrame
            new_embeddings: 新计算的embedding字典 {processed_content: embedding}
            save_path: 保存路径
        """
        try:
            # 确保DataFrame有embedding列
            if "embedding" not in df.columns:
                df["embedding"] = None

            # 更新DataFrame中的embedding
            updated_count = 0
            for idx, row in df.iterrows():
                processed_content = row.get("processed_content")
                if processed_content in new_embeddings:
                    # 将embedding转换为字符串格式保存（parquet兼容）
                    embedding_str = np.array2string(new_embeddings[processed_content], separator=",", max_line_width=np.inf)
                    df.at[idx, "embedding"] = embedding_str
                    updated_count += 1

            print(f"更新了 {updated_count} 行的embedding")

            # 保存到原路径
            df.to_parquet(save_path, engine="pyarrow", index=False)
            print(f"DataFrame已保存到: {save_path}")

        except Exception as e:
            print(f"保存embedding失败: {e}")

    def load_df_with_embeddings(self, file_path):
        """加载包含embedding的DataFrame

        Args:
            file_path: parquet文件路径

        Returns:
            pd.DataFrame: 加载的DataFrame
        """
        try:
            df = pd.read_parquet(file_path, engine="pyarrow")

            # 检查是否有embedding列
            if "embedding" in df.columns:
                embedding_count = df["embedding"].notna().sum()
                print(f"加载DataFrame成功，包含 {embedding_count} 个已有embedding")
            else:
                print("加载DataFrame成功，未发现embedding列")

            return df

        except Exception as e:
            print(f"加载DataFrame失败: {e}")
            return None

    def cluster_with_hierarchical(self, embeddings_matrix, unique_logs, distance_threshold=0.5):
        """使用层次聚类

        Args:
            embeddings_matrix: embedding矩阵
            unique_logs: 唯一日志列表
            distance_threshold: 距离阈值

        Returns:
            dict: 聚类结果 {cluster_id: [log_indices]}
        """
        print(f"开始层次聚类，距离阈值: {distance_threshold}")

        # 计算余弦距离
        distances = pdist(embeddings_matrix, metric="cosine")
        linkage_matrix = linkage(distances, method="ward")

        # 根据距离阈值确定聚类
        cluster_labels = fcluster(linkage_matrix, distance_threshold, criterion="distance")

        # 组织结果
        clusters = defaultdict(list)
        for idx, cluster_id in enumerate(cluster_labels):
            clusters[f"hierarchical_cluster_{cluster_id}"].append(idx)

        print(f"层次聚类完成，共 {len(clusters)} 个聚类")
        return dict(clusters)

    def cluster_with_kmeans_elbow(self, embeddings_matrix, max_k=20):
        """使用KMeans聚类，通过肘部法则确定最优k值

        Args:
            embeddings_matrix: embedding矩阵
            max_k: 最大k值

        Returns:
            dict: 聚类结果 {cluster_id: [log_indices]}
        """
        print(f"开始KMeans聚类，寻找最优k值 (max_k={max_k})")

        # 计算不同k值的SSE
        sse_scores = []
        k_range = range(2, min(max_k + 1, len(embeddings_matrix)))

        for k in k_range:
            kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
            kmeans.fit(embeddings_matrix)
            sse_scores.append(kmeans.inertia_)

        # 使用肘部法则选择最优k
        # 计算二阶差分来找到肘部
        if len(sse_scores) >= 3:
            diffs = np.diff(sse_scores)
            second_diffs = np.diff(diffs)
            optimal_k_idx = np.argmax(second_diffs) + 2  # +2因为二阶差分的索引偏移
            optimal_k = k_range[optimal_k_idx]
        else:
            optimal_k = k_range[0]

        print(f"选择最优k值: {optimal_k}")

        # 使用最优k进行聚类
        kmeans = KMeans(n_clusters=optimal_k, random_state=42, n_init=10)
        cluster_labels = kmeans.fit_predict(embeddings_matrix)

        # 组织结果
        clusters = defaultdict(list)
        for idx, cluster_id in enumerate(cluster_labels):
            clusters[f"kmeans_cluster_{cluster_id}"].append(idx)

        print(f"KMeans聚类完成，共 {len(clusters)} 个聚类")
        return dict(clusters)

    def cluster_with_density_based(self, embeddings_matrix, similarity_threshold=0.8):
        """使用基于密度的聚类（类似DBSCAN但基于余弦相似度）

        Args:
            embeddings_matrix: embedding矩阵
            similarity_threshold: 相似度阈值

        Returns:
            dict: 聚类结果 {cluster_id: [log_indices]}
        """
        print(f"开始基于密度的聚类，相似度阈值: {similarity_threshold}")

        # 计算余弦相似度矩阵
        similarity_matrix = cosine_similarity(embeddings_matrix)

        # 基于相似度阈值进行聚类
        n_samples = len(embeddings_matrix)
        visited = [False] * n_samples
        cluster_labels = [-1] * n_samples  # -1表示噪声点
        cluster_id = 0

        for i in range(n_samples):
            if visited[i]:
                continue

            visited[i] = True

            # 找到相似的邻居
            neighbors = []
            for j in range(n_samples):
                if similarity_matrix[i][j] >= similarity_threshold:
                    neighbors.append(j)

            if len(neighbors) >= 2:  # 至少需要2个相似点形成聚类
                cluster_labels[i] = cluster_id

                # 扩展聚类
                seed_set = neighbors[:]
                for neighbor_idx in seed_set:
                    if not visited[neighbor_idx]:
                        visited[neighbor_idx] = True

                        # 找到邻居的邻居
                        neighbor_neighbors = []
                        for k in range(n_samples):
                            if similarity_matrix[neighbor_idx][k] >= similarity_threshold:
                                neighbor_neighbors.append(k)

                        if len(neighbor_neighbors) >= 2:
                            seed_set.extend(neighbor_neighbors)

                    if cluster_labels[neighbor_idx] == -1:
                        cluster_labels[neighbor_idx] = cluster_id

                cluster_id += 1

        # 组织结果
        clusters = defaultdict(list)
        for idx, cluster_id in enumerate(cluster_labels):
            if cluster_id != -1:
                clusters[f"density_cluster_{cluster_id}"].append(idx)
            else:
                clusters[f"noise_{idx}"].append(idx)

        print(f"基于密度的聚类完成，共 {len(clusters)} 个聚类")
        return dict(clusters)

    def cluster_logs_with_embeddings(self, processed_logs, methods=["hierarchical", "kmeans", "density"]):
        """使用embedding进行日志聚类的综合方法

        Args:
            processed_logs: 预处理后的日志列表
            methods: 要使用的聚类方法列表

        Returns:
            dict: 各种聚类方法的结果 {method_name: {cluster_id: [original_log_indices]}}
        """
        print("开始embedding聚类...")

        # 获取embeddings
        embeddings_matrix, unique_logs, log_to_embedding_idx = self.get_embeddings_for_logs(processed_logs)

        # 创建原始日志索引映射
        log_to_original_indices = defaultdict(list)
        for i, log in enumerate(processed_logs):
            log_to_original_indices[log].append(i)

        results = {}

        # 层次聚类
        if "hierarchical" in methods:
            print("\n=== 层次聚类 ===")
            hierarchical_clusters = self.cluster_with_hierarchical(embeddings_matrix, unique_logs)

            # 映射回原始日志索引
            hierarchical_result = {}
            for cluster_id, embedding_indices in hierarchical_clusters.items():
                original_indices = []
                for emb_idx in embedding_indices:
                    log = unique_logs[emb_idx]
                    original_indices.extend(log_to_original_indices[log])
                hierarchical_result[cluster_id] = original_indices

            results["hierarchical"] = hierarchical_result

        # KMeans聚类
        if "kmeans" in methods:
            print("\n=== KMeans聚类 ===")
            kmeans_clusters = self.cluster_with_kmeans_elbow(embeddings_matrix)

            # 映射回原始日志索引
            kmeans_result = {}
            for cluster_id, embedding_indices in kmeans_clusters.items():
                original_indices = []
                for emb_idx in embedding_indices:
                    log = unique_logs[emb_idx]
                    original_indices.extend(log_to_original_indices[log])
                kmeans_result[cluster_id] = original_indices

            results["kmeans"] = kmeans_result

        # 基于密度的聚类
        if "density" in methods:
            print("\n=== 基于密度的聚类 ===")
            density_clusters = self.cluster_with_density_based(embeddings_matrix)

            # 映射回原始日志索引
            density_result = {}
            for cluster_id, embedding_indices in density_clusters.items():
                original_indices = []
                for emb_idx in embedding_indices:
                    log = unique_logs[emb_idx]
                    original_indices.extend(log_to_original_indices[log])
                density_result[cluster_id] = original_indices

            results["density"] = density_result

        # 打印总结
        print(f"\n=== 聚类结果总结 ===")
        for method, clusters in results.items():
            print(f"{method}: {len(clusters)} 个聚类")
            cluster_sizes = [len(indices) for indices in clusters.values()]
            print(f"  - 平均聚类大小: {np.mean(cluster_sizes):.1f}")
            print(f"  - 最大聚类大小: {max(cluster_sizes)}")
            print(f"  - 最小聚类大小: {min(cluster_sizes)}")

        return results


# %%
def demo_clustering_methods():
    """演示两种聚类方法的使用"""

    # 加载数据
    group_key = {"ecs_regionmaster_info_log": ["file"], "ecs_regionmaster_error_log": ["file"], "ecs_pync_log": ["file"], "libvirt_log": ["class", "line"]}
    pattern = os.path.join(data_path, "*.parquet")
    parquet_files = glob.glob(pattern)

    if not parquet_files:
        print("未找到parquet文件")
        return

    # 加载第一个文件进行演示
    filename = parquet_files[0]
    df = pd.read_parquet(filename, engine="pyarrow")
    logstore = os.path.basename(filename).split(".")[0]
    logs = df["content"].tolist()

    print(f"加载日志文件: {logstore}")
    print(f"原始日志数量: {len(logs)}")

    # 初始化聚类器
    clusterer = LogTemplateExtractor()

    # 预处理日志
    processed_logs = clusterer.batch_preprocess_logs(logs)
    df["processed_content"] = processed_logs

    print(f"预处理后日志数量: {len(processed_logs)}")

    # 取样本进行演示（避免API调用过多）
    sample_size = min(100, len(processed_logs))
    sample_logs = processed_logs[:sample_size]

    print(f"\n使用样本进行演示，样本大小: {sample_size}")

    # 方法1: LLM模板聚类
    print("\n" + "=" * 60)
    print("方法1: LLM模板聚类")
    print("=" * 60)

    try:
        llm_clusters = clusterer.cluster_logs_with_llm_templates(sample_logs, max_iterations=3, min_match_ratio=0.8)

        print(f"\nLLM聚类结果:")
        for template, indices in list(llm_clusters.items())[:5]:  # 只显示前5个
            print(f"模板: {template[:80]}...")
            print(f"匹配日志数: {len(indices)}")
            print(f"示例日志: {sample_logs[indices[0]][:80]}...")
            print("-" * 40)

    except Exception as e:
        print(f"LLM聚类失败: {e}")

    # 方法2: Embedding聚类
    print("\n" + "=" * 60)
    print("方法2: Embedding聚类")
    print("=" * 60)

    try:
        embedding_clusters = clusterer.cluster_logs_with_embeddings(sample_logs, methods=["hierarchical", "kmeans"])  # 先只用这两种方法

        print(f"\nEmbedding聚类结果:")
        for method, clusters in embedding_clusters.items():
            print(f"\n{method.upper()} 方法:")
            for cluster_id, indices in list(clusters.items())[:3]:  # 只显示前3个聚类
                print(f"  聚类 {cluster_id}: {len(indices)} 条日志")
                if indices:
                    print(f"    示例: {sample_logs[indices[0]][:80]}...")

    except Exception as e:
        print(f"Embedding聚类失败: {e}")

    return df


# 运行演示
if __name__ == "__main__":
    df = demo_clustering_methods()

# %%
