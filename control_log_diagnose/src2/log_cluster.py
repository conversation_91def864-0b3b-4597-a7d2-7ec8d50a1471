# %%
import re
import pandas as pd
import numpy as np
import dashscope
from rich.progress import track

import os
import sys
import gzip
import json
import glob
import time

BASE_PATH = os.path.abspath(os.path.join(__file__, "../../.."))
if BASE_PATH not in sys.path:
    sys.path.append(BASE_PATH)

data_path = os.path.join(BASE_PATH, "output", "control_log_diagnose", "processed_data")


class LogTemplateExtractor:
    """日志模板提取器"""

    def __init__(self, api_key=None, model="qwen3-coder-plus"):
        """初始化模板提取器

        Args:
            api_key: DashScope API密钥
            model: 使用的LLM模型
        """
        self.model = model
        # 预编译正则表达式以提高性能
        self.preprocess_patterns = self._compile_preprocess_patterns()

    def _compile_preprocess_patterns(self):
        """预编译用于日志预处理的正则表达式"""
        return {
            # 时间戳模式 (多种格式)
            "timestamps": [
                re.compile(r"\d{4}-\d{2}-\d{2}[T\s]\d{2}:\d{2}:\d{2}(?:\.\d{3})?(?:[+-]\d{4}|\.\d+[+-]\d{4}|Z)?"),  # ISO格式
                re.compile(r"\d{4}/\d{2}/\d{2}\s+\d{2}:\d{2}:\d{2}"),  # 斜杠分隔
                re.compile(r"\d{2}/\d{2}/\d{4}\s+\d{2}:\d{2}:\d{2}"),  # MM/dd/yyyy
                re.compile(r"\d{10,13}"),  # Unix时间戳
                re.compile(
                    r"(?:Fri|Sat|Sun|Thu|Mon|Tue|Wed)\s+(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+\d{1,2}\s+\d{2}:\d{2}:\d{2}\s+CST\s+\d{2,4}"
                ),  # CST格式
                re.compile(r"(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+\d{1,2}\s+[0-2]\d(?::[0-5]\d){2}"),  # syslog格式
                re.compile(r"\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3,6}(?:Z|[+-]\d{2}:\d{2})?"),  # 高精度ISO
            ],
            # 方括号内容模式
            "brackets": [
                re.compile(r"\[pid=\d+[^\]]*\]"),  # [pid=5580,iso=0]
                re.compile(r"\[\d{4}/\d{2}/\d{2}\s+\d{2}:\d{2}:\d{2}\]"),  # [2025/07/22 18:10:57]
                re.compile(r"\[\d{10,13}\]"),  # [1753179057331]
                re.compile(r"\[(?:debug|info|warn|error|fatal|trace)\]", re.IGNORECASE),  # [info], [ERROR]等
                re.compile(r"\[[^\]]*\d+[^\]]*\]"),  # 包含数字的其他方括号内容
            ],
            # 日志级别模式 (独立出现)
            "log_levels": re.compile(r"\b(?:DEBUG|INFO|WARN|WARNING|ERROR|FATAL|TRACE)\b:?\s*", re.IGNORECASE),
            # 进程/线程ID
            "process_info": [
                re.compile(r"\bpid[=:]\s*\d+\b", re.IGNORECASE),
                re.compile(r"\btid[=:]\s*\d+\b", re.IGNORECASE),
                re.compile(r"\bthread[=:]\s*\d+\b", re.IGNORECASE),
            ],
            # 其他常见的元信息
            "metadata": [
                re.compile(r"\b\w+\s*=\s*[a-f0-9-]{8,}\b"),  # 类似 requestId=abc123def
                re.compile(r"\b\w+Id\s*=\s*[\w-]+"),  # xxxId=something
            ],
            # === 新增的内容替换模式 ===
            # IP地址模式
            "ip_addresses": [
                re.compile(r"\b(?:\d{1,3}\.){3}\d{1,3}\b"),  # IPv4
                re.compile(r"\b(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}\b"),  # IPv6完整
                re.compile(r"\b(?:[0-9a-fA-F]{1,4}:){1,7}:(?:[0-9a-fA-F]{1,4})?"),  # IPv6简化
            ],
            # 实例ID和资源ID模式
            "instance_ids": [
                re.compile(r"\bi-[a-z0-9]{17,20}\b"),  # ECS实例ID: i-2ze9c4ik6b5vx3zqy3jq (17-20字符)
                re.compile(r"\bINSTANCE_i-[a-z0-9]{17,20}\b"),  # INSTANCE_i-xxx
                re.compile(r"\bslb-[a-z0-9]{17,20}\b"),  # SLB实例ID
                re.compile(r"\brds-[a-z0-9]{17,20}\b"),  # RDS实例ID
                re.compile(r"\bvolume-[a-z0-9]{17,20}\b"),  # 磁盘卷ID
                # 云服务器VM实例ID (多种前缀)
                re.compile(
                    r"(?:(?<=[^A-Za-z0-9-_])|^)(?:i-|hbm-|AY|eci-|cp-|ic-|BVT|bvt|wh|cn|wy|qy|hm|VM|al|uh|ay|tmpvm|ace|vm|houyiecsay)[-]?[a-zA-Z0-9-_]{8,62}(?=(?:[^A-Z0-9])|$)"
                ),
                # 更多VM实例格式
                re.compile(
                    r"(?:qemu-BVT-CTRL-EcsBasic|qemu-iso-i|qemu-i|eci-ops|vnd|acs|iso-i|iso-BVT-DATARS|qemu|eni|i|dh|gds|ri-cr|sg|sgr|hbm|AY|eci|cp|ic|BVT|wh|cn|wy|qy|hm|VM|al|uh|ay|tmpvm|ace|vm|sd|e)-[a-zA-Z0-9]{8,24}|qemu-virT_CheCK_enV_VM|qemu-CNforVM"
                ),
                re.compile(r"(?:vm:|Vm:|VM:|vmName:|performance)[a-zA-Z0-9-]{8,36}"),
                # 测试VM实例
                re.compile(r"(?:BVT|(?:qemu-|fc-|zhj-|jingshu-|ebs)test|qemu-test-net)([a-zA-Z0-9]){0,10}(-[a-zA-Z0-9]{1,20}){1,6}"),
            ],
            # 文件路径模式
            "file_paths": [
                re.compile(r'[a-zA-Z]:\\(?:[^\\/:*?"<>|\r\n]+\\)*[^\\/:*?"<>|\r\n]*'),  # Windows路径
                re.compile(r"/(?:[^/\0\n\r\t]+/)*[^/\0\n\r\t]*"),  # Unix路径
                re.compile(r"\\\\[^\\]+\\[^\\]+(?:\\[^\\]*)*"),  # UNC路径
            ],
            # 端口号模式
            "ports": [
                re.compile(r"\bport\s+(\d{1,5})\b", re.IGNORECASE),
                re.compile(r":(\d{1,5})\b"),  # :8080
            ],
            # UUID和哈希值模式
            "uuids": [
                re.compile(r"\b[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\b"),  # UUID
                re.compile(r"\b[0-9a-fA-F]{32}\b"),  # MD5哈希
                re.compile(r"\b[0-9a-fA-F]{40}\b"),  # SHA1哈希
                re.compile(r"\b[0-9a-fA-F]{64}\b"),  # SHA256哈希
            ],
            # 数字和编号模式
            "numbers": [
                re.compile(r"\b\d{4,}\b"),  # 长数字 (4位以上)
                re.compile(r"\b\d+\.\d+\.\d+(?:\.\d+)*\b"),  # 版本号
                re.compile(r"\b\d+ms\b|\b\d+s\b|\b\d+MB\b|\b\d+KB\b", re.IGNORECASE),  # 数量单位
            ],
            # 会话和认证令牌
            "tokens": [
                re.compile(r"\b[A-Za-z0-9+/]{20,}={0,2}\b"),  # Base64编码
                re.compile(r"\btoken[_=:]\s*[A-Za-z0-9._-]+", re.IGNORECASE),
                re.compile(r"\bsessionid[_=:]\s*[A-Za-z0-9._-]+", re.IGNORECASE),
                re.compile(r"access_?[kK]ey_?[iI]d=(?:[\\dA-Za-z=\'/\\+]{16,128})?(?:\s|,|;|$)"),  # Access Key ID
                re.compile(r"sign=(?:[\\dA-Za-z=\'/\\+]{16,128})?(?:\s|,|;|$)"),  # 签名
            ],
            # 阿里云特定资源标识
            "alibaba_resources": [
                re.compile(r"(?:(?:MOCK-)?ASW-)[A-Z0-9-.]{12,}"),  # ASW资源
                re.compile(r"(?:[^a-zA-Z0-9_-]|^)(?:[a-zA-Z0-9_-]+\.)+(?:alibaba-inc|aliyun-inc|aliyuncs)\.com"),  # 阿里云内网域名
                re.compile(r".+aliyuncs\.com"),  # 阿里云服务域名
                re.compile(r"(?:cn|ap|me|eu|us|rus)(-[A-Z0-9a-z]+)+"),  # 地域信息
                re.compile(r"[0-9a-z]{7,12}\.cloud\.[0-9a-z]{3,6}(?:\.na\d{1,4})?"),  # 主机名
                re.compile(r"(?:(?:CN\d{0,3}\.|)[0-9a-z]{8,12}\.(?:cloud|sqa)|ecs-onebox[0-9]{8,15})\.[0-9a-z]{3,6}(?:\.na\d{1,4})?"),  # CN节点ID
            ],
            # 网络和硬件标识
            "network_hardware": [
                re.compile(r"[\da-fA-F]{2}(?::[\da-fA-F]{2}){5}"),  # MAC地址
                re.compile(r"(?:Ethernet)[0-9]{0,3}"),  # 以太网接口
                re.compile(r"drive-virtio-disk[0-9]{1,3}"),  # 虚拟磁盘设备
                re.compile(r"(?:virt|virtio-disk|net|vcpu|cache|nvme-disk|vf\.|pci\.\d{1,3}-net|pci\.\d{1,3}-(?:virtio|nvme)-disk)\d{1,6}"),  # 虚拟设备ID
                re.compile(r"[0-9a-f]{2,4}(?::[0-9a-f]{2,6}){5,10}"),  # 设备ID
                re.compile(r"[0-9]{4}:[0-9a-f]{2}:[0-9a-f]{2}\.[0-9]"),  # PCI设备ID
            ],
            # 内存地址和指针
            "memory_addresses": [
                re.compile(r"(at |\[<{0,1})*([0-9a-f]{8}`{0,1}[0-9a-f]{8})(>{0,1}\])*"),  # 内存地址
                re.compile(r"\+*0x[0-9a-f]{1,16}(/0x[0-9a-f]{1,16})*"),  # 十六进制地址
                re.compile(r"(?:^|[ \[\(=])0x[A-Fa-f0-9]{1,16}(?:[: ,\]\)]|$)"),  # 十六进制值 (必须有0x前缀)
                # 只匹配明确的十六进制地址格式，避免误匹配普通数字
                re.compile(
                    r"(?:^|[< ,\[\-:])(?:(?!CC|add|fade|be|bad|Add|ADD|Bad|BAD|BE|Be|Fade|FADE)[a-fA-F]{1,}[0-9a-fA-F]{7,19}(?:[ \]\-,.>:]|$)){1,10}"
                ),  # 通用十六进制 (必须包含字母)
            ],
            # Java异常堆栈和反射
            "java_exceptions": [
                re.compile(
                    r"\s(?:at )?[\d\w]{1,50}(?:\.[<>/\w\d$]+){1,10}\((?:(?:[$\w\d]{1,100}\.java(?::\d{1,5})?)|(?:<generated>)|(?:Native Method)|(?:Unknown Source))\)"
                ),  # Java堆栈
                re.compile(r"\t\.\.\. \d{1,5} (?:more|common frames omitted)"),  # 省略的堆栈
                re.compile(r"sun\.reflect\.GeneratedConstructorAccessor[0-9]{0,5}\.newInstance"),  # 反射构造器
                re.compile(r"sun\.reflect\.GeneratedMethodAccessor[0-9]{0,5}\.invoke"),  # 反射方法
                re.compile(r" ~?\[na: ?\d+\.\d+\.\d+(?:_\d+)?\]"),  # jar版本信息
                re.compile(
                    r" ~?\[(?:[a-z_0-9]+\-)+\d+\.\d+\.\d+(?:[\.-][\d-]+)?(?:\-FASTJSON|\-SNAPSHOT|\.RELEASE|\.noneautotype|\-ahas|-bugfix\.?\d*|-stable\.?\d*|(?:\-[a-z_0-9]+)+)?\.jar(?:!/)?:(?:na|\d+\.\d+\.\d+(?:\.RELEASE|-SNAPSHOT)?)\]"
                ),
            ],
            # 容器和集群标识
            "containers": [
                re.compile(r"docker[0-9]{12}_na[0-9]{2}_xdragon_diagnose_center(_[0-9]{4}){2}"),  # Docker容器ID
                re.compile(r"DEFAULT\.[a-f0-9]{12}-[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}"),  # 作业ID
                re.compile(r"re-[a-f0-9]{8}(?:-[a-f0-9]{4}){3}-[a-f0-9]{12}"),  # 发布ID
            ],
            # URL和服务端点
            "urls_endpoints": [
                re.compile(
                    r"(?:http[s]?|dubbo)://(?:\d{1,3}(?:\.\d{1,3}){3}|[\w-]{2,20}(?:\.[\w-]{2,20}){0,4})(?::\d{1,5})?(?:/[\w\-#\.]{1,50}){1,10}"
                ),  # HTTP/Dubbo URL
                re.compile(
                    r'["\'(,]?(?:https?|dubbo)://\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}(?::\d{1,5})?(?:/\w{1,32}){0,10}(?:\.\w{1,50}){0,20}["\',:)]?'
                ),  # IP格式URL
                re.compile(r"(?:dubbo|aliyun-nacos-ns)://.+/.+\?[_a-zA-Z0-9.-]+=[_a-zA-Z0-9-.]+(?:&[_a-zA-Z0-9.-]+=[^=&]{0,})+(?:, | |$)"),  # Dubbo配置URL
                re.compile(r"\?[^&= ]{1,20}=[^& ]{0,100}(?:(?:(?:&[^&= ]{1,30}=[^& ]{0,1000}){1,100})|\])(?:[, ;\.]|$)"),  # URL参数
            ],
        }

    def preprocess_log(self, log_text, preserve_structure=True, enable_content_replacement=True):
        """预处理单条日志

        Args:
            log_text: 原始日志文本
            preserve_structure: 是否保留基本结构（如冒号、等号等）
            enable_content_replacement: 是否启用内容替换（将显著内容替换为占位符）

        Returns:
            str: 预处理后的日志文本
        """
        if not log_text or not isinstance(log_text, str):
            return ""

        # 1. 去除换行符和回车符
        cleaned = log_text.replace("\r\n", " ").replace("\r", " ").replace("\n", " ")

        # 2. 先去除时间戳（避免时间戳被其他模式错误匹配）
        for timestamp_pattern in self.preprocess_patterns["timestamps"]:
            cleaned = timestamp_pattern.sub("", cleaned)

        # 3. 内容替换阶段（在去除时间戳之后进行）
        if enable_content_replacement:
            # 替换IP地址
            for ip_pattern in self.preprocess_patterns["ip_addresses"]:
                cleaned = ip_pattern.sub("<IP>", cleaned)

            # 替换实例ID和资源ID
            for instance_pattern in self.preprocess_patterns["instance_ids"]:
                cleaned = instance_pattern.sub("<INSTANCE_ID>", cleaned)

            # 替换文件路径
            for path_pattern in self.preprocess_patterns["file_paths"]:
                cleaned = path_pattern.sub("<PATH>", cleaned)

            # 替换端口号
            for port_pattern in self.preprocess_patterns["ports"]:
                if "port" in port_pattern.pattern.lower():
                    cleaned = port_pattern.sub(r"port <PORT>", cleaned)
                else:
                    cleaned = port_pattern.sub(r":<PORT>", cleaned)

            # 替换UUID和哈希值
            for uuid_pattern in self.preprocess_patterns["uuids"]:
                cleaned = uuid_pattern.sub("<UUID>", cleaned)

            # 替换数字和编号
            for number_pattern in self.preprocess_patterns["numbers"]:
                if "ms\\b|s\\b|MB\\b|KB\\b" in number_pattern.pattern:
                    # 保留单位，只替换数字
                    cleaned = re.sub(r"\b(\d+)(ms|s|MB|KB)\b", r"<NUM>\2", cleaned, flags=re.IGNORECASE)
                elif r"\d+\.\d+\.\d+" in number_pattern.pattern:
                    cleaned = number_pattern.sub("<VERSION>", cleaned)
                else:
                    cleaned = number_pattern.sub("<NUM>", cleaned)

            # 替换令牌和会话信息
            for token_pattern in self.preprocess_patterns["tokens"]:
                if "token" in token_pattern.pattern.lower():
                    cleaned = token_pattern.sub("token=<TOKEN>", cleaned)
                elif "sessionid" in token_pattern.pattern.lower():
                    cleaned = token_pattern.sub("sessionid=<SESSION>", cleaned)
                elif "access" in token_pattern.pattern.lower():
                    cleaned = token_pattern.sub("access_key_id=<ACCESS_KEY>", cleaned)
                elif "sign" in token_pattern.pattern.lower():
                    cleaned = token_pattern.sub("sign=<SIGN>", cleaned)
                else:
                    cleaned = token_pattern.sub("<TOKEN>", cleaned)

            # 替换阿里云特定资源标识
            for alibaba_pattern in self.preprocess_patterns["alibaba_resources"]:
                if "ASW" in alibaba_pattern.pattern:
                    cleaned = alibaba_pattern.sub("<ASW_RESOURCE>", cleaned)
                elif "aliyuncs" in alibaba_pattern.pattern or "alibaba-inc" in alibaba_pattern.pattern:
                    cleaned = alibaba_pattern.sub("<ALIBABA_DOMAIN>", cleaned)
                elif "cloud" in alibaba_pattern.pattern:
                    cleaned = alibaba_pattern.sub("<CLOUD_HOST>", cleaned)
                else:
                    cleaned = alibaba_pattern.sub("<REGION>", cleaned)

            # 替换网络和硬件标识
            for network_pattern in self.preprocess_patterns["network_hardware"]:
                if "da-fA-F" in network_pattern.pattern and ":" in network_pattern.pattern:
                    cleaned = network_pattern.sub("<MAC>", cleaned)
                elif "Ethernet" in network_pattern.pattern:
                    cleaned = network_pattern.sub("<ETHERNET>", cleaned)
                elif "drive-virtio" in network_pattern.pattern:
                    cleaned = network_pattern.sub("<VDISK>", cleaned)
                elif "pci" in network_pattern.pattern or "virtio" in network_pattern.pattern:
                    cleaned = network_pattern.sub("<DEVICE>", cleaned)
                else:
                    cleaned = network_pattern.sub("<DEVICE_ID>", cleaned)

            # 替换内存地址和指针
            for addr_pattern in self.preprocess_patterns["memory_addresses"]:
                cleaned = addr_pattern.sub("<ADDR>", cleaned)

            # 替换Java异常堆栈
            for java_pattern in self.preprocess_patterns["java_exceptions"]:
                if "at " in java_pattern.pattern:
                    cleaned = java_pattern.sub("\tat <JAVA_STACK>", cleaned)
                elif "..." in java_pattern.pattern:
                    cleaned = java_pattern.sub("\t... <MORE_FRAMES>", cleaned)
                elif "reflect" in java_pattern.pattern:
                    cleaned = java_pattern.sub("<REFLECTION>", cleaned)
                else:
                    cleaned = java_pattern.sub("<JAR_INFO>", cleaned)

            # 替换容器和集群标识
            for container_pattern in self.preprocess_patterns["containers"]:
                if "docker" in container_pattern.pattern:
                    cleaned = container_pattern.sub("<DOCKER_ID>", cleaned)
                elif "DEFAULT" in container_pattern.pattern:
                    cleaned = container_pattern.sub("<JOB_ID>", cleaned)
                else:
                    cleaned = container_pattern.sub("<RELEASE_ID>", cleaned)

            # 替换URL和服务端点
            for url_pattern in self.preprocess_patterns["urls_endpoints"]:
                if "dubbo" in url_pattern.pattern:
                    cleaned = url_pattern.sub("<DUBBO_URL>", cleaned)
                elif "?" in url_pattern.pattern and "&" in url_pattern.pattern:
                    cleaned = url_pattern.sub("<URL_PARAMS>", cleaned)
                else:
                    cleaned = url_pattern.sub("<URL>", cleaned)

        # 4. 去除方括号内容
        for bracket_pattern in self.preprocess_patterns["brackets"]:
            cleaned = bracket_pattern.sub("", cleaned)

        # 5. 去除日志级别
        cleaned = self.preprocess_patterns["log_levels"].sub("", cleaned)

        # 6. 去除进程/线程信息
        for process_pattern in self.preprocess_patterns["process_info"]:
            cleaned = process_pattern.sub("", cleaned)

        # 7. 去除其他元信息（可选）
        if not preserve_structure:
            for metadata_pattern in self.preprocess_patterns["metadata"]:
                cleaned = metadata_pattern.sub("", cleaned)

        # 8. 清理多余空格
        cleaned = re.sub(r"\s+", " ", cleaned).strip()

        # 9. 去除开头和结尾的特殊字符
        cleaned = cleaned.strip(" .,;:-_=")

        # 10. 最终清理连续的占位符
        cleaned = re.sub(r"(<[^>]+>)\s+\1", r"\1", cleaned)  # 去除重复的相同占位符

        return cleaned

    def batch_preprocess_logs(self, logs, show_examples=True, enable_content_replacement=True):
        """批量预处理日志

        Args:
            logs: 日志列表
            show_examples: 是否显示预处理示例
            enable_content_replacement: 是否启用内容替换

        Returns:
            list: 预处理后的日志列表
        """
        if not logs:
            return []

        preprocessed_logs = []
        skipped_count = 0

        for log in logs:
            processed = self.preprocess_log(log, enable_content_replacement=enable_content_replacement)

            if processed:  # 只保留非空的处理结果
                preprocessed_logs.append(processed)

                # 显示前几个预处理示例
                if show_examples and len(preprocessed_logs) <= 3:
                    print(f"预处理示例 {len(preprocessed_logs)}:")
                    print(f"  原始: {log}")
                    print(f"  处理后: {processed}")
                    print()
            else:
                skipped_count += 1

        replacement_status = "启用内容替换" if enable_content_replacement else "仅清理格式"
        print(f"预处理完成 ({replacement_status}): 处理 {len(logs)} 条 -> 保留 {len(preprocessed_logs)} 条 (跳过 {skipped_count} 条空日志)")

        return preprocessed_logs


# %%
group_key = {"ecs_regionmaster_info_log": ["file"], "ecs_regionmaster_error_log": ["file"], "ecs_pync_log": ["file"], "libvirt_log": ["class", "line"]}
pattern = os.path.join(data_path, "*.parquet")
parquet_files = glob.glob(pattern)

for filename in parquet_files:
    df = pd.read_parquet(filename, engine="pyarrow")
    logstore = os.path.basename(filename).split(".")[0]
    logs = df["content"].tolist()

    preprocessor = LogTemplateExtractor()
    df["processed_content"] = preprocessor.batch_preprocess_logs(logs)

    
    break

# %%
