# %%
import re
import pandas as pd
import numpy as np
import dashscope
from rich.progress import track

import os
import sys
import gzip
import json
import glob
import time

BASE_PATH = os.path.abspath(os.path.join(__file__, "../../.."))
if BASE_PATH not in sys.path:
    sys.path.append(BASE_PATH)

data_path = os.path.join(BASE_PATH, "output", "control_log_diagnose", "logs_output")


def load_all_jsonl_gz(data_path: str) -> list:
    """Load all jsonl.gz files from the data_path directory into a single list"""
    start_time = time.time()
    all_data = []

    # Find all jsonl.gz files in the directory
    pattern = os.path.join(data_path, "*.jsonl.gz")
    jsonl_files = glob.glob(pattern)

    print(f"Found {len(jsonl_files)} jsonl.gz files")

    for i, file_path in enumerate(jsonl_files, 1):
        file_start = time.time()
        print(f"Loading {file_path}... ({i}/{len(jsonl_files)})")

        with gzip.open(file_path, "rt", encoding="utf-8") as f:
            for line in f:
                if line.strip():  # Skip empty lines
                    try:
                        data = json.loads(line)
                        all_data.append(data)
                    except json.JSONDecodeError as e:
                        print(f"Error parsing line in {file_path}: {e}")
                        continue

        file_elapsed = time.time() - file_start
        print(f"  Loaded in {file_elapsed:.2f}s")

    total_elapsed = time.time() - start_time
    print(f"Loaded {len(all_data)} records from {len(jsonl_files)} files in {total_elapsed:.2f}s")
    print(f"Average: {total_elapsed/len(jsonl_files):.2f}s per file, {len(all_data)/total_elapsed:.0f} records/s")

    return all_data


if __name__ == "__main__":
    data = load_all_jsonl_gz(data_path)


data.__len__()
logstore_resource = {}
for index, record in track(enumerate(data), description="Processing records"):
    meta_data = record.get("record", {})
    logstore_data = record.get("logstore", {})
    for logstore, logs in logstore_data.items():
        if logstore not in logstore_resource:
            logstore_resource[logstore] = []
        for log in logs:
            log.update(meta_data)
            logstore_resource[logstore].append(log)

# %%
# Statistic

keep_keys = {
    "ecs_regionmaster_info_log": [
        "content",
        "__time__",
        "__source__",
        "level",
        "briefTraceInfo",
        "file",
        "instance_id",
        "action_time",
        "nc_ip",
        "action_type",
    ],
    "ecs_regionmaster_error_log": [
        "content",
        "__time__",
        "__source__",
        "level",
        "briefTraceInfo",
        "file",
        "instance_id",
        "action_time",
        "nc_ip",
        "action_type",
    ],
    "schedule_trace": [
        "content",
        "__time__",
        "__source__",
        "v3FailDegradeToV2",
        "filterInfoNodes",
        "scoreNode",
        "ncRescheduleResourceNode",
        "instance_id",
        "action_time",
        "nc_ip",
        "action_type",
    ],
    "ecs_pync_log": ["content", "__time__", "__source__", "file", "level", "module", "instance_id", "action_time", "nc_ip", "action_type"],
    "libvirt_log": ["content", "__time__", "__source__", "class", "code", "detail", "level", "line", "instance_id", "action_time", "nc_ip", "action_type"],
    "iohub_pcie_log": ["content", "__time__", "__source__", "instance_id", "action_time", "nc_ip", "action_type"],
    "console_log": ["content", "__time__", "__source__", "instance_id", "action_time", "nc_ip", "action_type"],
    "iohub-server": ["content", "__time__", "__source__", "instance_id", "action_time", "nc_ip", "action_type"],
    "default": ["content", "__time__", "__source__", "instance_id", "action_time", "nc_ip", "action_type"],
}
for key, value in logstore_resource.items():
    if len(value) == 0:
        continue
    print(f"{key}: {len(value)}")

    # 先获取要保留的字段列表
    keep_keys_list = keep_keys.get(key, keep_keys["default"])
    print(f"Keeping fields: {keep_keys_list}")

    # 过滤数据：只保留需要的字段，减少内存使用
    filtered_data = []
    for record in value:
        # 只保留需要的字段
        filtered_record = {field: record.get(field) for field in keep_keys_list if field in record}
        filtered_data.append(filtered_record)

    # 现在创建DataFrame，内存使用会大大减少
    df = pd.DataFrame(filtered_data)

    # 确保所有需要的列都存在（填充缺失的列）
    for col in keep_keys_list:
        if col not in df.columns:
            df[col] = None

    # 重新排序列以匹配期望的顺序
    df = df[keep_keys_list]

    # 保存到parquet
    output_dir = os.path.join(BASE_PATH, "output/control_log_diagnose/processed_data")
    os.makedirs(output_dir, exist_ok=True)

    df.to_parquet(
        os.path.join(output_dir, f"{key}.parquet"),
        index=False,
        engine="pyarrow",
    )

    print(f"Saved {len(df)} records to {key}.parquet")

    # 清理内存
    del filtered_data
    del df
# %%

