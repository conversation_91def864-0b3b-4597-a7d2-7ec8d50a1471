"""
日志序列成功/失败预测器 - 简化API

基于训练好的模型提供简单的预测接口
"""

import os
import sys
import pickle
import pandas as pd
import numpy as np
from collections import Counter, defaultdict
import networkx as nx

BASE_PATH = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if BASE_PATH not in sys.path:
    sys.path.append(BASE_PATH)

from control_log_diagnose.src.sequence_classifier import LogSequenceClassifier


class LogSequencePredictor:
    """简化的日志序列预测器"""

    def __init__(self, model_path=None):
        """
        初始化预测器

        Args:
            model_path: 模型文件路径，如果为None则使用默认路径
        """
        self.classifier = LogSequenceClassifier()

        if model_path is None:
            model_path = os.path.join(BASE_PATH, "control_log_diagnose/models/sequence_classifier.pkl")

        if os.path.exists(model_path):
            self.classifier.load_model(model_path)
            self.model_loaded = True
            print(f"✓ 模型加载成功: {model_path}")
        else:
            self.model_loaded = False
            print(f"✗ 模型文件不存在: {model_path}")
            print("请先运行 sequence_classifier.py 训练模型")

    def predict(self, log_sequence, return_details=False):
        """
        预测日志序列的成功/失败状态

        Args:
            log_sequence: 日志pattern序列，例如 ['pattern_1', 'pattern_2', 'pattern_3']
            return_details: 是否返回详细信息

        Returns:
            如果 return_details=False: 返回预测结果字符串 ('Success' 或 'Failure')
            如果 return_details=True: 返回详细的预测信息字典
        """
        if not self.model_loaded:
            raise RuntimeError("模型未加载，无法进行预测")

        if not log_sequence or len(log_sequence) == 0:
            return "Invalid" if not return_details else {"prediction": "Invalid", "reason": "Empty sequence"}

        try:
            result = self.classifier.predict(log_sequence)

            if return_details:
                return {
                    "prediction": result["prediction_label"],
                    "confidence": result["confidence"],
                    "probability_success": result["probability_success"],
                    "probability_failure": result["probability_failure"],
                    "sequence_length": len(log_sequence),
                    "unique_patterns": len(set(log_sequence)),
                }
            else:
                return result["prediction_label"]

        except Exception as e:
            if return_details:
                return {"prediction": "Error", "reason": str(e)}
            else:
                return "Error"

    def predict_batch(self, log_sequences, return_details=False):
        """
        批量预测多个日志序列

        Args:
            log_sequences: 日志序列列表
            return_details: 是否返回详细信息

        Returns:
            预测结果列表
        """
        results = []
        for sequence in log_sequences:
            result = self.predict(sequence, return_details)
            results.append(result)
        return results

    def analyze_sequence(self, log_sequence):
        """
        分析序列的详细特征（用于调试和理解）

        Args:
            log_sequence: 日志pattern序列

        Returns:
            序列分析结果字典
        """
        if not self.model_loaded:
            raise RuntimeError("模型未加载，无法进行分析")

        if not log_sequence or len(log_sequence) == 0:
            return {"error": "Empty sequence"}

        try:
            # 提取特征进行分析
            features = self.classifier.extract_all_features(log_sequence, self.classifier.transition_features, self.classifier.reference_sequences)

            # 预测结果
            prediction = self.classifier.predict(log_sequence)

            # 序列统计
            pattern_counts = Counter(log_sequence)

            # 转换分析
            transitions = []
            for i in range(len(log_sequence) - 1):
                transitions.append(f"{log_sequence[i]} -> {log_sequence[i+1]}")

            analysis = {
                "prediction": prediction,
                "sequence_stats": {
                    "length": len(log_sequence),
                    "unique_patterns": len(set(log_sequence)),
                    "diversity_ratio": len(set(log_sequence)) / len(log_sequence),
                    "most_common_pattern": pattern_counts.most_common(1)[0] if pattern_counts else None,
                    "start_pattern": log_sequence[0],
                    "end_pattern": log_sequence[-1],
                },
                "transition_stats": {
                    "num_transitions": len(transitions),
                    "unique_transitions": len(set(transitions)),
                    "transition_diversity": len(set(transitions)) / len(transitions) if transitions else 0,
                },
                "key_features": {
                    "stability_ratio": features.get("stability_ratio", 0),
                    "path_complexity": features.get("path_complexity", 0),
                    "max_success_similarity": features.get("max_success_similarity", 0),
                    "max_failure_similarity": features.get("max_failure_similarity", 0),
                    "similarity_ratio": features.get("similarity_ratio", 0),
                },
            }

            return analysis

        except Exception as e:
            return {"error": str(e)}


def demo_usage():
    """演示如何使用预测器"""
    print("=== 日志序列预测器演示 ===\n")

    # 创建预测器
    predictor = LogSequencePredictor()

    if not predictor.model_loaded:
        print("请先运行训练脚本生成模型文件")
        return

    # 示例序列
    test_sequences = [
        # 模拟的成功序列
        ["drain_pattern_001", "drain_pattern_002", "drain_pattern_003", "drain_pattern_004"],
        # 模拟的失败序列
        ["drain_pattern_005", "drain_pattern_006", "drain_pattern_007"],
        # 更复杂的序列
        ["drain_pattern_001", "drain_pattern_002", "drain_pattern_001", "drain_pattern_003", "drain_pattern_004", "drain_pattern_002", "drain_pattern_005"],
        # 短序列
        ["drain_pattern_001", "drain_pattern_002"],
        # 空序列
        [],
    ]

    print("1. 简单预测:")
    for i, sequence in enumerate(test_sequences):
        result = predictor.predict(sequence)
        print(f"序列 {i+1}: {result}")

    print("\n2. 详细预测:")
    for i, sequence in enumerate(test_sequences):
        if sequence:  # 跳过空序列
            result = predictor.predict(sequence, return_details=True)
            print(f"序列 {i+1}:")
            print(f"  - 预测: {result['prediction']}")
            print(f"  - 置信度: {result['confidence']:.3f}")
            print(f"  - 成功概率: {result['probability_success']:.3f}")
            print(f"  - 失败概率: {result['probability_failure']:.3f}")
            print()

    print("3. 批量预测:")
    batch_results = predictor.predict_batch(test_sequences[:-1])  # 排除空序列
    for i, result in enumerate(batch_results):
        print(f"序列 {i+1}: {result}")

    print("\n4. 序列分析:")
    if test_sequences[2]:  # 分析第三个序列
        analysis = predictor.analyze_sequence(test_sequences[2])
        print("复杂序列的详细分析:")
        print(f"  - 预测结果: {analysis['prediction']['prediction_label']}")
        print(f"  - 序列长度: {analysis['sequence_stats']['length']}")
        print(f"  - 模式多样性: {analysis['sequence_stats']['diversity_ratio']:.3f}")
        print(f"  - 转换多样性: {analysis['transition_stats']['transition_diversity']:.3f}")
        print(f"  - 与成功案例最大相似度: {analysis['key_features']['max_success_similarity']:.3f}")
        print(f"  - 与失败案例最大相似度: {analysis['key_features']['max_failure_similarity']:.3f}")


if __name__ == "__main__":
    demo_usage()
