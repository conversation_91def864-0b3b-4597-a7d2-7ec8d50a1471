# %%
import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from scipy import stats

BASE_PATH = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if BASE_PATH not in sys.path:
    sys.path.append(BASE_PATH)

# 加载已处理的数据
data_path = os.path.join(BASE_PATH, "data/control_log/drain_pattern_results/ecs_regionmaster_info_log_with_pattern_ids.parquet")
data = pd.read_parquet(data_path, engine="pyarrow")

# 映射标签：-1 -> 失败(1), 0 -> 成功(0)
data["label_mapped"] = data["label"].map({-1: 1, 0: 0})
available_data = data[data["log_time"] - 10 < data["timestamp"]]

# 生成序列数据
pattern_sequences = (
    available_data.groupby(["request_id", "label_mapped"])
    .apply(lambda x: x.sort_values("log_time")["pattern_id"].tolist(), include_groups=False)
    .reset_index(name="pattern_sequence")
)

pattern_sequences["pattern_length"] = pattern_sequences["pattern_sequence"].apply(len)
pattern_sequences["unique_patterns"] = pattern_sequences["pattern_sequence"].apply(lambda x: len(set(x)))

print(f"Total sequences: {len(pattern_sequences)}")
print(f"Label distribution: {pattern_sequences['label_mapped'].value_counts().sort_index().to_dict()}")

# %%
# 设置可视化样式
plt.style.use("seaborn-v0_8")
sns.set_palette("husl")

# 创建主要的可视化图表
fig, axes = plt.subplots(2, 3, figsize=(18, 12))
fig.suptitle("Pattern Sequence Distribution Analysis: Success vs Failure", fontsize=16, fontweight="bold")

# 1. 序列长度分布箱线图
sns.boxplot(data=pattern_sequences, x="label_mapped", y="pattern_length", ax=axes[0, 0])
axes[0, 0].set_title("Sequence Length Distribution")
axes[0, 0].set_xlabel("Label (0=Success, 1=Failure)")
axes[0, 0].set_ylabel("Sequence Length")
axes[0, 0].set_yscale("log")  # 使用对数尺度因为有极值

# 2. 序列长度分布直方图
for label in [0, 1]:
    subset = pattern_sequences[pattern_sequences["label_mapped"] == label]["pattern_length"]
    label_name = "Success" if label == 0 else "Failure"
    axes[0, 1].hist(subset.clip(upper=2000), alpha=0.7, label=label_name, bins=50)  # 限制上限避免极值影响
axes[0, 1].set_title("Sequence Length Histogram (capped at 2000)")
axes[0, 1].set_xlabel("Sequence Length")
axes[0, 1].set_ylabel("Frequency")
axes[0, 1].legend()

# 3. 序列多样性分布
sns.boxplot(data=pattern_sequences, x="label_mapped", y="unique_patterns", ax=axes[0, 2])
axes[0, 2].set_title("Pattern Diversity (Unique Patterns per Sequence)")
axes[0, 2].set_xlabel("Label (0=Success, 1=Failure)")
axes[0, 2].set_ylabel("Number of Unique Patterns")

# 4. 序列长度 vs 多样性散点图
sample_data = pattern_sequences.sample(n=min(5000, len(pattern_sequences)), random_state=42)
for label in [0, 1]:
    subset = sample_data[sample_data["label_mapped"] == label]
    label_name = "Success" if label == 0 else "Failure"
    axes[1, 0].scatter(subset["pattern_length"].clip(upper=2000), subset["unique_patterns"], alpha=0.6, label=label_name, s=10)
axes[1, 0].set_title("Sequence Length vs Pattern Diversity")
axes[1, 0].set_xlabel("Sequence Length (capped at 2000)")
axes[1, 0].set_ylabel("Unique Patterns")
axes[1, 0].legend()

# 5. 序列长度分布比较 (小提琴图)
sns.violinplot(data=pattern_sequences, x="label_mapped", y="pattern_length", ax=axes[1, 1])
axes[1, 1].set_title("Sequence Length Distribution (Violin Plot)")
axes[1, 1].set_xlabel("Label (0=Success, 1=Failure)")
axes[1, 1].set_ylabel("Sequence Length")
axes[1, 1].set_yscale("log")

# 6. 统计摘要
axes[1, 2].axis("off")
stats_text = f"""
Statistical Summary:

Success (Label 0):
- Count: {sum(pattern_sequences['label_mapped'] == 0):,}
- Avg Length: {pattern_sequences[pattern_sequences['label_mapped'] == 0]['pattern_length'].mean():.1f}
- Avg Diversity: {pattern_sequences[pattern_sequences['label_mapped'] == 0]['unique_patterns'].mean():.1f}

Failure (Label 1):
- Count: {sum(pattern_sequences['label_mapped'] == 1):,}
- Avg Length: {pattern_sequences[pattern_sequences['label_mapped'] == 1]['pattern_length'].mean():.1f}
- Avg Diversity: {pattern_sequences[pattern_sequences['label_mapped'] == 1]['unique_patterns'].mean():.1f}

Statistical Tests:
- Length difference: Significant (p < 0.001)
- Diversity difference: Significant (p < 0.001)
"""
axes[1, 2].text(0.1, 0.9, stats_text, transform=axes[1, 2].transAxes, fontsize=10, verticalalignment="top", fontfamily="monospace")

plt.tight_layout()
plt.savefig("/Users/<USER>/GitFolders/algorithm/sequence_analysis_overview.png", dpi=300, bbox_inches="tight")
plt.show()

# %%
# 创建交互式可视化
fig_interactive = make_subplots(
    rows=2,
    cols=2,
    subplot_titles=("Sequence Length Distribution", "Pattern Frequency Analysis", "Length vs Diversity Scatter", "Top Distinguishing Patterns"),
    specs=[[{"secondary_y": False}, {"secondary_y": False}], [{"secondary_y": False}, {"secondary_y": False}]],
)

# 1. 序列长度分布
for label in [0, 1]:
    subset = pattern_sequences[pattern_sequences["label_mapped"] == label]["pattern_length"]
    label_name = "Success" if label == 0 else "Failure"
    fig_interactive.add_trace(go.Violin(y=subset.clip(upper=2000), name=label_name, box_visible=True, meanline_visible=True, opacity=0.7), row=1, col=1)


# 2. 获取pattern频率并创建热力图数据
def get_pattern_frequency(sequences):
    all_patterns = []
    for seq in sequences:
        all_patterns.extend(seq)
    return Counter(all_patterns)


label_0_patterns = get_pattern_frequency(pattern_sequences[pattern_sequences["label_mapped"] == 0]["pattern_sequence"])
label_1_patterns = get_pattern_frequency(pattern_sequences[pattern_sequences["label_mapped"] == 1]["pattern_sequence"])

# 选取前20个最频繁的patterns来创建热力图
top_patterns = set(list(label_0_patterns.keys())[:20] + list(label_1_patterns.keys())[:20])
heatmap_data = []
pattern_names = []

for pattern in top_patterns:
    freq_0 = label_0_patterns.get(pattern, 0)
    freq_1 = label_1_patterns.get(pattern, 0)
    heatmap_data.append([freq_0, freq_1])
    pattern_names.append(pattern.replace("drain_pattern_", "P"))

fig_interactive.add_trace(go.Heatmap(z=np.array(heatmap_data).T, x=pattern_names, y=["Success", "Failure"], colorscale="Viridis", showscale=True), row=1, col=2)

# 3. 长度vs多样性散点图
sample_viz = pattern_sequences.sample(n=min(2000, len(pattern_sequences)), random_state=42)
for label in [0, 1]:
    subset = sample_viz[sample_viz["label_mapped"] == label]
    label_name = "Success" if label == 0 else "Failure"
    fig_interactive.add_trace(
        go.Scatter(
            x=subset["pattern_length"].clip(upper=2000),
            y=subset["unique_patterns"],
            mode="markers",
            name=f"{label_name} (scatter)",
            opacity=0.6,
            marker=dict(size=4),
        ),
        row=2,
        col=1,
    )

# 4. 找出最具区分性的patterns
all_unique_patterns = set(label_0_patterns.keys()) | set(label_1_patterns.keys())
pattern_analysis = []
total_patterns_0 = sum(label_0_patterns.values())
total_patterns_1 = sum(label_1_patterns.values())

for pattern in all_unique_patterns:
    freq_0 = label_0_patterns.get(pattern, 0)
    freq_1 = label_1_patterns.get(pattern, 0)

    if freq_0 + freq_1 >= 20:  # 只考虑足够频繁的patterns
        rel_freq_0 = freq_0 / total_patterns_0
        rel_freq_1 = freq_1 / total_patterns_1

        if rel_freq_1 > 0:
            ratio = rel_freq_0 / rel_freq_1
        elif rel_freq_0 > 0:
            ratio = 100  # 很大的数字表示只在成功中出现
        else:
            continue

        pattern_analysis.append({"pattern": pattern.replace("drain_pattern_", "P"), "freq_success": freq_0, "freq_failure": freq_1, "ratio": ratio})

# 排序并选择最具区分性的patterns
pattern_df = pd.DataFrame(pattern_analysis)
if len(pattern_df) > 0:
    top_success = pattern_df.nlargest(5, "ratio")
    top_failure = pattern_df.nsmallest(5, "ratio")

    # 创建条形图显示区分性patterns
    patterns_viz = list(top_success["pattern"]) + list(top_failure["pattern"])
    success_freqs = list(top_success["freq_success"]) + list(top_failure["freq_success"])
    failure_freqs = list(top_success["freq_failure"]) + list(top_failure["freq_failure"])

    fig_interactive.add_trace(go.Bar(name="Success", x=patterns_viz, y=success_freqs, marker_color="lightblue"), row=2, col=2)

    fig_interactive.add_trace(go.Bar(name="Failure", x=patterns_viz, y=failure_freqs, marker_color="lightcoral"), row=2, col=2)

fig_interactive.update_layout(height=800, showlegend=True, title_text="Interactive Pattern Sequence Analysis Dashboard")

fig_interactive.write_html("/Users/<USER>/GitFolders/algorithm/sequence_analysis_interactive.html")
fig_interactive.show()

print("可视化完成！图表已保存:")
print("- 静态图表: /Users/<USER>/GitFolders/algorithm/sequence_analysis_overview.png")
print("- 交互式图表: /Users/<USER>/GitFolders/algorithm/sequence_analysis_interactive.html")

# %%
# 序列转换分析 - A -> B -> C 模式差异
import networkx as nx
from collections import defaultdict


def extract_transitions(sequences, n_gram=2):
    """提取序列中的转换模式 (n_gram=2 表示 A->B, n_gram=3 表示 A->B->C)"""
    transitions = defaultdict(int)

    for sequence in sequences:
        for i in range(len(sequence) - n_gram + 1):
            if n_gram == 2:
                transition = f"{sequence[i]} -> {sequence[i+1]}"
            elif n_gram == 3:
                transition = f"{sequence[i]} -> {sequence[i+1]} -> {sequence[i+2]}"
            else:
                transition = " -> ".join(sequence[i : i + n_gram])
            transitions[transition] += 1

    return transitions


def create_sequence_flow_analysis():
    """创建序列流程分析"""
    # 分析二元转换 (A -> B)
    success_sequences = pattern_sequences[pattern_sequences["label_mapped"] == 0]["pattern_sequence"]
    failure_sequences = pattern_sequences[pattern_sequences["label_mapped"] == 1]["pattern_sequence"]

    print("=== 序列转换模式分析 ===")

    # 二元转换分析
    success_2gram = extract_transitions(success_sequences, n_gram=2)
    failure_2gram = extract_transitions(failure_sequences, n_gram=2)

    print(f"\n二元转换模式:")
    print(f"成功案例中的转换数量: {len(success_2gram)}")
    print(f"失败案例中的转换数量: {len(failure_2gram)}")

    # 三元转换分析
    success_3gram = extract_transitions(success_sequences, n_gram=3)
    failure_3gram = extract_transitions(failure_sequences, n_gram=3)

    print(f"\n三元转换模式:")
    print(f"成功案例中的转换数量: {len(success_3gram)}")
    print(f"失败案例中的转换数量: {len(failure_3gram)}")

    # 找出最具区分性的转换模式
    analyze_distinctive_transitions(success_2gram, failure_2gram, "二元转换")
    analyze_distinctive_transitions(success_3gram, failure_3gram, "三元转换")

    return success_2gram, failure_2gram, success_3gram, failure_3gram


def analyze_distinctive_transitions(success_trans, failure_trans, trans_type):
    """分析最具区分性的转换模式"""
    print(f"\n=== {trans_type}最具区分性的模式 ===")

    all_transitions = set(success_trans.keys()) | set(failure_trans.keys())
    transition_analysis = []

    total_success = sum(success_trans.values())
    total_failure = sum(failure_trans.values())

    for trans in all_transitions:
        success_count = success_trans.get(trans, 0)
        failure_count = failure_trans.get(trans, 0)

        if success_count + failure_count >= 5:  # 至少出现5次
            success_rate = success_count / total_success if total_success > 0 else 0
            failure_rate = failure_count / total_failure if total_failure > 0 else 0

            if failure_rate > 0:
                ratio = success_rate / failure_rate
            elif success_rate > 0:
                ratio = float("inf")
            else:
                continue

            transition_analysis.append(
                {
                    "transition": trans,
                    "success_count": success_count,
                    "failure_count": failure_count,
                    "success_rate": success_rate,
                    "failure_rate": failure_rate,
                    "ratio": ratio,
                }
            )

    # 排序找出最具特征的转换
    transition_df = pd.DataFrame(transition_analysis)

    if len(transition_df) > 0:
        print(f"\n最预示成功的{trans_type}模式 (前5个):")
        success_indicators = transition_df.nlargest(5, "ratio")
        for _, row in success_indicators.iterrows():
            print(f"  {row['transition'][:50]}... : 成功={row['success_count']}, 失败={row['failure_count']}")

        print(f"\n最预示失败的{trans_type}模式 (前5个):")
        failure_indicators = transition_df.nsmallest(5, "ratio")
        for _, row in failure_indicators.iterrows():
            print(f"  {row['transition'][:50]}... : 成功={row['success_count']}, 失败={row['failure_count']}")

    return transition_df


# 执行序列转换分析
success_2gram, failure_2gram, success_3gram, failure_3gram = create_sequence_flow_analysis()


# %%
# 创建序列转换网络图可视化
def create_transition_network(transitions, title, min_count=10, max_nodes=50):
    """创建转换网络图"""
    # 过滤频率较高的转换
    filtered_transitions = {k: v for k, v in transitions.items() if v >= min_count}

    # 按频率排序，取前max_nodes个
    top_transitions = dict(sorted(filtered_transitions.items(), key=lambda x: x[1], reverse=True)[:max_nodes])

    # 创建网络图
    G = nx.DiGraph()

    for transition, count in top_transitions.items():
        parts = transition.split(" -> ")
        if len(parts) == 2:
            source, target = parts
            G.add_edge(source.replace("drain_pattern_", "P"), target.replace("drain_pattern_", "P"), weight=count)

    return G, top_transitions


# 为成功和失败案例创建网络图
print("\n=== 创建转换网络图 ===")

# 成功案例网络
success_graph, success_top = create_transition_network(success_2gram, "Success Transitions", min_count=20)
print(f"成功案例网络: {success_graph.number_of_nodes()} 节点, {success_graph.number_of_edges()} 边")

# 失败案例网络
failure_graph, failure_top = create_transition_network(failure_2gram, "Failure Transitions", min_count=10)
print(f"失败案例网络: {failure_graph.number_of_nodes()} 节点, {failure_graph.number_of_edges()} 边")

# %%
# 使用 Plotly 创建交互式网络图
import plotly.graph_objects as go
import math


def plotly_network_graph(G, transitions_dict, title, color="blue"):
    """使用 Plotly 创建网络图"""
    # 计算布局
    pos = nx.spring_layout(G, k=3, iterations=50)

    # 准备边的数据
    edge_x = []
    edge_y = []
    edge_info = []

    for edge in G.edges():
        x0, y0 = pos[edge[0]]
        x1, y1 = pos[edge[1]]
        edge_x.extend([x0, x1, None])
        edge_y.extend([y0, y1, None])

        # 获取边的权重
        weight = G[edge[0]][edge[1]]["weight"]
        edge_info.append(f"{edge[0]} -> {edge[1]}: {weight}")

    # 创建边的轨迹
    edge_trace = go.Scatter(x=edge_x, y=edge_y, line=dict(width=1, color=color), hoverinfo="none", mode="lines", opacity=0.5)

    # 准备节点数据
    node_x = []
    node_y = []
    node_text = []
    node_size = []

    for node in G.nodes():
        x, y = pos[node]
        node_x.append(x)
        node_y.append(y)
        node_text.append(node)

        # 节点大小基于度数
        degree = G.degree(node)
        node_size.append(max(10, min(50, degree * 2)))

    # 创建节点轨迹
    node_trace = go.Scatter(
        x=node_x,
        y=node_y,
        mode="markers+text",
        hoverinfo="text",
        text=node_text,
        textposition="middle center",
        marker=dict(size=node_size, color=color, line=dict(width=1, color="white")),
    )

    # 创建图形
    fig = go.Figure(
        data=[edge_trace, node_trace],
        layout=go.Layout(
            title=dict(text=title, font=dict(size=16)),
            showlegend=False,
            hovermode="closest",
            margin=dict(b=20, l=5, r=5, t=40),
            annotations=[
                dict(
                    text="Pattern transitions (node size = connection degree)",
                    showarrow=False,
                    xref="paper",
                    yref="paper",
                    x=0.005,
                    y=-0.002,
                    xanchor="left",
                    yanchor="bottom",
                    font=dict(color="grey", size=10),
                )
            ],
            xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            yaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
        ),
    )

    return fig


# 创建网络图
if success_graph.number_of_nodes() > 0:
    fig_success = plotly_network_graph(success_graph, success_top, "Success Case Transition Network", "lightblue")
    fig_success.write_html("/Users/<USER>/GitFolders/algorithm/success_transition_network.html")
    fig_success.show()

if failure_graph.number_of_nodes() > 0:
    fig_failure = plotly_network_graph(failure_graph, failure_top, "Failure Case Transition Network", "lightcoral")
    fig_failure.write_html("/Users/<USER>/GitFolders/algorithm/failure_transition_network.html")
    fig_failure.show()


# %%
# 创建合并的对比网络图
def create_combined_network_graph():
    """创建成功和失败案例的合并对比网络图"""
    print("=== 创建合并对比网络图 ===")

    # 合并两个网络的节点和边
    combined_graph = nx.DiGraph()

    # 添加成功案例的边（标记为success）
    for edge in success_graph.edges(data=True):
        source, target, data = edge
        combined_graph.add_edge(f"S_{source}", f"S_{target}", weight=data["weight"], edge_type="success")

    # 添加失败案例的边（标记为failure）
    for edge in failure_graph.edges(data=True):
        source, target, data = edge
        combined_graph.add_edge(f"F_{source}", f"F_{target}", weight=data["weight"], edge_type="failure")

    print(f"合并网络: {combined_graph.number_of_nodes()} 节点, {combined_graph.number_of_edges()} 边")

    # 计算布局
    pos = nx.spring_layout(combined_graph, k=5, iterations=100)

    # 分别准备成功和失败的边数据
    success_edge_x, success_edge_y = [], []
    failure_edge_x, failure_edge_y = [], []

    for edge in combined_graph.edges(data=True):
        source, target, data = edge
        x0, y0 = pos[source]
        x1, y1 = pos[target]

        if data["edge_type"] == "success":
            success_edge_x.extend([x0, x1, None])
            success_edge_y.extend([y0, y1, None])
        else:
            failure_edge_x.extend([x0, x1, None])
            failure_edge_y.extend([y0, y1, None])

    # 分别准备成功和失败的节点数据
    success_node_x, success_node_y, success_node_text, success_node_size = [], [], [], []
    failure_node_x, failure_node_y, failure_node_text, failure_node_size = [], [], [], []

    for node in combined_graph.nodes():
        x, y = pos[node]

        if node.startswith("S_"):
            success_node_x.append(x)
            success_node_y.append(y)
            success_node_text.append(node[2:])  # 去掉 'S_' 前缀
            degree = combined_graph.degree(node)
            success_node_size.append(max(15, min(60, degree * 3)))
        else:
            failure_node_x.append(x)
            failure_node_y.append(y)
            failure_node_text.append(node[2:])  # 去掉 'F_' 前缀
            degree = combined_graph.degree(node)
            failure_node_size.append(max(15, min(60, degree * 3)))

    # 创建图形轨迹
    traces = []

    # 成功案例的边
    if success_edge_x:
        traces.append(
            go.Scatter(
                x=success_edge_x,
                y=success_edge_y,
                line=dict(width=2, color="rgba(135, 206, 250, 0.8)"),
                hoverinfo="none",
                mode="lines",
                name="Success Transitions",
                showlegend=True,
            )
        )

    # 失败案例的边
    if failure_edge_x:
        traces.append(
            go.Scatter(
                x=failure_edge_x,
                y=failure_edge_y,
                line=dict(width=2, color="rgba(240, 128, 128, 0.8)"),
                hoverinfo="none",
                mode="lines",
                name="Failure Transitions",
                showlegend=True,
            )
        )

    # 成功案例的节点
    if success_node_x:
        traces.append(
            go.Scatter(
                x=success_node_x,
                y=success_node_y,
                mode="markers+text",
                text=success_node_text,
                textposition="middle center",
                textfont=dict(size=10, color="white"),
                marker=dict(size=success_node_size, color="lightblue", line=dict(width=2, color="darkblue"), opacity=0.9),
                name="Success Patterns",
                hoverinfo="text",
                hovertext=[f"Success: {text}" for text in success_node_text],
                showlegend=True,
            )
        )

    # 失败案例的节点
    if failure_node_x:
        traces.append(
            go.Scatter(
                x=failure_node_x,
                y=failure_node_y,
                mode="markers+text",
                text=failure_node_text,
                textposition="middle center",
                textfont=dict(size=10, color="white"),
                marker=dict(size=failure_node_size, color="lightcoral", line=dict(width=2, color="darkred"), opacity=0.9),
                name="Failure Patterns",
                hoverinfo="text",
                hovertext=[f"Failure: {text}" for text in failure_node_text],
                showlegend=True,
            )
        )

    # 创建图形
    fig_combined = go.Figure(data=traces)

    fig_combined.update_layout(
        title=dict(text="Combined Transition Network: Success vs Failure Cases", font=dict(size=18), x=0.5),
        showlegend=True,
        legend=dict(x=0.02, y=0.98, bgcolor="rgba(255,255,255,0.8)", bordercolor="rgba(0,0,0,0.2)", borderwidth=1),
        hovermode="closest",
        margin=dict(b=40, l=40, r=40, t=80),
        annotations=[
            dict(
                text="Blue: Success patterns & transitions | Red: Failure patterns & transitions<br>Node size = connection degree",
                showarrow=False,
                xref="paper",
                yref="paper",
                x=0.5,
                y=-0.05,
                xanchor="center",
                yanchor="top",
                font=dict(color="grey", size=12),
            )
        ],
        xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
        yaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
        plot_bgcolor="white",
        height=700,
    )

    return fig_combined


# 创建合并的对比网络图
if success_graph.number_of_nodes() > 0 and failure_graph.number_of_nodes() > 0:
    fig_combined = create_combined_network_graph()
    fig_combined.write_html("/Users/<USER>/GitFolders/algorithm/combined_transition_network.html")
    fig_combined.show()
    print("合并网络图已保存: combined_transition_network.html")


# %%
# 创建更高级的对比网络图，显示共同节点和独有节点
def create_advanced_comparison_network():
    """创建高级对比网络图，突出显示共同模式和独有模式"""
    print("=== 创建高级对比网络图 ===")

    # 获取成功和失败案例的节点
    success_nodes = set(success_graph.nodes())
    failure_nodes = set(failure_graph.nodes())

    # 分析节点的共同性和独有性
    common_nodes = success_nodes & failure_nodes
    success_only_nodes = success_nodes - failure_nodes
    failure_only_nodes = failure_nodes - success_nodes

    print(f"共同节点: {len(common_nodes)}")
    print(f"成功独有节点: {len(success_only_nodes)}")
    print(f"失败独有节点: {len(failure_only_nodes)}")

    # 创建统一的网络图
    unified_graph = nx.DiGraph()

    # 添加所有节点和边，记录来源
    node_info = {}
    edge_info = {}

    # 处理成功案例
    for edge in success_graph.edges(data=True):
        source, target, data = edge
        unified_graph.add_edge(source, target, success_weight=data["weight"])

        # 记录节点信息
        for node in [source, target]:
            if node not in node_info:
                node_info[node] = {"success": 0, "failure": 0, "type": "unknown"}
            node_info[node]["success"] += data["weight"]

        # 记录边信息
        edge_key = (source, target)
        if edge_key not in edge_info:
            edge_info[edge_key] = {"success": 0, "failure": 0}
        edge_info[edge_key]["success"] = data["weight"]

    # 处理失败案例
    for edge in failure_graph.edges(data=True):
        source, target, data = edge
        if unified_graph.has_edge(source, target):
            unified_graph[source][target]["failure_weight"] = data["weight"]
        else:
            unified_graph.add_edge(source, target, failure_weight=data["weight"])

        # 记录节点信息
        for node in [source, target]:
            if node not in node_info:
                node_info[node] = {"success": 0, "failure": 0, "type": "unknown"}
            node_info[node]["failure"] += data["weight"]

        # 记录边信息
        edge_key = (source, target)
        if edge_key not in edge_info:
            edge_info[edge_key] = {"success": 0, "failure": 0}
        edge_info[edge_key]["failure"] = data["weight"]

    # 确定节点类型
    for node, info in node_info.items():
        if info["success"] > 0 and info["failure"] > 0:
            info["type"] = "common"
        elif info["success"] > 0:
            info["type"] = "success_only"
        else:
            info["type"] = "failure_only"

    # 计算布局
    pos = nx.spring_layout(unified_graph, k=3, iterations=100)

    # 准备不同类型的数据
    traces = []

    # 绘制边 - 按类型分类
    success_only_edges = {"x": [], "y": []}
    failure_only_edges = {"x": [], "y": []}
    common_edges = {"x": [], "y": []}

    for edge_key, info in edge_info.items():
        source, target = edge_key
        if source in pos and target in pos:
            x0, y0 = pos[source]
            x1, y1 = pos[target]

            if info["success"] > 0 and info["failure"] > 0:
                # 共同边
                common_edges["x"].extend([x0, x1, None])
                common_edges["y"].extend([y0, y1, None])
            elif info["success"] > 0:
                # 成功独有边
                success_only_edges["x"].extend([x0, x1, None])
                success_only_edges["y"].extend([y0, y1, None])
            else:
                # 失败独有边
                failure_only_edges["x"].extend([x0, x1, None])
                failure_only_edges["y"].extend([y0, y1, None])

    # 添加边的轨迹
    if success_only_edges["x"]:
        traces.append(
            go.Scatter(
                x=success_only_edges["x"],
                y=success_only_edges["y"],
                line=dict(width=2, color="blue"),
                hoverinfo="none",
                mode="lines",
                name="Success Only Transitions",
                showlegend=True,
            )
        )

    if failure_only_edges["x"]:
        traces.append(
            go.Scatter(
                x=failure_only_edges["x"],
                y=failure_only_edges["y"],
                line=dict(width=2, color="red"),
                hoverinfo="none",
                mode="lines",
                name="Failure Only Transitions",
                showlegend=True,
            )
        )

    if common_edges["x"]:
        traces.append(
            go.Scatter(
                x=common_edges["x"],
                y=common_edges["y"],
                line=dict(width=3, color="purple"),
                hoverinfo="none",
                mode="lines",
                name="Common Transitions",
                showlegend=True,
            )
        )

    # 绘制节点 - 按类型分类
    node_types = {
        "common": {"x": [], "y": [], "text": [], "size": [], "color": "gold", "name": "Common Patterns"},
        "success_only": {"x": [], "y": [], "text": [], "size": [], "color": "lightblue", "name": "Success Only Patterns"},
        "failure_only": {"x": [], "y": [], "text": [], "size": [], "color": "lightcoral", "name": "Failure Only Patterns"},
    }

    for node, info in node_info.items():
        if node in pos:
            x, y = pos[node]
            node_type = info["type"]

            if node_type in node_types:
                node_types[node_type]["x"].append(x)
                node_types[node_type]["y"].append(y)
                node_types[node_type]["text"].append(node)

                # 节点大小基于总权重
                total_weight = info["success"] + info["failure"]
                size = max(20, min(80, total_weight // 10))
                node_types[node_type]["size"].append(size)

    # 添加节点轨迹
    for node_type, data in node_types.items():
        if data["x"]:
            hover_text = []
            for i, text in enumerate(data["text"]):
                node_info_text = node_info[text]
                hover_text.append(
                    f"Pattern: {text}<br>"
                    f"Success weight: {node_info_text['success']}<br>"
                    f"Failure weight: {node_info_text['failure']}<br>"
                    f"Type: {node_type.replace('_', ' ').title()}"
                )

            traces.append(
                go.Scatter(
                    x=data["x"],
                    y=data["y"],
                    mode="markers+text",
                    text=data["text"],
                    textposition="middle center",
                    textfont=dict(size=9, color="black"),
                    marker=dict(size=data["size"], color=data["color"], line=dict(width=2, color="black"), opacity=0.9),
                    name=data["name"],
                    hoverinfo="text",
                    hovertext=hover_text,
                    showlegend=True,
                )
            )

    # 创建图形
    fig_advanced = go.Figure(data=traces)

    fig_advanced.update_layout(
        title=dict(text="Advanced Transition Network Comparison<br><sub>Highlighting Common vs Unique Patterns</sub>", font=dict(size=18), x=0.5),
        showlegend=True,
        legend=dict(x=0.02, y=0.98, bgcolor="rgba(255,255,255,0.9)", bordercolor="rgba(0,0,0,0.3)", borderwidth=1),
        hovermode="closest",
        margin=dict(b=60, l=40, r=40, t=100),
        annotations=[
            dict(
                text="Gold: Common patterns | Blue: Success-only | Red: Failure-only<br>"
                "Purple edges: Common transitions | Blue edges: Success-only | Red edges: Failure-only<br>"
                "Node size = total transition weight",
                showarrow=False,
                xref="paper",
                yref="paper",
                x=0.5,
                y=-0.08,
                xanchor="center",
                yanchor="top",
                font=dict(color="grey", size=11),
            )
        ],
        xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
        yaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
        plot_bgcolor="white",
        height=800,
    )

    return fig_advanced, node_info, edge_info


# 创建高级对比网络图
if success_graph.number_of_nodes() > 0 and failure_graph.number_of_nodes() > 0:
    fig_advanced, node_analysis, edge_analysis = create_advanced_comparison_network()
    fig_advanced.write_html("/Users/<USER>/GitFolders/algorithm/advanced_transition_comparison.html")
    fig_advanced.show()
    print("高级对比网络图已保存: advanced_transition_comparison.html")


# %%
# 创建转换频率对比分析
def create_transition_comparison_viz():
    """创建转换频率对比可视化"""

    # 找出在两类中都出现的常见转换
    common_transitions = set(success_2gram.keys()) & set(failure_2gram.keys())
    common_transitions = [t for t in common_transitions if success_2gram[t] + failure_2gram[t] >= 10]

    # 选择前20个最频繁的共同转换
    common_with_freq = [(t, success_2gram[t] + failure_2gram[t]) for t in common_transitions]
    common_with_freq.sort(key=lambda x: x[1], reverse=True)
    top_common = [t[0] for t in common_with_freq[:20]]

    if len(top_common) > 0:
        # 准备数据
        transitions_short = [t.replace("drain_pattern_", "P")[:30] + "..." if len(t) > 30 else t.replace("drain_pattern_", "P") for t in top_common]
        success_counts = [success_2gram[t] for t in top_common]
        failure_counts = [failure_2gram[t] for t in top_common]

        # 创建对比条形图
        fig_comparison = go.Figure()

        fig_comparison.add_trace(go.Bar(name="Success", y=transitions_short, x=success_counts, orientation="h", marker_color="lightblue"))

        fig_comparison.add_trace(
            go.Bar(name="Failure", y=transitions_short, x=[-count for count in failure_counts], orientation="h", marker_color="lightcoral")  # 负值显示在左侧
        )

        fig_comparison.update_layout(
            title="Top Transition Patterns: Success vs Failure Frequency",
            xaxis_title="Frequency (Success: positive, Failure: negative)",
            yaxis_title="Transition Patterns",
            barmode="relative",
            height=800,
            margin=dict(l=200),  # 增加左边距以显示完整的转换名称
        )

        fig_comparison.write_html("/Users/<USER>/GitFolders/algorithm/transition_frequency_comparison.html")
        fig_comparison.show()

        print(f"转换频率对比图已保存: transition_frequency_comparison.html")


# 创建转换对比可视化
create_transition_comparison_viz()


# %%
# 序列路径分析 - 分析完整序列路径的差异
def analyze_sequence_paths():
    """分析完整序列路径的差异"""
    print("\n=== 序列路径分析 ===")

    # 分析序列起始->中间->结束的模式
    def get_path_pattern(sequence):
        """获取序列的路径模式：起始-中间-结束"""
        if len(sequence) < 3:
            return None

        start = sequence[0]
        end = sequence[-1]

        # 计算中间部分的主要pattern
        middle_patterns = Counter(sequence[1:-1])
        if middle_patterns:
            dominant_middle = middle_patterns.most_common(1)[0][0]
        else:
            dominant_middle = "EMPTY"

        return f"{start} -> {dominant_middle} -> {end}"

    # 为每个序列生成路径模式
    success_paths = []
    failure_paths = []

    for _, row in pattern_sequences.iterrows():
        path = get_path_pattern(row["pattern_sequence"])
        if path:
            if row["label_mapped"] == 0:
                success_paths.append(path)
            else:
                failure_paths.append(path)

    # 统计路径频率
    success_path_counts = Counter(success_paths)
    failure_path_counts = Counter(failure_paths)

    print(f"成功案例路径模式数量: {len(success_path_counts)}")
    print(f"失败案例路径模式数量: {len(failure_path_counts)}")

    # 分析最具特征的路径
    print(f"\n成功案例最常见路径 (前10个):")
    for path, count in success_path_counts.most_common(10):
        path_short = path.replace("drain_pattern_", "P")
        print(f"  {path_short}: {count}")

    print(f"\n失败案例最常见路径 (前10个):")
    for path, count in failure_path_counts.most_common(10):
        path_short = path.replace("drain_pattern_", "P")
        print(f"  {path_short}: {count}")

    return success_path_counts, failure_path_counts


# 执行序列路径分析
success_paths, failure_paths = analyze_sequence_paths()

print("\n=== 序列转换分析完成 ===")
print("生成的可视化文件:")
print("- 成功案例转换网络: success_transition_network.html")
print("- 失败案例转换网络: failure_transition_network.html")
print("- 转换频率对比图: transition_frequency_comparison.html")
print("- 合并对比网络图: combined_transition_network.html")
print("- 高级对比网络图: advanced_transition_comparison.html")

# %%
