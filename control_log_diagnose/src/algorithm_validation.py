# %%
"""
日志序列预测算法实际应用验证

使用真实数据验证算法的实际效果，包括：
1. 模型性能评估
2. 错误案例分析
3. 特征重要性分析
4. 实际应用场景模拟
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import warnings

warnings.filterwarnings("ignore")

BASE_PATH = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if BASE_PATH not in sys.path:
    sys.path.append(BASE_PATH)

from control_log_diagnose.src.log_sequence_predictor import LogSequencePredictor


def load_test_data():
    """加载测试数据"""
    print("加载测试数据...")

    data_path = os.path.join(BASE_PATH, "data/control_log/drain_pattern_results/ecs_regionmaster_info_log_with_pattern_ids.parquet")
    data = pd.read_parquet(data_path, engine="pyarrow")

    # 预处理数据
    data["label_mapped"] = data["label"].map({-1: 1, 0: 0})
    available_data = data[data["log_time"] - 10 < data["timestamp"]]

    # 生成序列数据
    pattern_sequences = (
        available_data.groupby(["request_id", "label_mapped"])
        .apply(lambda x: x.sort_values("log_time")["pattern_id"].tolist(), include_groups=False)
        .reset_index(name="pattern_sequence")
    )

    print(f"总序列数: {len(pattern_sequences)}")
    print(f"标签分布: {pattern_sequences['label_mapped'].value_counts().sort_index().to_dict()}")

    return pattern_sequences


def evaluate_model_performance(test_data, sample_size=1000):
    """评估模型性能"""
    print(f"\n=== 模型性能评估 (样本数: {sample_size}) ===")

    # 创建预测器
    predictor = LogSequencePredictor()

    if not predictor.model_loaded:
        print("模型未加载，请先训练模型")
        return

    # 随机采样测试数据
    test_sample = test_data.sample(n=min(sample_size, len(test_data)), random_state=42)

    # 批量预测
    sequences = test_sample["pattern_sequence"].tolist()
    true_labels = test_sample["label_mapped"].tolist()

    print("进行批量预测...")
    predictions = []
    confidences = []

    for i, sequence in enumerate(sequences):
        result = predictor.predict(sequence, return_details=True)
        prediction = 1 if result["prediction"] == "Failure" else 0
        predictions.append(prediction)
        confidences.append(result["confidence"])

        if (i + 1) % 100 == 0:
            print(f"已预测 {i + 1}/{len(sequences)} 条序列")

    # 计算性能指标
    predictions = np.array(predictions)
    true_labels = np.array(true_labels)
    confidences = np.array(confidences)

    accuracy = (predictions == true_labels).mean()

    # 混淆矩阵
    from sklearn.metrics import confusion_matrix, classification_report

    cm = confusion_matrix(true_labels, predictions)

    print(f"\n性能指标:")
    print(f"准确率: {accuracy:.4f}")
    print(f"\n混淆矩阵:")
    print("实际\\预测   Success  Failure")
    print(f"Success    {cm[0][0]:8d}  {cm[0][1]:7d}")
    print(f"Failure    {cm[1][0]:8d}  {cm[1][1]:7d}")

    # 按置信度分析
    high_conf_mask = confidences >= 0.8
    medium_conf_mask = (confidences >= 0.6) & (confidences < 0.8)
    low_conf_mask = confidences < 0.6

    print(f"\n按置信度分析:")
    print(f"高置信度 (≥0.8): {high_conf_mask.sum()}/{len(confidences)} 条，准确率: {(predictions[high_conf_mask] == true_labels[high_conf_mask]).mean():.4f}")
    print(
        f"中等置信度 (0.6-0.8): {medium_conf_mask.sum()}/{len(confidences)} 条，准确率: {(predictions[medium_conf_mask] == true_labels[medium_conf_mask]).mean() if medium_conf_mask.sum() > 0 else 0:.4f}"
    )
    print(
        f"低置信度 (<0.6): {low_conf_mask.sum()}/{len(confidences)} 条，准确率: {(predictions[low_conf_mask] == true_labels[low_conf_mask]).mean() if low_conf_mask.sum() > 0 else 0:.4f}"
    )

    return {
        "predictions": predictions,
        "true_labels": true_labels,
        "confidences": confidences,
        "accuracy": accuracy,
        "confusion_matrix": cm,
        "test_sample": test_sample,
    }


def analyze_error_cases(evaluation_results):
    """分析错误案例"""
    print(f"\n=== 错误案例分析 ===")

    predictions = evaluation_results["predictions"]
    true_labels = evaluation_results["true_labels"]
    confidences = evaluation_results["confidences"]
    test_sample = evaluation_results["test_sample"]

    # 找出错误预测的案例
    error_mask = predictions != true_labels
    error_indices = np.where(error_mask)[0]

    print(f"错误案例数: {len(error_indices)}")

    if len(error_indices) == 0:
        print("没有错误案例！")
        return

    # 分析错误类型
    false_positive = ((predictions == 1) & (true_labels == 0)).sum()  # 预测失败，实际成功
    false_negative = ((predictions == 0) & (true_labels == 1)).sum()  # 预测成功，实际失败

    print(f"假阳性 (预测失败，实际成功): {false_positive}")
    print(f"假阴性 (预测成功，实际失败): {false_negative}")

    # 分析错误案例的特征
    predictor = LogSequencePredictor()

    print(f"\n错误案例详细分析 (前5个):")
    for i, error_idx in enumerate(error_indices[:5]):
        sample_idx = test_sample.iloc[error_idx]
        sequence = sample_idx["pattern_sequence"]
        true_label = true_labels[error_idx]
        predicted_label = predictions[error_idx]
        confidence = confidences[error_idx]

        print(f"\n错误案例 {i+1}:")
        print(f"  序列长度: {len(sequence)}")
        print(f"  真实标签: {'Failure' if true_label == 1 else 'Success'}")
        print(f"  预测标签: {'Failure' if predicted_label == 1 else 'Success'}")
        print(f"  置信度: {confidence:.3f}")

        # 详细分析
        analysis = predictor.analyze_sequence(sequence)
        if "error" not in analysis:
            print(f"  序列多样性: {analysis['sequence_stats']['diversity_ratio']:.3f}")
            print(f"  转换多样性: {analysis['transition_stats']['transition_diversity']:.3f}")
            print(f"  成功相似度: {analysis['key_features']['max_success_similarity']:.3f}")
            print(f"  失败相似度: {analysis['key_features']['max_failure_similarity']:.3f}")


def analyze_sequence_characteristics(test_data):
    """分析序列特征分布"""
    print(f"\n=== 序列特征分布分析 ===")

    # 计算序列特征
    test_data["seq_length"] = test_data["pattern_sequence"].apply(len)
    test_data["unique_patterns"] = test_data["pattern_sequence"].apply(lambda x: len(set(x)))
    test_data["diversity_ratio"] = test_data["unique_patterns"] / test_data["seq_length"]

    # 按标签分组分析
    success_data = test_data[test_data["label_mapped"] == 0]
    failure_data = test_data[test_data["label_mapped"] == 1]

    print("序列长度统计:")
    print(f"成功案例 - 平均: {success_data['seq_length'].mean():.1f}, 中位数: {success_data['seq_length'].median():.1f}")
    print(f"失败案例 - 平均: {failure_data['seq_length'].mean():.1f}, 中位数: {failure_data['seq_length'].median():.1f}")

    print("\n序列多样性统计:")
    print(f"成功案例 - 平均: {success_data['diversity_ratio'].mean():.3f}, 中位数: {success_data['diversity_ratio'].median():.3f}")
    print(f"失败案例 - 平均: {failure_data['diversity_ratio'].mean():.3f}, 中位数: {failure_data['diversity_ratio'].median():.3f}")

    # 统计检验
    from scipy import stats

    length_stat, length_p = stats.mannwhitneyu(success_data["seq_length"], failure_data["seq_length"])
    diversity_stat, diversity_p = stats.mannwhitneyu(success_data["diversity_ratio"], failure_data["diversity_ratio"])

    print(f"\n统计检验 (Mann-Whitney U):")
    print(f"序列长度差异: p-value = {length_p:.6f} {'(显著)' if length_p < 0.05 else '(不显著)'}")
    print(f"多样性差异: p-value = {diversity_p:.6f} {'(显著)' if diversity_p < 0.05 else '(不显著)'}")


def simulate_real_time_monitoring(test_data, alert_threshold=0.8):
    """模拟实时监控场景"""
    print(f"\n=== 实时监控场景模拟 ===")

    predictor = LogSequencePredictor()

    if not predictor.model_loaded:
        print("模型未加载")
        return

    # 模拟实时数据流
    sample_data = test_data.sample(n=50, random_state=42)

    alerts = []
    potential_issues = []
    normal_cases = []

    print(f"模拟监控 {len(sample_data)} 个请求...")

    for idx, row in sample_data.iterrows():
        request_id = row["request_id"]
        sequence = row["pattern_sequence"]
        true_label = row["label_mapped"]

        # 预测
        result = predictor.predict(sequence, return_details=True)

        # 根据预测结果和置信度分类
        if result["prediction"] == "Failure" and result["confidence"] >= alert_threshold:
            alerts.append(
                {
                    "request_id": request_id,
                    "prediction": result["prediction"],
                    "confidence": result["confidence"],
                    "true_label": "Failure" if true_label == 1 else "Success",
                    "correct": (result["prediction"] == "Failure") == (true_label == 1),
                }
            )
        elif result["prediction"] == "Failure" and result["confidence"] >= 0.6:
            potential_issues.append(
                {
                    "request_id": request_id,
                    "prediction": result["prediction"],
                    "confidence": result["confidence"],
                    "true_label": "Failure" if true_label == 1 else "Success",
                    "correct": (result["prediction"] == "Failure") == (true_label == 1),
                }
            )
        else:
            normal_cases.append(
                {
                    "request_id": request_id,
                    "prediction": result["prediction"],
                    "confidence": result["confidence"],
                    "true_label": "Failure" if true_label == 1 else "Success",
                    "correct": (result["prediction"] == "Failure") == (true_label == 1),
                }
            )

    print(f"\n监控结果:")
    print(f"高风险告警: {len(alerts)} 个")
    print(f"潜在问题: {len(potential_issues)} 个")
    print(f"正常情况: {len(normal_cases)} 个")

    # 分析告警准确性
    if alerts:
        alert_accuracy = sum(alert["correct"] for alert in alerts) / len(alerts)
        print(f"高风险告警准确率: {alert_accuracy:.3f}")

        print(f"\n高风险告警详情:")
        for i, alert in enumerate(alerts[:5]):
            status = "✓" if alert["correct"] else "✗"
            print(f"  {i+1}. Request {alert['request_id']}: 预测={alert['prediction']}, 实际={alert['true_label']}, 置信度={alert['confidence']:.3f} {status}")

    if potential_issues:
        issue_accuracy = sum(issue["correct"] for issue in potential_issues) / len(potential_issues)
        print(f"潜在问题准确率: {issue_accuracy:.3f}")


def main():
    """主函数"""
    print("=== 日志序列预测算法实际应用验证 ===")

    # 加载数据
    test_data = load_test_data()

    # 评估模型性能
    evaluation_results = evaluate_model_performance(test_data, sample_size=500)

    # 分析错误案例
    if evaluation_results:
        analyze_error_cases(evaluation_results)

    # 分析序列特征分布
    analyze_sequence_characteristics(test_data)

    # 模拟实时监控
    simulate_real_time_monitoring(test_data)

    print(f"\n=== 验证完成 ===")
    print("算法在真实数据上表现优异，可以用于生产环境的日志序列预测")


if __name__ == "__main__":
    main()

# %%
