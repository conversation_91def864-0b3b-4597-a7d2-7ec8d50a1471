# %%
"""
日志序列失败原因分析器

基于关键转换和关键节点的失败原因识别算法：
1. 关键转换识别 - 找出最能预示失败的转换模式
2. 关键节点分析 - 识别失败路径中的决策点
3. 失败模式聚类 - 按失败原因分组
4. 对比分析 - 与成功案例对比找差异
5. 可解释性报告 - 生成人类可读的失败原因分析
"""

import os
import sys
import pandas as pd
import numpy as np
from collections import Counter, defaultdict
import networkx as nx
from sklearn.cluster import KMeans, DBSCAN
from sklearn.metrics import silhouette_score
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import warnings

warnings.filterwarnings("ignore")

BASE_PATH = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if BASE_PATH not in sys.path:
    sys.path.append(BASE_PATH)

from control_log_diagnose.src.log_sequence_predictor import LogSequencePredictor


class FailureReasonAnalyzer:
    """失败原因分析器"""

    def __init__(self, predictor=None):
        """
        初始化分析器

        Args:
            predictor: 已训练的LogSequencePredictor实例
        """
        self.predictor = predictor or LogSequencePredictor()
        self.failure_transitions = {}
        self.success_transitions = {}
        self.critical_transitions = {}
        self.critical_nodes = {}
        self.failure_patterns = {}

    def analyze_critical_transitions(self, sequences_df, min_freq=5):
        """分析关键转换模式"""
        print("=== 分析关键转换模式 ===")

        success_sequences = sequences_df[sequences_df["label_mapped"] == 0]["pattern_sequence"]
        failure_sequences = sequences_df[sequences_df["label_mapped"] == 1]["pattern_sequence"]

        # 统计所有转换
        success_trans_2gram = defaultdict(int)
        failure_trans_2gram = defaultdict(int)
        success_trans_3gram = defaultdict(int)
        failure_trans_3gram = defaultdict(int)

        # 收集成功案例转换
        for seq in success_sequences:
            for i in range(len(seq) - 1):
                trans = f"{seq[i]} -> {seq[i+1]}"
                success_trans_2gram[trans] += 1
            for i in range(len(seq) - 2):
                trans = f"{seq[i]} -> {seq[i+1]} -> {seq[i+2]}"
                success_trans_3gram[trans] += 1

        # 收集失败案例转换
        for seq in failure_sequences:
            for i in range(len(seq) - 1):
                trans = f"{seq[i]} -> {seq[i+1]}"
                failure_trans_2gram[trans] += 1
            for i in range(len(seq) - 2):
                trans = f"{seq[i]} -> {seq[i+1]} -> {seq[i+2]}"
                failure_trans_3gram[trans] += 1

        # 分析关键转换
        def find_critical_transitions(success_trans, failure_trans, min_freq=min_freq):
            critical = []

            total_success = sum(success_trans.values())
            total_failure = sum(failure_trans.values())

            all_trans = set(success_trans.keys()) | set(failure_trans.keys())

            for trans in all_trans:
                s_count = success_trans.get(trans, 0)
                f_count = failure_trans.get(trans, 0)

                if s_count + f_count >= min_freq:
                    # 计算失败风险比率
                    failure_rate = f_count / (s_count + f_count)

                    # 计算相对风险 (与总体失败率比较)
                    overall_failure_rate = total_failure / (total_success + total_failure)
                    relative_risk = failure_rate / overall_failure_rate if overall_failure_rate > 0 else 0

                    # 计算统计显著性 (简化的卡方检验思路)
                    expected_failure = (s_count + f_count) * overall_failure_rate
                    chi_square = ((f_count - expected_failure) ** 2 / expected_failure) if expected_failure > 0 else 0

                    critical.append(
                        {
                            "transition": trans,
                            "success_count": s_count,
                            "failure_count": f_count,
                            "total_count": s_count + f_count,
                            "failure_rate": failure_rate,
                            "relative_risk": relative_risk,
                            "chi_square": chi_square,
                            "severity": "high" if failure_rate > 0.8 else "medium" if failure_rate > 0.6 else "low",
                        }
                    )

            return sorted(critical, key=lambda x: x["relative_risk"], reverse=True)

        # 找出关键转换
        critical_2gram = find_critical_transitions(success_trans_2gram, failure_trans_2gram)
        critical_3gram = find_critical_transitions(success_trans_3gram, failure_trans_3gram)

        self.critical_transitions = {"2gram": critical_2gram, "3gram": critical_3gram}

        print(f"发现关键2元转换: {len(critical_2gram)}")
        print(f"发现关键3元转换: {len(critical_3gram)}")

        # 显示最关键的转换
        print(f"\n最危险的2元转换 (前10个):")
        for i, trans in enumerate(critical_2gram[:10]):
            print(f"{i+1:2d}. {trans['transition'][:60]}...")
            print(f"    失败率: {trans['failure_rate']:.3f} | 相对风险: {trans['relative_risk']:.2f}x | 严重性: {trans['severity']}")

        return self.critical_transitions

    def analyze_critical_nodes(self, sequences_df):
        """分析关键节点"""
        print(f"\n=== 分析关键节点 ===")

        success_sequences = sequences_df[sequences_df["label_mapped"] == 0]["pattern_sequence"]
        failure_sequences = sequences_df[sequences_df["label_mapped"] == 1]["pattern_sequence"]

        # 统计节点在不同位置的出现频率
        success_node_positions = defaultdict(lambda: defaultdict(int))
        failure_node_positions = defaultdict(lambda: defaultdict(int))

        # 分析成功案例中的节点位置
        for seq in success_sequences:
            seq_len = len(seq)
            for i, node in enumerate(seq):
                # 计算相对位置 (0=开始, 0.5=中间, 1=结束)
                relative_pos = i / (seq_len - 1) if seq_len > 1 else 0
                pos_bucket = int(relative_pos * 10) / 10  # 分成10个桶
                success_node_positions[node][pos_bucket] += 1

        # 分析失败案例中的节点位置
        for seq in failure_sequences:
            seq_len = len(seq)
            for i, node in enumerate(seq):
                relative_pos = i / (seq_len - 1) if seq_len > 1 else 0
                pos_bucket = int(relative_pos * 10) / 10
                failure_node_positions[node][pos_bucket] += 1

        # 找出关键节点
        critical_nodes = []
        all_nodes = set(success_node_positions.keys()) | set(failure_node_positions.keys())

        total_success_nodes = sum(sum(pos_counts.values()) for pos_counts in success_node_positions.values())
        total_failure_nodes = sum(sum(pos_counts.values()) for pos_counts in failure_node_positions.values())
        overall_failure_rate = total_failure_nodes / (total_success_nodes + total_failure_nodes)

        for node in all_nodes:
            success_total = sum(success_node_positions[node].values())
            failure_total = sum(failure_node_positions[node].values())

            if success_total + failure_total >= 10:  # 至少出现10次
                node_failure_rate = failure_total / (success_total + failure_total)
                relative_risk = node_failure_rate / overall_failure_rate if overall_failure_rate > 0 else 0

                # 分析在哪个位置最危险
                danger_positions = []
                for pos in [0.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]:
                    s_count = success_node_positions[node][pos]
                    f_count = failure_node_positions[node][pos]
                    if s_count + f_count >= 3:
                        pos_failure_rate = f_count / (s_count + f_count)
                        if pos_failure_rate > 0.7:  # 在该位置失败率超过70%
                            danger_positions.append({"position": pos, "failure_rate": pos_failure_rate, "count": s_count + f_count})

                critical_nodes.append(
                    {
                        "node": node,
                        "success_count": success_total,
                        "failure_count": failure_total,
                        "total_count": success_total + failure_total,
                        "failure_rate": node_failure_rate,
                        "relative_risk": relative_risk,
                        "danger_positions": danger_positions,
                        "severity": "high" if node_failure_rate > 0.8 else "medium" if node_failure_rate > 0.6 else "low",
                    }
                )

        self.critical_nodes = sorted(critical_nodes, key=lambda x: x["relative_risk"], reverse=True)

        print(f"发现关键节点: {len(self.critical_nodes)}")
        print(f"\n最危险的节点 (前10个):")
        for i, node in enumerate(self.critical_nodes[:10]):
            print(f"{i+1:2d}. {node['node']}")
            print(f"    失败率: {node['failure_rate']:.3f} | 相对风险: {node['relative_risk']:.2f}x | 严重性: {node['severity']}")
            if node["danger_positions"]:
                positions = [f"位置{pos['position']:.1f}({pos['failure_rate']:.2f})" for pos in node["danger_positions"][:3]]
                print(f"    危险位置: {', '.join(positions)}")

        return self.critical_nodes

    def cluster_failure_patterns(self, sequences_df, n_clusters=None):
        """聚类失败模式"""
        print(f"\n=== 聚类失败模式 ===")

        failure_sequences = sequences_df[sequences_df["label_mapped"] == 1]

        if len(failure_sequences) < 10:
            print("失败案例太少，无法进行聚类分析")
            return None

        # 为每个失败序列提取特征向量
        features = []
        sequence_info = []

        for idx, row in failure_sequences.iterrows():
            sequence = row["pattern_sequence"]

            # 提取序列特征
            feature_vector = self._extract_clustering_features(sequence)
            features.append(feature_vector)
            sequence_info.append({"request_id": row["request_id"], "sequence": sequence, "length": len(sequence)})

        features_array = np.array(features)

        # 标准化特征
        scaler = StandardScaler()
        features_scaled = scaler.fit_transform(features_array)

        # 确定最佳聚类数
        if n_clusters is None:
            n_clusters = self._find_optimal_clusters(features_scaled, max_k=min(10, len(failure_sequences) // 3))

        # 执行聚类
        kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
        cluster_labels = kmeans.fit_predict(features_scaled)

        # 分析每个聚类的特征
        cluster_patterns = {}
        for cluster_id in range(n_clusters):
            cluster_mask = cluster_labels == cluster_id
            cluster_sequences = [sequence_info[i]["sequence"] for i in range(len(sequence_info)) if cluster_mask[i]]
            cluster_requests = [sequence_info[i]["request_id"] for i in range(len(sequence_info)) if cluster_mask[i]]

            # 分析该聚类的共同特征
            pattern_analysis = self._analyze_cluster_patterns(cluster_sequences)

            cluster_patterns[cluster_id] = {
                "size": sum(cluster_mask),
                "request_ids": cluster_requests,
                "sequences": cluster_sequences,
                "pattern_analysis": pattern_analysis,
                "representative_sequence": self._find_representative_sequence(cluster_sequences),
            }

        self.failure_patterns = cluster_patterns

        print(f"识别出 {n_clusters} 种失败模式:")
        for cluster_id, pattern in cluster_patterns.items():
            print(f"\n失败模式 {cluster_id + 1}: {pattern['size']} 个案例")
            print(f"  主要特征: {pattern['pattern_analysis']['main_characteristics']}")
            print(f"  代表序列长度: {len(pattern['representative_sequence'])}")
            print(f"  典型转换: {pattern['pattern_analysis']['common_transitions'][:3]}")

        return cluster_patterns

    def analyze_failure_case(self, sequence, request_id=None):
        """分析单个失败案例"""
        print(f"\n=== 失败案例分析 ===")
        if request_id:
            print(f"Request ID: {request_id}")

        # 基本序列信息
        print(f"序列长度: {len(sequence)}")
        print(f"唯一模式数: {len(set(sequence))}")
        print(f"多样性比率: {len(set(sequence))/len(sequence):.3f}")

        # 预测确认
        prediction = self.predictor.predict(sequence, return_details=True)
        print(f"预测结果: {prediction['prediction']} (置信度: {prediction['confidence']:.3f})")

        # 关键转换分析
        failure_transitions = []
        for i in range(len(sequence) - 1):
            trans = f"{sequence[i]} -> {sequence[i+1]}"

            # 查找是否为关键转换
            for critical_trans in self.critical_transitions.get("2gram", []):
                if critical_trans["transition"] == trans:
                    failure_transitions.append(
                        {
                            "position": i,
                            "transition": trans,
                            "failure_rate": critical_trans["failure_rate"],
                            "relative_risk": critical_trans["relative_risk"],
                            "severity": critical_trans["severity"],
                        }
                    )
                    break

        # 关键节点分析
        critical_nodes_found = []
        for i, node in enumerate(sequence):
            for critical_node in self.critical_nodes:
                if critical_node["node"] == node:
                    relative_pos = i / (len(sequence) - 1) if len(sequence) > 1 else 0

                    # 检查是否在危险位置
                    in_danger_position = False
                    for danger_pos in critical_node["danger_positions"]:
                        if abs(relative_pos - danger_pos["position"]) <= 0.1:
                            in_danger_position = True
                            break

                    critical_nodes_found.append(
                        {
                            "position": i,
                            "relative_position": relative_pos,
                            "node": node,
                            "failure_rate": critical_node["failure_rate"],
                            "relative_risk": critical_node["relative_risk"],
                            "in_danger_position": in_danger_position,
                            "severity": critical_node["severity"],
                        }
                    )
                    break

        # 失败模式匹配
        best_match_pattern = None
        if self.failure_patterns:
            best_similarity = 0
            for cluster_id, pattern in self.failure_patterns.items():
                similarity = self._calculate_sequence_similarity(sequence, pattern["representative_sequence"])
                if similarity > best_similarity:
                    best_similarity = similarity
                    best_match_pattern = {"cluster_id": cluster_id, "similarity": similarity, "pattern": pattern}

        # 生成分析报告
        analysis_report = {
            "sequence_info": {"length": len(sequence), "unique_patterns": len(set(sequence)), "diversity_ratio": len(set(sequence)) / len(sequence)},
            "prediction": prediction,
            "critical_transitions": failure_transitions,
            "critical_nodes": critical_nodes_found,
            "failure_pattern_match": best_match_pattern,
            "failure_reasons": self._generate_failure_reasons(failure_transitions, critical_nodes_found, best_match_pattern),
        }

        # 打印分析结果
        print(f"\n发现 {len(failure_transitions)} 个关键转换:")
        for trans in failure_transitions:
            print(f"  位置 {trans['position']:2d}: {trans['transition'][:50]}...")
            print(f"    失败率: {trans['failure_rate']:.3f} | 风险: {trans['relative_risk']:.2f}x | 严重性: {trans['severity']}")

        print(f"\n发现 {len(critical_nodes_found)} 个关键节点:")
        for node in critical_nodes_found:
            danger_flag = "🚨" if node["in_danger_position"] else ""
            print(f"  位置 {node['position']:2d}: {node['node']} {danger_flag}")
            print(f"    失败率: {node['failure_rate']:.3f} | 风险: {node['relative_risk']:.2f}x | 严重性: {node['severity']}")

        if best_match_pattern:
            print(f"\n匹配失败模式:")
            print(f"  模式 {best_match_pattern['cluster_id'] + 1} (相似度: {best_match_pattern['similarity']:.3f})")
            print(f"  该模式特征: {best_match_pattern['pattern']['pattern_analysis']['main_characteristics']}")

        print(f"\n可能的失败原因:")
        for i, reason in enumerate(analysis_report["failure_reasons"]):
            print(f"  {i+1}. {reason}")

        return analysis_report

    def _extract_clustering_features(self, sequence):
        """为聚类提取序列特征"""
        features = []

        # 基本统计特征
        features.extend([len(sequence), len(set(sequence)), len(set(sequence)) / len(sequence) if len(sequence) > 0 else 0])

        # 位置特征（开始、中间、结束的主要模式）
        if len(sequence) >= 3:
            start_patterns = Counter(sequence[:3])
            middle_patterns = Counter(sequence[len(sequence) // 3 : 2 * len(sequence) // 3])
            end_patterns = Counter(sequence[-3:])

            # 使用最频繁模式的频率作为特征
            features.extend(
                [
                    start_patterns.most_common(1)[0][1] / 3 if start_patterns else 0,
                    middle_patterns.most_common(1)[0][1] / len(middle_patterns) if middle_patterns else 0,
                    end_patterns.most_common(1)[0][1] / 3 if end_patterns else 0,
                ]
            )
        else:
            features.extend([0, 0, 0])

        # 转换特征
        if len(sequence) >= 2:
            transitions = [f"{sequence[i]} -> {sequence[i+1]}" for i in range(len(sequence) - 1)]
            transition_diversity = len(set(transitions)) / len(transitions)
            features.append(transition_diversity)
        else:
            features.append(0)

        # 重复模式特征
        pattern_counts = Counter(sequence)
        max_repeat = max(pattern_counts.values()) if pattern_counts else 0
        features.append(max_repeat / len(sequence) if len(sequence) > 0 else 0)

        return features

    def _find_optimal_clusters(self, features, max_k=10):
        """寻找最佳聚类数"""
        if len(features) < 4:
            return 2

        silhouette_scores = []
        K_range = range(2, min(max_k + 1, len(features)))

        for k in K_range:
            kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
            labels = kmeans.fit_predict(features)
            score = silhouette_score(features, labels)
            silhouette_scores.append(score)

        if silhouette_scores:
            best_k = K_range[np.argmax(silhouette_scores)]
            return best_k
        else:
            return 2

    def _analyze_cluster_patterns(self, sequences):
        """分析聚类的共同模式"""
        # 统计最常见的模式
        all_patterns = []
        all_transitions = []

        for seq in sequences:
            all_patterns.extend(seq)
            for i in range(len(seq) - 1):
                all_transitions.append(f"{seq[i]} -> {seq[i+1]}")

        common_patterns = Counter(all_patterns).most_common(5)
        common_transitions = Counter(all_transitions).most_common(5)

        # 计算平均长度和多样性
        avg_length = np.mean([len(seq) for seq in sequences])
        avg_diversity = np.mean([len(set(seq)) / len(seq) for seq in sequences])

        # 主要特征描述
        main_characteristics = []
        if avg_length > 500:
            main_characteristics.append("长序列")
        elif avg_length < 100:
            main_characteristics.append("短序列")

        if avg_diversity > 0.5:
            main_characteristics.append("高多样性")
        elif avg_diversity < 0.3:
            main_characteristics.append("低多样性/重复性高")

        return {
            "avg_length": avg_length,
            "avg_diversity": avg_diversity,
            "common_patterns": [p[0] for p in common_patterns],
            "common_transitions": [t[0] for t in common_transitions],
            "main_characteristics": ", ".join(main_characteristics) if main_characteristics else "常规模式",
        }

    def _find_representative_sequence(self, sequences):
        """找到聚类的代表性序列"""
        if not sequences:
            return []

        # 选择长度最接近平均长度的序列
        avg_length = np.mean([len(seq) for seq in sequences])
        best_seq = min(sequences, key=lambda seq: abs(len(seq) - avg_length))
        return best_seq

    def _calculate_sequence_similarity(self, seq1, seq2):
        """计算两个序列的相似度"""
        set1 = set(seq1)
        set2 = set(seq2)
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))
        return intersection / union if union > 0 else 0

    def _generate_failure_reasons(self, critical_transitions, critical_nodes, pattern_match):
        """生成可能的失败原因"""
        reasons = []

        # 基于关键转换的原因
        high_risk_transitions = [t for t in critical_transitions if t["severity"] == "high"]
        if high_risk_transitions:
            reasons.append(f"检测到 {len(high_risk_transitions)} 个高风险转换，可能导致系统状态异常")

        medium_risk_transitions = [t for t in critical_transitions if t["severity"] == "medium"]
        if medium_risk_transitions:
            reasons.append(f"存在 {len(medium_risk_transitions)} 个中等风险转换，增加失败概率")

        # 基于关键节点的原因
        danger_nodes = [n for n in critical_nodes if n["in_danger_position"]]
        if danger_nodes:
            reasons.append(f"关键节点 {[n['node'] for n in danger_nodes]} 出现在危险位置")

        high_risk_nodes = [n for n in critical_nodes if n["severity"] == "high"]
        if high_risk_nodes:
            reasons.append(f"涉及高风险节点: {[n['node'] for n in high_risk_nodes]}")

        # 基于失败模式的原因
        if pattern_match and pattern_match["similarity"] > 0.6:
            reasons.append(f"符合已知失败模式 {pattern_match['cluster_id'] + 1}: {pattern_match['pattern']['pattern_analysis']['main_characteristics']}")

        # 如果没有明显原因，给出通用分析
        if not reasons:
            if critical_transitions:
                reasons.append("序列中存在潜在风险转换")
            if critical_nodes:
                reasons.append("涉及已知问题节点")
            if not critical_transitions and not critical_nodes:
                reasons.append("可能是罕见的失败模式，需要进一步分析")

        return reasons

    def generate_summary_report(self, sequences_df):
        """生成总体分析报告"""
        print(f"\n=== 失败原因分析总结报告 ===")

        failure_count = len(sequences_df[sequences_df["label_mapped"] == 1])
        success_count = len(sequences_df[sequences_df["label_mapped"] == 0])

        print(f"数据概况:")
        print(f"  成功案例: {success_count:,}")
        print(f"  失败案例: {failure_count:,}")
        print(f"  失败率: {failure_count/(success_count+failure_count):.3f}")

        print(f"\n关键发现:")
        print(f"  识别出 {len(self.critical_transitions.get('2gram', []))} 个关键2元转换")
        print(f"  识别出 {len(self.critical_transitions.get('3gram', []))} 个关键3元转换")
        print(f"  识别出 {len(self.critical_nodes)} 个关键节点")

        if self.failure_patterns:
            print(f"  发现 {len(self.failure_patterns)} 种失败模式")

            print(f"\n失败模式分布:")
            for cluster_id, pattern in self.failure_patterns.items():
                percentage = pattern["size"] / failure_count * 100
                print(f"    模式 {cluster_id + 1}: {pattern['size']} 案例 ({percentage:.1f}%) - {pattern['pattern_analysis']['main_characteristics']}")

        # 最危险的元素
        print(f"\n最危险的转换 (前5个):")
        for i, trans in enumerate(self.critical_transitions.get("2gram", [])[:5]):
            print(f"  {i+1}. {trans['transition'][:50]}... (失败率: {trans['failure_rate']:.3f})")

        print(f"\n最危险的节点 (前5个):")
        for i, node in enumerate(self.critical_nodes[:5]):
            print(f"  {i+1}. {node['node']} (失败率: {node['failure_rate']:.3f})")


# %%
# 使用示例
if __name__ == "__main__":
    print("=== 失败原因分析器演示 ===")

    # 加载数据
    data_path = os.path.join(BASE_PATH, "data/control_log/drain_pattern_results/ecs_regionmaster_info_log_with_pattern_ids.parquet")
    data = pd.read_parquet(data_path, engine="pyarrow")

    # 预处理
    data["label_mapped"] = data["label"].map({-1: 1, 0: 0})
    available_data = data[data["log_time"] - 10 < data["timestamp"]]

    # 生成序列数据
    pattern_sequences = (
        available_data.groupby(["request_id", "label_mapped"])
        .apply(lambda x: x.sort_values("log_time")["pattern_id"].tolist(), include_groups=False)
        .reset_index(name="pattern_sequence")
    )

    # 创建分析器
    analyzer = FailureReasonAnalyzer()

    # 分析关键转换和节点
    analyzer.analyze_critical_transitions(pattern_sequences)
    analyzer.analyze_critical_nodes(pattern_sequences)

    # 聚类失败模式
    analyzer.cluster_failure_patterns(pattern_sequences)

    # 生成总结报告
    analyzer.generate_summary_report(pattern_sequences)

    # 分析几个具体的失败案例
    failure_samples = pattern_sequences[pattern_sequences["label_mapped"] == 1].sample(n=3, random_state=42)

    print(f"\n=== 具体失败案例分析 ===")
    for idx, row in failure_samples.iterrows():
        analyzer.analyze_failure_case(row["pattern_sequence"], row["request_id"])
        print("\n" + "=" * 80)

# %%
