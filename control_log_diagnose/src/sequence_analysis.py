# %%
import os
import sys

import json

import pandas as pd
import numpy as np
import yaml
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

BASE_PATH = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if BASE_PATH not in sys.path:
    sys.path.append(BASE_PATH)

data_path = os.path.join(BASE_PATH, "data/control_log/drain_pattern_results/console_log_with_pattern_ids.parquet")

# 检查可能的数据文件
alt_data_paths = [
    os.path.join(BASE_PATH, "data/control_log/processed_data"),
    os.path.join(BASE_PATH, "data/control_log"),
    os.path.join(BASE_PATH, "data/control_log/drain_pattern_results"),
]

print("Checking for available data files:")
for path in alt_data_paths:
    if os.path.exists(path):
        print(f"\nContents of {path}:")
        for file in os.listdir(path):
            if file.endswith((".parquet", ".csv", ".jsonl")):
                full_path = os.path.join(path, file)
                size_mb = os.path.getsize(full_path) / (1024 * 1024)
                print(f"  - {file} ({size_mb:.1f} MB)")

# 尝试使用更大的数据集
data_files_to_try = ["ecs_regionmaster_info_log_with_pattern_ids.parquet", "ecs_pync_log_with_pattern_ids.parquet", "console_log_with_pattern_ids.parquet"]

data = None
for filename in data_files_to_try:
    try:
        data_path = os.path.join(BASE_PATH, "data/control_log/drain_pattern_results", filename)
        if os.path.exists(data_path):
            print(f"\nTrying to load {filename}...")
            temp_data = pd.read_parquet(data_path, engine="pyarrow")
            print(f"  - Total records: {len(temp_data)}")
            if "label" in temp_data.columns:
                label_dist = temp_data["label"].value_counts().sort_index()
                print(f"  - Label distribution: {dict(label_dist)}")
                # 如果发现有label=1的数据，使用这个文件
                if 1 in temp_data["label"].values:
                    data = temp_data
                    print(f"  - Using {filename} (contains both success and failure cases)")
                    break
                elif data is None:  # 如果还没有选择数据文件，先用第一个
                    data = temp_data
                    print(f"  - Temporarily using {filename} (only success cases)")
    except Exception as e:
        print(f"  - Error loading {filename}: {e}")

if data is None:
    print("No suitable data file found!")
    exit()

print(f"\nFinal dataset: {len(data)} records")
print("Data columns:", data.columns.tolist())
print("Label distribution in selected data:")
print(data["label"].value_counts().sort_index())
print(f"Unique labels: {data['label'].unique()}")

# 调整label映射：-1表示失败，0表示成功
print(f"\nMapping labels: -1 -> Failure, 0 -> Success")
data["label_mapped"] = data["label"].map({-1: 1, 0: 0})  # 将-1映射为1（失败），0保持为0（成功）

available_data = data[data["log_time"] - 10 < data["timestamp"]]
print(f"\nAfter time filtering - remaining records: {len(available_data)}")
print("Mapped label distribution after filtering:")
print(available_data["label_mapped"].value_counts().sort_index())

# %%
# Group by request_id and label_mapped, sort by log_time, and get pattern_id sequences
pattern_sequences = (
    available_data.groupby(["request_id", "label_mapped"])
    .apply(lambda x: x.sort_values("log_time")["pattern_id"].tolist(), include_groups=False)
    .reset_index(name="pattern_sequence")
)

print(f"Number of request_id and label groups: {len(pattern_sequences)}")
print("Sample pattern sequences:")
print(pattern_sequences.head())

pattern_sequences["pattern_length"] = pattern_sequences["pattern_sequence"].apply(len)

# %%
# 可视化分析不同 label 的 pattern_sequence 分布差异

# 设置中文字体和图表样式
plt.rcParams["font.sans-serif"] = ["Arial Unicode MS", "SimHei"]
plt.rcParams["axes.unicode_minus"] = False
sns.set_style("whitegrid")

# 由于数据量很大，先进行采样以提高可视化性能
sample_size = 1000
pattern_sequences_sample = (
    pattern_sequences.groupby("label_mapped").apply(lambda x: x.sample(n=min(len(x), sample_size), random_state=42)).reset_index(drop=True)
)

print(f"Sampled data for visualization: {len(pattern_sequences_sample)} sequences")
print("Sample distribution:", pattern_sequences_sample["label_mapped"].value_counts().sort_index())

# 1. 序列长度分布比较
fig, axes = plt.subplots(2, 2, figsize=(15, 12))
fig.suptitle("Pattern Sequence Distribution Analysis by Label", fontsize=16, fontweight="bold")

# 1.1 序列长度分布箱线图
sns.boxplot(data=pattern_sequences_sample, x="label_mapped", y="pattern_length", ax=axes[0, 0])
axes[0, 0].set_title("Pattern Sequence Length Distribution")
axes[0, 0].set_xlabel("Label (0=Success, 1=Failure)")
axes[0, 0].set_ylabel("Sequence Length")

# 1.2 序列长度分布直方图
for label in [0, 1]:
    subset = pattern_sequences_sample[pattern_sequences_sample["label_mapped"] == label]["pattern_length"]
    if len(subset) > 0:
        axes[0, 1].hist(subset, alpha=0.7, label=f"Label {label}", bins=30)
axes[0, 1].set_title("Pattern Sequence Length Histogram")
axes[0, 1].set_xlabel("Sequence Length")
axes[0, 1].set_ylabel("Frequency")
axes[0, 1].legend()


# 2. 序列中最常见的 pattern_id 分析
def get_pattern_frequency(sequences):
    all_patterns = []
    for seq in sequences:
        all_patterns.extend(seq)
    return Counter(all_patterns)


label_0_patterns = get_pattern_frequency(pattern_sequences[pattern_sequences["label_mapped"] == 0]["pattern_sequence"])
label_1_patterns = get_pattern_frequency(pattern_sequences[pattern_sequences["label_mapped"] == 1]["pattern_sequence"])

# 获取前10个最常见的pattern
top_patterns_0 = dict(label_0_patterns.most_common(10))
top_patterns_1 = dict(label_1_patterns.most_common(10))

# 2.1 Label 0 的前10个常见pattern
axes[1, 0].bar(range(len(top_patterns_0)), list(top_patterns_0.values()))
axes[1, 0].set_title("Top 10 Pattern IDs in Label 0 (Success)")
axes[1, 0].set_xlabel("Pattern ID Rank")
axes[1, 0].set_ylabel("Frequency")
axes[1, 0].set_xticks(range(len(top_patterns_0)))
axes[1, 0].set_xticklabels([f"P{pid}" for pid in top_patterns_0.keys()], rotation=45)

# 2.2 Label 1 的前10个常见pattern
axes[1, 1].bar(range(len(top_patterns_1)), list(top_patterns_1.values()))
axes[1, 1].set_title("Top 10 Pattern IDs in Label 1 (Failure)")
axes[1, 1].set_xlabel("Pattern ID Rank")
axes[1, 1].set_ylabel("Frequency")
axes[1, 1].set_xticks(range(len(top_patterns_1)))
axes[1, 1].set_xticklabels([f"P{pid}" for pid in top_patterns_1.keys()], rotation=45)

plt.tight_layout()
plt.show()

# %%
# 3. 详细统计分析
print("=== Pattern Sequence Statistics by Label ===")
print("\nSequence Length Statistics:")
print(pattern_sequences.groupby("label_mapped")["pattern_length"].describe())

print(f"\nTotal sequences by label:")
print(pattern_sequences["label_mapped"].value_counts().sort_index())

print(f"\nUnique pattern IDs in each label:")
unique_patterns_0 = set()
unique_patterns_1 = set()
for seq in pattern_sequences[pattern_sequences["label_mapped"] == 0]["pattern_sequence"]:
    unique_patterns_0.update(seq)
for seq in pattern_sequences[pattern_sequences["label_mapped"] == 1]["pattern_sequence"]:
    unique_patterns_1.update(seq)

print(f"Label 0 (Success): {len(unique_patterns_0)} unique patterns")
print(f"Label 1 (Failure): {len(unique_patterns_1)} unique patterns")
print(f"Common patterns: {len(unique_patterns_0.intersection(unique_patterns_1))}")
print(f"Label 0 only: {len(unique_patterns_0 - unique_patterns_1)}")
print(f"Label 1 only: {len(unique_patterns_1 - unique_patterns_0)}")

# %%
# 4. 使用 Plotly 创建交互式可视化
fig = make_subplots(
    rows=2,
    cols=2,
    subplot_titles=("Sequence Length Distribution", "Pattern Frequency Heatmap", "Sequence Length vs Pattern Diversity", "Top Distinctive Patterns"),
    specs=[[{"secondary_y": False}, {"secondary_y": False}], [{"secondary_y": False}, {"secondary_y": False}]],
)

# 4.1 序列长度小提琴图
for label in [0, 1]:
    subset = pattern_sequences[pattern_sequences["label_mapped"] == label]["pattern_length"]
    if len(subset) > 0:
        fig.add_trace(go.Violin(y=subset, name=f"Label {label}", box_visible=True, meanline_visible=True), row=1, col=1)

# 4.2 Pattern频率比较热力图数据准备
all_patterns = list(unique_patterns_0.union(unique_patterns_1))
pattern_freq_matrix = []
for pattern in all_patterns[:50]:  # 取前50个pattern避免图表过密
    freq_0 = label_0_patterns.get(pattern, 0)
    freq_1 = label_1_patterns.get(pattern, 0)
    pattern_freq_matrix.append([freq_0, freq_1])

fig.add_trace(
    go.Heatmap(z=np.array(pattern_freq_matrix).T, x=[f"P{p}" for p in all_patterns[:50]], y=["Label 0", "Label 1"], colorscale="Viridis"), row=1, col=2
)

# 4.3 序列长度 vs Pattern多样性散点图
pattern_sequences["unique_patterns"] = pattern_sequences["pattern_sequence"].apply(lambda x: len(set(x)))
for label in [0, 1]:
    subset = pattern_sequences[pattern_sequences["label_mapped"] == label]
    if len(subset) > 0:
        fig.add_trace(go.Scatter(x=subset["pattern_length"], y=subset["unique_patterns"], mode="markers", name=f"Label {label}", opacity=0.6), row=2, col=1)

# 4.4 特异性pattern分析
label_0_only_patterns = unique_patterns_0 - unique_patterns_1
label_1_only_patterns = unique_patterns_1 - unique_patterns_0

distinctive_data = [
    ["Label 0 Only", len(label_0_only_patterns)],
    ["Label 1 Only", len(label_1_only_patterns)],
    ["Common", len(unique_patterns_0.intersection(unique_patterns_1))],
]

fig.add_trace(go.Bar(x=[row[0] for row in distinctive_data], y=[row[1] for row in distinctive_data], name="Pattern Distribution"), row=2, col=2)

fig.update_layout(height=800, showlegend=True, title_text="Pattern Sequence Analysis Dashboard")
fig.show()

# %%
# 5. 序列起始和结束pattern分析
print("\n=== Sequence Start and End Pattern Analysis ===")


def analyze_start_end_patterns(sequences_by_label):
    start_patterns = Counter()
    end_patterns = Counter()

    for seq in sequences_by_label:
        if len(seq) > 0:
            start_patterns[seq[0]] += 1
            end_patterns[seq[-1]] += 1

    return start_patterns, end_patterns


# 分析每个label的起始和结束pattern
for label in [0, 1]:
    sequences = pattern_sequences[pattern_sequences["label_mapped"] == label]["pattern_sequence"]
    if len(sequences) > 0:
        start_patterns, end_patterns = analyze_start_end_patterns(sequences)

        print(f"\nLabel {label} ({'Success' if label == 0 else 'Failure'}):")
        print(f"Top 5 starting patterns: {start_patterns.most_common(5)}")
        print(f"Top 5 ending patterns: {end_patterns.most_common(5)}")
    else:
        print(f"\nLabel {label}: No sequences found")

# %%
# 6. 序列相似性分析
from difflib import SequenceMatcher


def sequence_similarity(seq1, seq2):
    """计算两个序列的相似度"""
    return SequenceMatcher(None, seq1, seq2).ratio()


# 计算同label内和不同label间的序列相似性
print("\n=== Sequence Similarity Analysis ===")

# 随机采样进行相似性分析（避免计算量过大）
sample_size = min(100, len(pattern_sequences) // 2)
sample_0 = pattern_sequences[pattern_sequences["label_mapped"] == 0].sample(n=min(sample_size, sum(pattern_sequences["label_mapped"] == 0)))
sample_1 = pattern_sequences[pattern_sequences["label_mapped"] == 1].sample(n=min(sample_size, sum(pattern_sequences["label_mapped"] == 1)))


# 计算同类内相似性
def calculate_intra_similarity(sequences):
    similarities = []
    seqs = sequences["pattern_sequence"].tolist()
    for i in range(len(seqs)):
        for j in range(i + 1, min(i + 10, len(seqs))):  # 限制比较次数
            similarities.append(sequence_similarity(seqs[i], seqs[j]))
    return similarities


intra_sim_0 = calculate_intra_similarity(sample_0)
intra_sim_1 = calculate_intra_similarity(sample_1)

# 计算不同类间相似性
inter_similarities = []
if len(sample_0) > 0 and len(sample_1) > 0:
    for seq_0 in sample_0["pattern_sequence"].head(20):
        for seq_1 in sample_1["pattern_sequence"].head(20):
            inter_similarities.append(sequence_similarity(seq_0, seq_1))

print(f"Intra-class similarity (Label 0): mean={np.mean(intra_sim_0):.3f}, std={np.std(intra_sim_0):.3f}")
if len(intra_sim_1) > 0:
    print(f"Intra-class similarity (Label 1): mean={np.mean(intra_sim_1):.3f}, std={np.std(intra_sim_1):.3f}")
else:
    print(f"Intra-class similarity (Label 1): No data available")
if len(inter_similarities) > 0:
    print(f"Inter-class similarity: mean={np.mean(inter_similarities):.3f}, std={np.std(inter_similarities):.3f}")
else:
    print(f"Inter-class similarity: No data available")

# %%
# 添加统计检验
from scipy import stats

print("\n=== Statistical Significance Tests ===")

# 序列长度的统计检验
length_0 = pattern_sequences[pattern_sequences["label_mapped"] == 0]["pattern_length"]
length_1 = pattern_sequences[pattern_sequences["label_mapped"] == 1]["pattern_length"]

# Mann-Whitney U 检验（非参数检验）
mannwhitney_stat, mannwhitney_p = stats.mannwhitneyu(length_0, length_1, alternative="two-sided")
print(f"Mann-Whitney U test for sequence length:")
print(f"  Statistic: {mannwhitney_stat}")
print(f"  P-value: {mannwhitney_p:.2e}")
print(f"  Significant difference: {'Yes' if mannwhitney_p < 0.05 else 'No'}")

# 序列多样性的统计检验
diversity_0 = pattern_sequences[pattern_sequences["label_mapped"] == 0]["unique_patterns"]
diversity_1 = pattern_sequences[pattern_sequences["label_mapped"] == 1]["unique_patterns"]

mannwhitney_div_stat, mannwhitney_div_p = stats.mannwhitneyu(diversity_0, diversity_1, alternative="two-sided")
print(f"\nMann-Whitney U test for pattern diversity:")
print(f"  Statistic: {mannwhitney_div_stat}")
print(f"  P-value: {mannwhitney_div_p:.2e}")
print(f"  Significant difference: {'Yes' if mannwhitney_div_p < 0.05 else 'No'}")

# %%
# 分析重要的差异化模式
print("\n=== Key Distinguishing Patterns ===")

# 计算每个pattern在两个类别中的相对频率
total_patterns_0 = sum(label_0_patterns.values())
total_patterns_1 = sum(label_1_patterns.values())

pattern_analysis = []
all_unique_patterns = set(label_0_patterns.keys()) | set(label_1_patterns.keys())

for pattern in all_unique_patterns:
    freq_0 = label_0_patterns.get(pattern, 0)
    freq_1 = label_1_patterns.get(pattern, 0)

    # 计算相对频率
    rel_freq_0 = freq_0 / total_patterns_0 if total_patterns_0 > 0 else 0
    rel_freq_1 = freq_1 / total_patterns_1 if total_patterns_1 > 0 else 0

    # 计算频率比值 (避免除零)
    if rel_freq_1 > 0:
        ratio = rel_freq_0 / rel_freq_1
    elif rel_freq_0 > 0:
        ratio = float("inf")  # 只在成功案例中出现
    else:
        ratio = 1  # 都为0

    pattern_analysis.append(
        {
            "pattern": pattern,
            "freq_success": freq_0,
            "freq_failure": freq_1,
            "rel_freq_success": rel_freq_0,
            "rel_freq_failure": rel_freq_1,
            "ratio_success_to_failure": ratio,
        }
    )

# 转换为DataFrame并排序
pattern_df = pd.DataFrame(pattern_analysis)

# 找出最能区分成功和失败的模式
print("Top 10 patterns most indicative of SUCCESS (high in success, low in failure):")
success_indicators = pattern_df[pattern_df["freq_success"] >= 10].nlargest(10, "ratio_success_to_failure")
for _, row in success_indicators.iterrows():
    print(f"  {row['pattern']}: Success={row['freq_success']}, Failure={row['freq_failure']}, Ratio={row['ratio_success_to_failure']:.2f}")

print("\nTop 10 patterns most indicative of FAILURE (high in failure, low in success):")
failure_indicators = pattern_df[pattern_df["freq_failure"] >= 10].nsmallest(10, "ratio_success_to_failure")
for _, row in failure_indicators.iterrows():
    ratio_text = f"{1/row['ratio_success_to_failure']:.2f}" if row["ratio_success_to_failure"] > 0 else "inf"
    print(f"  {row['pattern']}: Success={row['freq_success']}, Failure={row['freq_failure']}, Failure/Success={ratio_text}")

# %%
