#!/usr/bin/env python3
"""
端到端失败分析算法
给定序列，判断是否失败，并识别关键失败转换和对应的模板ID
"""

import os
import sys
import pickle
import pandas as pd
import numpy as np
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from collections import defaultdict, Counter

# 添加路径
BASE_PATH = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if BASE_PATH not in sys.path:
    sys.path.append(BASE_PATH)


@dataclass
class FailureTransition:
    """失败转换信息"""

    from_pattern: str
    to_pattern: str
    position: int
    failure_rate: float
    relative_risk: float
    severity: str
    from_template_id: Optional[str] = None
    to_template_id: Optional[str] = None


@dataclass
class AnalysisResult:
    """分析结果"""

    sequence: List[str]
    is_failure: bool
    confidence: float
    critical_transitions: List[FailureTransition]
    summary: str


class EndToEndFailureAnalyzer:
    """端到端失败分析器"""

    def __init__(self, model_path: str = None, template_mapping_path: str = None):
        """
        初始化分析器

        Args:
            model_path: 模型文件路径
            template_mapping_path: 模板映射文件路径
        """
        self.model_path = model_path or os.path.join(BASE_PATH, "control_log_diagnose/models/sequence_classifier.pkl")
        self.template_mapping_path = template_mapping_path

        # 加载模型和数据
        self._load_model()
        self._load_template_mapping()
        self._prepare_transition_stats()

    def _load_model(self):
        """加载训练好的模型"""
        try:
            with open(self.model_path, "rb") as f:
                model_data = pickle.load(f)

            self.classifier = model_data["model"]
            self.scaler = model_data.get("scaler", None)
            self.feature_names = model_data.get("feature_names", [])
            self.transition_features = model_data.get("transition_features", [])

            # 构建特征词汇表（兼容旧格式）
            self.feature_vocab = {"transitions": self.transition_features, "first_pattern": [], "last_pattern": []}  # 将在后续填充  # 将在后续填充

            print(f"✓ 模型加载成功: {self.model_path}")
            print(f"  - 转换特征数: {len(self.transition_features)}")

        except Exception as e:
            raise RuntimeError(f"❌ 模型加载失败: {e}")

    def _load_template_mapping(self):
        """加载模板映射关系"""
        # 如果有模板映射文件，加载它
        if self.template_mapping_path and os.path.exists(self.template_mapping_path):
            try:
                self.template_mapping = pd.read_csv(self.template_mapping_path)
                print(f"✓ 模板映射加载成功: {self.template_mapping_path}")
            except Exception as e:
                print(f"⚠️ 模板映射加载失败: {e}")
                self.template_mapping = None
        else:
            # 使用默认映射（pattern_id -> template_id）
            self.template_mapping = None
            print("ℹ️ 使用默认模板映射")

    def _get_template_id(self, pattern_id: str) -> str:
        """获取模板ID"""
        if self.template_mapping is not None:
            # 从映射文件中查找
            template_row = self.template_mapping[self.template_mapping["pattern_id"] == pattern_id]
            if not template_row.empty:
                return template_row["template_id"].iloc[0]

        # 默认映射：pattern_id就是template_id
        return pattern_id.replace("drain_pattern_", "template_")

    def _prepare_transition_stats(self):
        """准备转换统计数据"""
        # 加载训练数据计算转换统计
        data_path = os.path.join(BASE_PATH, "data/control_log/drain_pattern_results/ecs_regionmaster_info_log_with_pattern_ids.parquet")

        if not os.path.exists(data_path):
            print("⚠️ 训练数据不存在，使用默认转换统计")
            self.transition_stats = {}
            self.overall_failure_rate = 0.5
            return

        try:
            data = pd.read_parquet(data_path, engine="pyarrow")
            data["label_mapped"] = data["label"].map({-1: 1, 0: 0})
            available_data = data[data["log_time"] - 10 < data["timestamp"]]

            # 生成序列数据
            pattern_sequences = (
                available_data.groupby(["request_id", "label_mapped"])
                .apply(lambda x: x.sort_values("log_time")["pattern_id"].tolist(), include_groups=False)
                .reset_index(name="pattern_sequence")
            )

            # 计算整体失败率
            self.overall_failure_rate = pattern_sequences["label_mapped"].mean()

            # 计算转换统计
            self.transition_stats = self._calculate_transition_stats(pattern_sequences)
            print(f"✓ 转换统计计算完成: {len(self.transition_stats)} 个转换")

        except Exception as e:
            print(f"⚠️ 转换统计计算失败: {e}")
            self.transition_stats = {}
            self.overall_failure_rate = 0.5

    def _calculate_transition_stats(self, pattern_sequences: pd.DataFrame) -> Dict:
        """计算转换统计"""
        transition_stats = defaultdict(lambda: {"total": 0, "failures": 0})

        for _, row in pattern_sequences.iterrows():
            sequence = row["pattern_sequence"]
            is_failure = row["label_mapped"]

            # 计算2元转换
            for i in range(len(sequence) - 1):
                transition = (sequence[i], sequence[i + 1])
                transition_stats[transition]["total"] += 1
                if is_failure:
                    transition_stats[transition]["failures"] += 1

        # 计算失败率和相对风险
        final_stats = {}
        for transition, stats in transition_stats.items():
            if stats["total"] >= 5:  # 至少出现5次才有统计意义
                failure_rate = stats["failures"] / stats["total"]
                relative_risk = failure_rate / self.overall_failure_rate if self.overall_failure_rate > 0 else 1.0

                final_stats[transition] = {
                    "failure_rate": failure_rate,
                    "relative_risk": relative_risk,
                    "total_count": stats["total"],
                    "failure_count": stats["failures"],
                }

        return final_stats

    def _extract_all_features(self, sequence: List[str]) -> np.ndarray:
        """提取序列的所有特征（与训练时一致）"""
        features = []

        # 1. 基本统计特征 (12个)
        seq_length = len(sequence)
        unique_patterns = len(set(sequence))
        diversity_ratio = unique_patterns / seq_length if seq_length > 0 else 0
        repetition_count = seq_length - unique_patterns

        pattern_counts = Counter(sequence)
        most_frequent_count = pattern_counts.most_common(1)[0][1] if sequence else 0
        avg_frequency = np.mean(list(pattern_counts.values())) if sequence else 0
        std_frequency = np.std(list(pattern_counts.values())) if len(pattern_counts) > 1 else 0

        # 连续重复和循环
        consecutive_repeats = 1
        has_cycle = 0
        if seq_length > 1:
            current_repeat = 1
            for i in range(1, seq_length):
                if sequence[i] == sequence[i - 1]:
                    current_repeat += 1
                else:
                    consecutive_repeats = max(consecutive_repeats, current_repeat)
                    current_repeat = 1
            consecutive_repeats = max(consecutive_repeats, current_repeat)

            # 简单循环检测
            for cycle_len in range(2, min(10, seq_length // 2 + 1)):
                for start in range(seq_length - 2 * cycle_len + 1):
                    if sequence[start : start + cycle_len] == sequence[start + cycle_len : start + 2 * cycle_len]:
                        has_cycle = 1
                        break
                if has_cycle:
                    break

        # 熵和稳定性
        if pattern_counts:
            probabilities = np.array(list(pattern_counts.values())) / seq_length
            entropy = -np.sum(probabilities * np.log2(probabilities + 1e-10))
        else:
            entropy = 0

        stability_score = 1 - diversity_ratio if seq_length > 0 else 0

        basic_features = [
            seq_length,
            unique_patterns,
            diversity_ratio,
            repetition_count,
            most_frequent_count,
            avg_frequency,
            std_frequency,
            consecutive_repeats,
            has_cycle,
            entropy,
            stability_score,
            0,  # 12个特征
        ]
        features.extend(basic_features)

        # 2. 转换特征（使用模型保存的特征数量）
        transitions = [(sequence[i], sequence[i + 1]) for i in range(len(sequence) - 1)]
        transition_counter = Counter(transitions)

        # 使用模型中保存的转换特征
        for transition in self.transition_features:
            features.append(transition_counter.get(transition, 0))

        # 如果转换特征不足，需要补充到期望数量
        expected_transition_features = 55  # 77 - 12 - 6 - 10 + 5 = 55
        current_transition_features = len(self.transition_features)
        if current_transition_features < expected_transition_features:
            # 补充0值特征
            features.extend([0] * (expected_transition_features - current_transition_features))

        # 3. 路径特征 (6个)
        first_pattern = sequence[0] if sequence else "EMPTY"
        last_pattern = sequence[-1] if sequence else "EMPTY"
        path_diversity = len(set([first_pattern, last_pattern]))
        start_end_same = 1 if first_pattern == last_pattern else 0

        path_features = [
            1 if first_pattern != "EMPTY" else 0,  # has_first
            1 if last_pattern != "EMPTY" else 0,  # has_last
            path_diversity,
            start_end_same,
            0,
            0,  # 占位符达到6个
        ]
        features.extend(path_features)

        # 4. 网络特征 (5个)
        if transitions:
            unique_transitions = len(set(transitions))

            out_degrees = Counter([t[0] for t in transitions])
            in_degrees = Counter([t[1] for t in transitions])
            max_out_degree = max(out_degrees.values()) if out_degrees else 0
            max_in_degree = max(in_degrees.values()) if in_degrees else 0

            # 简化的聚类系数
            clustering_coeff = 0
        else:
            unique_transitions = 0
            max_out_degree = max_in_degree = 0
            clustering_coeff = 0

        network_features = [unique_transitions, max_out_degree, max_in_degree, clustering_coeff, 0]  # 5个特征
        features.extend(network_features)

        # 确保总特征数为77
        current_feature_count = len(features)
        if current_feature_count < 77:
            features.extend([0] * (77 - current_feature_count))
        elif current_feature_count > 77:
            features = features[:77]

        result = np.array(features).reshape(1, -1)

        # 如果有scaler，进行标准化
        if hasattr(self, "scaler") and self.scaler is not None:
            result = self.scaler.transform(result)

        return result

    def _identify_critical_transitions(self, sequence: List[str]) -> List[FailureTransition]:
        """识别关键失败转换"""
        critical_transitions = []

        for i in range(len(sequence) - 1):
            from_pattern = sequence[i]
            to_pattern = sequence[i + 1]
            transition = (from_pattern, to_pattern)

            if transition in self.transition_stats:
                stats = self.transition_stats[transition]
                failure_rate = stats["failure_rate"]
                relative_risk = stats["relative_risk"]

                # 判断严重性
                if failure_rate >= 0.8 and relative_risk >= 2.0:
                    severity = "high"
                elif failure_rate >= 0.6 and relative_risk >= 1.5:
                    severity = "medium"
                elif failure_rate >= 0.4 and relative_risk >= 1.2:
                    severity = "low"
                else:
                    continue  # 不是关键转换

                critical_transition = FailureTransition(
                    from_pattern=from_pattern,
                    to_pattern=to_pattern,
                    position=i,
                    failure_rate=failure_rate,
                    relative_risk=relative_risk,
                    severity=severity,
                    from_template_id=self._get_template_id(from_pattern),
                    to_template_id=self._get_template_id(to_pattern),
                )

                critical_transitions.append(critical_transition)

        # 按严重性和风险排序
        severity_order = {"high": 3, "medium": 2, "low": 1}
        critical_transitions.sort(key=lambda x: (severity_order[x.severity], x.relative_risk), reverse=True)

        return critical_transitions

    def analyze_sequence(self, sequence: List[str]) -> AnalysisResult:
        """
        分析给定序列

        Args:
            sequence: 模式序列，如 ['drain_pattern_1', 'drain_pattern_2', ...]

        Returns:
            AnalysisResult: 分析结果
        """
        if not sequence:
            return AnalysisResult(sequence=sequence, is_failure=False, confidence=0.0, critical_transitions=[], summary="空序列，无法分析")

        try:
            # 1. 预测是否失败
            features = self._extract_all_features(sequence)
            prediction_proba = self.classifier.predict_proba(features)[0]
            is_failure = prediction_proba[1] > 0.5
            confidence = max(prediction_proba)

            # 2. 如果是失败案例，识别关键转换
            critical_transitions = []
            if is_failure:
                critical_transitions = self._identify_critical_transitions(sequence)

            # 3. 生成总结
            summary = self._generate_summary(sequence, is_failure, confidence, critical_transitions)

            return AnalysisResult(sequence=sequence, is_failure=is_failure, confidence=confidence, critical_transitions=critical_transitions, summary=summary)

        except Exception as e:
            return AnalysisResult(sequence=sequence, is_failure=False, confidence=0.0, critical_transitions=[], summary=f"分析过程中出错: {e}")

    def _generate_summary(self, sequence: List[str], is_failure: bool, confidence: float, critical_transitions: List[FailureTransition]) -> str:
        """生成分析总结"""
        summary_parts = []

        # 基本信息
        summary_parts.append(f"序列长度: {len(sequence)}")
        summary_parts.append(f"唯一模式数: {len(set(sequence))}")
        summary_parts.append(f"预测结果: {'失败' if is_failure else '成功'} (置信度: {confidence:.3f})")

        if is_failure and critical_transitions:
            summary_parts.append(f"\n发现 {len(critical_transitions)} 个关键失败转换:")

            # 按严重性分组
            high_risk = [t for t in critical_transitions if t.severity == "high"]
            medium_risk = [t for t in critical_transitions if t.severity == "medium"]
            low_risk = [t for t in critical_transitions if t.severity == "low"]

            if high_risk:
                summary_parts.append(f"  🚨 高风险转换 ({len(high_risk)} 个):")
                for t in high_risk[:3]:  # 只显示前3个
                    summary_parts.append(
                        f"    位置{t.position}: {t.from_template_id} → {t.to_template_id} " f"(失败率: {t.failure_rate:.3f}, 风险: {t.relative_risk:.2f}x)"
                    )

            if medium_risk:
                summary_parts.append(f"  ⚠️ 中等风险转换 ({len(medium_risk)} 个):")
                for t in medium_risk[:2]:  # 只显示前2个
                    summary_parts.append(
                        f"    位置{t.position}: {t.from_template_id} → {t.to_template_id} " f"(失败率: {t.failure_rate:.3f}, 风险: {t.relative_risk:.2f}x)"
                    )

            if low_risk:
                summary_parts.append(f"  ℹ️ 低风险转换 ({len(low_risk)} 个)")

        elif is_failure:
            summary_parts.append("\n⚠️ 预测为失败，但未发现明显的关键转换模式")

        return "\n".join(summary_parts)

    def batch_analyze(self, sequences: List[List[str]]) -> List[AnalysisResult]:
        """批量分析序列"""
        results = []
        for i, sequence in enumerate(sequences):
            if i % 100 == 0:
                print(f"处理序列 {i+1}/{len(sequences)}")

            result = self.analyze_sequence(sequence)
            results.append(result)

        return results


def main():
    """主函数，演示用法"""
    print("=== 端到端失败分析器演示 ===\n")

    # 初始化分析器
    analyzer = EndToEndFailureAnalyzer()

    # 示例序列（来自实际数据）
    test_sequences = [
        # 成功案例示例
        ["drain_pattern_1", "drain_pattern_2", "drain_pattern_3", "drain_pattern_24", "drain_pattern_135"],
        # 失败案例示例（包含已知的危险转换）
        [
            "drain_pattern_1",
            "drain_pattern_1593",
            "drain_pattern_3331",
            "drain_pattern_1594",
            "drain_pattern_3",
            "drain_pattern_24",
            "drain_pattern_135",
            "drain_pattern_292",
            "drain_pattern_1587",
            "drain_pattern_1589",
        ],
        # 另一个失败案例
        ["drain_pattern_43", "drain_pattern_1596", "drain_pattern_292", "drain_pattern_585", "drain_pattern_535", "drain_pattern_1595", "drain_pattern_533"],
    ]

    # 分析每个序列
    for i, sequence in enumerate(test_sequences, 1):
        print(f"=== 分析序列 {i} ===")
        result = analyzer.analyze_sequence(sequence)

        print(result.summary)

        if result.critical_transitions:
            print(f"\n详细的关键转换:")
            for j, transition in enumerate(result.critical_transitions, 1):
                print(f"{j:2d}. 位置{transition.position:2d}: " f"{transition.from_template_id} → {transition.to_template_id}")
                print(f"     失败率: {transition.failure_rate:.3f} | " f"相对风险: {transition.relative_risk:.2f}x | " f"严重性: {transition.severity}")

        print("-" * 80)


if __name__ == "__main__":
    main()
