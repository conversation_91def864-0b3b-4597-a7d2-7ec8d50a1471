#!/usr/bin/env python3
"""
简化的端到端失败分析API
提供简单易用的接口进行失败检测和关键转换识别
"""

import os
import sys
from typing import List, Dict, Tuple, Optional

# 添加路径
BASE_PATH = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if BASE_PATH not in sys.path:
    sys.path.append(BASE_PATH)

from control_log_diagnose.src.end_to_end_failure_analyzer import EndToEndFailureAnalyzer, FailureTransition, AnalysisResult


class FailureAnalysisAPI:
    """简化的失败分析API"""

    def __init__(self):
        """初始化API"""
        self.analyzer = EndToEndFailureAnalyzer()
        print("🚀 失败分析API已就绪")

    def predict_failure(self, sequence: List[str]) -> Dict:
        """
        预测序列是否失败

        Args:
            sequence: 模式序列，如 ['drain_pattern_1', 'drain_pattern_2', ...]

        Returns:
            Dict: {
                'is_failure': bool,
                'confidence': float,
                'prediction': str  # "SUCCESS" 或 "FAILURE"
            }
        """
        result = self.analyzer.analyze_sequence(sequence)

        return {"is_failure": result.is_failure, "confidence": result.confidence, "prediction": "FAILURE" if result.is_failure else "SUCCESS"}

    def find_critical_transitions(self, sequence: List[str]) -> List[Dict]:
        """
        查找关键失败转换

        Args:
            sequence: 模式序列

        Returns:
            List[Dict]: 关键转换列表，每个包含：
            {
                'position': int,
                'from_pattern': str,
                'to_pattern': str,
                'from_template_id': str,
                'to_template_id': str,
                'failure_rate': float,
                'relative_risk': float,
                'severity': str  # "high", "medium", "low"
            }
        """
        result = self.analyzer.analyze_sequence(sequence)

        transitions = []
        for t in result.critical_transitions:
            transitions.append(
                {
                    "position": t.position,
                    "from_pattern": t.from_pattern,
                    "to_pattern": t.to_pattern,
                    "from_template_id": t.from_template_id,
                    "to_template_id": t.to_template_id,
                    "failure_rate": t.failure_rate,
                    "relative_risk": t.relative_risk,
                    "severity": t.severity,
                }
            )

        return transitions

    def analyze(self, sequence: List[str]) -> Dict:
        """
        完整分析：预测 + 关键转换识别

        Args:
            sequence: 模式序列

        Returns:
            Dict: 完整分析结果
        """
        result = self.analyzer.analyze_sequence(sequence)

        # 提取关键转换
        critical_transitions = []
        for t in result.critical_transitions:
            critical_transitions.append(
                {
                    "position": t.position,
                    "from_pattern": t.from_pattern,
                    "to_pattern": t.to_pattern,
                    "from_template_id": t.from_template_id,
                    "to_template_id": t.to_template_id,
                    "failure_rate": round(t.failure_rate, 3),
                    "relative_risk": round(t.relative_risk, 2),
                    "severity": t.severity,
                }
            )

        # 统计严重性分布
        severity_count = {
            "high": len([t for t in result.critical_transitions if t.severity == "high"]),
            "medium": len([t for t in result.critical_transitions if t.severity == "medium"]),
            "low": len([t for t in result.critical_transitions if t.severity == "low"]),
        }

        return {
            # 预测结果
            "prediction": {"is_failure": result.is_failure, "confidence": round(result.confidence, 3), "result": "FAILURE" if result.is_failure else "SUCCESS"},
            # 序列信息
            "sequence_info": {
                "length": len(sequence),
                "unique_patterns": len(set(sequence)),
                "diversity_ratio": round(len(set(sequence)) / len(sequence), 3) if len(sequence) > 0 else 0,
            },
            # 关键转换
            "critical_transitions": critical_transitions,
            "transition_summary": {
                "total_critical": len(critical_transitions),
                "high_risk": severity_count["high"],
                "medium_risk": severity_count["medium"],
                "low_risk": severity_count["low"],
            },
            # 建议
            "recommendations": self._generate_recommendations(result),
        }

    def _generate_recommendations(self, result: AnalysisResult) -> List[str]:
        """生成建议"""
        recommendations = []

        if not result.is_failure:
            recommendations.append("✅ 序列预测为成功，无需特殊处理")
            return recommendations

        # 失败案例的建议
        high_risk = [t for t in result.critical_transitions if t.severity == "high"]
        medium_risk = [t for t in result.critical_transitions if t.severity == "medium"]

        if high_risk:
            recommendations.append(f"🚨 发现 {len(high_risk)} 个高风险转换，需要立即关注")
            # 找出最早出现的高风险转换
            earliest_high_risk = min(high_risk, key=lambda x: x.position)
            recommendations.append(
                f"💡 最早的高风险转换在位置 {earliest_high_risk.position}: " f"{earliest_high_risk.from_template_id} → {earliest_high_risk.to_template_id}"
            )

        if medium_risk:
            recommendations.append(f"⚠️ 发现 {len(medium_risk)} 个中等风险转换，建议监控")

        if len(result.critical_transitions) > 10:
            recommendations.append("📊 关键转换过多，建议检查序列流程是否正常")

        # 针对常见模式的建议
        template_ids = [t.to_template_id for t in result.critical_transitions if t.severity == "high"]
        common_templates = set([tid for tid in template_ids if template_ids.count(tid) > 1])

        if common_templates:
            recommendations.append(f"🔍 模板 {', '.join(common_templates)} 反复出现在高风险转换中，建议重点检查")

        return recommendations

    def batch_analyze(self, sequences: List[List[str]]) -> List[Dict]:
        """批量分析多个序列"""
        results = []
        for i, sequence in enumerate(sequences):
            if i % 100 == 0:
                print(f"📊 处理序列 {i+1}/{len(sequences)}")

            result = self.analyze(sequence)
            results.append(result)

        return results


def demo():
    """演示API用法"""
    print("=== 失败分析API演示 ===\n")

    # 初始化API
    api = FailureAnalysisAPI()

    # 测试序列
    test_cases = [
        {"name": "疑似成功案例", "sequence": ["drain_pattern_1", "drain_pattern_2", "drain_pattern_3", "drain_pattern_24"]},
        {"name": "疑似失败案例1", "sequence": ["drain_pattern_1", "drain_pattern_1593", "drain_pattern_3331", "drain_pattern_1594", "drain_pattern_1587"]},
        {"name": "疑似失败案例2", "sequence": ["drain_pattern_43", "drain_pattern_1596", "drain_pattern_292", "drain_pattern_585", "drain_pattern_535"]},
    ]

    for test_case in test_cases:
        print(f"📝 测试案例: {test_case['name']}")
        print("-" * 50)

        # 完整分析
        result = api.analyze(test_case["sequence"])

        # 打印结果
        pred = result["prediction"]
        print(f"🎯 预测结果: {pred['result']} (置信度: {pred['confidence']})")

        seq_info = result["sequence_info"]
        print(f"📊 序列信息: 长度={seq_info['length']}, 唯一模式={seq_info['unique_patterns']}, 多样性={seq_info['diversity_ratio']}")

        trans_summary = result["transition_summary"]
        if trans_summary["total_critical"] > 0:
            print(
                f"⚠️ 关键转换: 总共{trans_summary['total_critical']}个 "
                f"(高风险:{trans_summary['high_risk']}, 中风险:{trans_summary['medium_risk']}, 低风险:{trans_summary['low_risk']})"
            )

            print("🔍 前3个关键转换:")
            for i, transition in enumerate(result["critical_transitions"][:3], 1):
                print(
                    f"  {i}. 位置{transition['position']}: {transition['from_template_id']} → {transition['to_template_id']} "
                    f"(失败率:{transition['failure_rate']}, 风险:{transition['relative_risk']}x, 严重性:{transition['severity']})"
                )

        print("💡 建议:")
        for rec in result["recommendations"]:
            print(f"  - {rec}")

        print("\n" + "=" * 80 + "\n")


if __name__ == "__main__":
    demo()
