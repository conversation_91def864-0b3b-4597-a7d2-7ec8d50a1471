#!/usr/bin/env python3
"""
日志Embedding聚类主程序
支持命令行参数和配置文件
"""

import argparse
import yaml
import os
import sys
from pathlib import Path
from log_embedding_clustering import LogEmbeddingClustering, ClusteringConfig


def load_config(config_path: str) -> dict:
    """加载YAML配置文件"""
    with open(config_path, "r", encoding="utf-8") as f:
        return yaml.safe_load(f)


def create_config_from_yaml(yaml_config: dict, args: argparse.Namespace) -> ClusteringConfig:
    """从YAML配置创建ClusteringConfig对象"""

    # API配置
    api_key = args.api_key or yaml_config["api"].get("api_key") or os.getenv("DASHSCOPE_API_KEY")

    # 聚类方法
    clustering_methods = args.methods or yaml_config["clustering"]["methods"]

    # 输出目录
    output_dir = args.output_dir or yaml_config["output"]["output_dir"]

    config = ClusteringConfig(
        # API配置
        api_key=api_key,
        model=yaml_config["api"]["model"],
        dimension=yaml_config["api"]["dimension"],
        output_type=yaml_config["api"]["output_type"],
        # 数据配置
        log_column=args.log_column or yaml_config["data"]["log_column"],
        batch_size=yaml_config["data"]["batch_size"],
        max_retries=yaml_config["data"]["max_retries"],
        retry_delay=yaml_config["data"]["retry_delay"],
        
        # 预处理配置 (命令行参数可以覆盖配置文件)
        enable_preprocessing=not args.no_preprocessing and yaml_config.get("preprocessing", {}).get("enabled", True),
        preserve_structure=not args.no_structure_preservation and yaml_config.get("preprocessing", {}).get("preserve_structure", True),
        enable_content_replacement=not args.no_content_replacement and yaml_config.get("preprocessing", {}).get("enable_content_replacement", True),
        preprocess_before_embedding=not args.no_preprocessing and yaml_config.get("preprocessing", {}).get("preprocess_before_embedding", True),
        # 聚类配置
        clustering_methods=clustering_methods,
        auto_select_clusters=yaml_config["clustering"]["auto_select_clusters"],
        max_clusters=yaml_config["clustering"]["max_clusters"],
        # 输出配置
        save_embeddings=yaml_config["output"]["save_embeddings"],
        save_results=yaml_config["output"]["save_results"],
        output_dir=output_dir,
    )

    return config


def main():
    parser = argparse.ArgumentParser(description="日志Embedding聚类工具")

    # 必需参数
    parser.add_argument("input_file", help="输入文件路径 (CSV或Parquet格式)")

    # 可选参数
    parser.add_argument("--config", "-c", default="clustering_config.yaml", help="配置文件路径 (默认: clustering_config.yaml)")
    parser.add_argument("--api-key", help="DashScope API Key (覆盖配置文件)")
    parser.add_argument("--log-column", help="日志内容列名 (覆盖配置文件)")
    parser.add_argument("--output-dir", "-o", help="输出目录 (覆盖配置文件)")
    parser.add_argument("--methods", nargs="+", choices=["kmeans", "dbscan", "hierarchical", "optics", "gmm"], help="聚类方法 (覆盖配置文件)")
    parser.add_argument("--no-preprocessing", action="store_true", help="禁用日志预处理")
    parser.add_argument("--no-content-replacement", action="store_true", help="禁用内容替换")
    parser.add_argument("--no-structure-preservation", action="store_true", help="不保留日志结构")
    parser.add_argument("--no-visualization", action="store_true", help="跳过可视化")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")

    args = parser.parse_args()

    # 检查输入文件
    if not Path(args.input_file).exists():
        print(f"错误: 输入文件 '{args.input_file}' 不存在")
        sys.exit(1)

    # 检查配置文件
    if not Path(args.config).exists():
        print(f"错误: 配置文件 '{args.config}' 不存在")
        print("请先创建配置文件，或使用 clustering_config.yaml 模板")
        sys.exit(1)

    try:
        # 加载配置
        yaml_config = load_config(args.config)
        config = create_config_from_yaml(yaml_config, args)

        # 检查API Key
        if not config.api_key:
            print("错误: 未设置DashScope API Key")
            print("请通过以下方式之一设置:")
            print("1. 命令行参数: --api-key your-key")
            print("2. 环境变量: export DASHSCOPE_API_KEY=your-key")
            print("3. 配置文件: 在clustering_config.yaml中设置api_key")
            sys.exit(1)

        print("=" * 60)
        print("日志Embedding聚类开始")
        print("=" * 60)
        print(f"输入文件: {args.input_file}")
        print(f"日志列名: {config.log_column}")
        print(f"聚类方法: {', '.join(config.clustering_methods)}")
        print(f"输出目录: {config.output_dir}")
        print(f"Embedding维度: {config.dimension}")
        print("-" * 60)

        # 创建聚类器
        clusterer = LogEmbeddingClustering(config)

        # 运行聚类
        print("开始执行聚类流程...")
        results = clusterer.run_full_pipeline(args.input_file)

        # 显示结果摘要
        print("\n" + "=" * 60)
        print("聚类完成！结果摘要:")
        print("=" * 60)

        evaluation = results["evaluation"]
        print("\n聚类方法评估:")
        print(evaluation.to_string(index=False))

        # 找出最佳方法
        if not evaluation.empty and "silhouette_score" in evaluation.columns:
            valid_scores = evaluation.dropna(subset=["silhouette_score"])
            if not valid_scores.empty:
                best_idx = valid_scores["silhouette_score"].idxmax()
                best_method = evaluation.loc[best_idx, "method"]
                best_score = evaluation.loc[best_idx, "silhouette_score"]

                print(f"\n推荐方法: {best_method} (轮廓系数: {best_score:.3f})")

                # 显示最佳方法的聚类摘要
                summary = clusterer.get_cluster_summary(best_method, top_n=3)
                print(f"\n{best_method} 聚类详情:")
                for _, row in summary.head(10).iterrows():  # 只显示前10个聚类
                    print(f"  聚类 {row['cluster_id']}: {row['size']} 条日志 ({row['percentage']:.1f}%)")
                    if row["sample_logs"]:
                        print(f"    示例: {row['sample_logs'][0][:100]}...")

        # 显示保存的文件
        print(f"\n输出文件保存在: {config.output_dir}/")
        saved_files = results["saved_files"]
        for file_type, file_path in saved_files.items():
            print(f"  {file_type}: {file_path}")

        # 可视化提示
        if not args.no_visualization and yaml_config["output"]["visualization"]["enabled"]:
            print(f"\n可视化图片保存在: {config.output_dir}/")
            print("  格式: method_reduction-method_visualization.png")

        print("\n" + "=" * 60)
        print("聚类流程完成！")
        print("=" * 60)

    except Exception as e:
        print(f"错误: {e}")
        if args.verbose:
            import traceback

            traceback.print_exc()
        sys.exit(1)


def create_sample_config():
    """创建示例配置文件"""
    config_path = "clustering_config.yaml"
    if Path(config_path).exists():
        print(f"配置文件 {config_path} 已存在")
        return

    # 这里可以创建一个基本的配置文件
    sample_config = """# 日志Embedding聚类配置文件
api:
  api_key: null  # 设置你的DashScope API Key
  model: "text-embedding-v4"
  dimension: 1024
  output_type: "dense"

data:
  log_column: "log_content"
  batch_size: 100
  max_retries: 3
  retry_delay: 1.0

clustering:
  methods:
    - "kmeans"
    - "dbscan"
    - "hierarchical"
  auto_select_clusters: true
  max_clusters: 50

output:
  save_embeddings: true
  save_results: true
  output_dir: "clustering_results"
  visualization:
    enabled: true
    reduction_methods:
      - "tsne"
    save_plots: true
"""

    with open(config_path, "w", encoding="utf-8") as f:
        f.write(sample_config)

    print(f"已创建示例配置文件: {config_path}")


if __name__ == "__main__":
    # 如果没有参数，显示帮助信息
    if len(sys.argv) == 1:
        print("日志Embedding聚类工具")
        print("=" * 40)
        print("\n使用方法:")
        print("  python run_log_clustering.py input_file.csv")
        print("  python run_log_clustering.py input_file.parquet --methods kmeans dbscan")
        print("  python run_log_clustering.py input_file.csv --api-key your-key --output-dir results")
        print("\n配置文件:")
        print("  首次使用请确保有 clustering_config.yaml 配置文件")
        print("  可以参考 clustering_config.yaml 模板")
        print("\n示例:")
        print("  # 使用默认配置")
        print("  python run_log_clustering.py logs.csv")
        print()
        print("  # 指定聚类方法")
        print("  python run_log_clustering.py logs.csv --methods kmeans dbscan")
        print()
        print("  # 设置API Key和输出目录")
        print("  python run_log_clustering.py logs.parquet --api-key sk-xxx --output-dir my_results")
        print()

        # 询问是否创建示例配置
        try:
            response = input("是否创建示例配置文件? (y/N): ").strip().lower()
            if response in ["y", "yes"]:
                create_sample_config()
        except KeyboardInterrupt:
            print("\n退出")

        sys.exit(0)

    main()
