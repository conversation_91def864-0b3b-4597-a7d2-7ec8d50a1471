# %%
import os
import sys
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from collections import Counter, defaultdict

BASE_PATH = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if BASE_PATH not in sys.path:
    sys.path.append(BASE_PATH)

# 加载已处理的数据
data_path = os.path.join(BASE_PATH, "data/control_log/drain_pattern_results/ecs_regionmaster_info_log_with_pattern_ids.parquet")
data = pd.read_parquet(data_path, engine="pyarrow")

# 映射标签：-1 -> 失败(1), 0 -> 成功(0)
data["label_mapped"] = data["label"].map({-1: 1, 0: 0})
available_data = data[data["log_time"] - 10 < data["timestamp"]]

# 生成序列数据
pattern_sequences = (
    available_data.groupby(["request_id", "label_mapped"])
    .apply(lambda x: x.sort_values("log_time")["pattern_id"].tolist(), include_groups=False)
    .reset_index(name="pattern_sequence")
)

print(f"Loading sequence data: {len(pattern_sequences)} sequences")


# %%
# 创建桑基图来展示序列流向
def create_sankey_flow_diagram(sequences, label_name, max_stages=5, top_patterns=20):
    """创建桑基图展示序列流向"""

    # 只考虑长度足够的序列
    long_sequences = [seq for seq in sequences if len(seq) >= max_stages]
    print(f"{label_name}: 分析 {len(long_sequences)} 个长度 >= {max_stages} 的序列")

    if len(long_sequences) == 0:
        return None

    # 统计每个阶段最常见的patterns
    stage_patterns = {}
    for stage in range(max_stages):
        stage_counter = Counter()
        for seq in long_sequences:
            if stage < len(seq):
                stage_counter[seq[stage]] += 1
        # 取每个阶段最常见的patterns
        stage_patterns[stage] = [pattern.replace("drain_pattern_", "P") for pattern, _ in stage_counter.most_common(top_patterns)]

    # 创建节点
    nodes = []
    node_labels = []
    node_colors = []
    node_positions_x = []
    node_positions_y = []

    # 为每个阶段创建节点
    color_stages = ["lightblue", "lightgreen", "lightcoral", "lightyellow", "lightpink"]
    for stage in range(max_stages):
        stage_color = color_stages[stage % len(color_stages)]
        y_positions = np.linspace(0.1, 0.9, len(stage_patterns[stage]))

        for i, pattern in enumerate(stage_patterns[stage]):
            nodes.append(len(nodes))
            node_labels.append(f"S{stage+1}_{pattern}")
            node_colors.append(stage_color)
            node_positions_x.append(stage / (max_stages - 1))
            node_positions_y.append(y_positions[i])

    # 创建节点映射
    node_map = {label: idx for idx, label in enumerate(node_labels)}

    # 统计流向
    flows = defaultdict(int)
    for seq in long_sequences:
        for stage in range(max_stages - 1):
            if stage < len(seq) - 1:
                current = f"S{stage+1}_{seq[stage].replace('drain_pattern_', 'P')}"
                next_pattern = f"S{stage+2}_{seq[stage+1].replace('drain_pattern_', 'P')}"

                if current in node_map and next_pattern in node_map:
                    flows[(current, next_pattern)] += 1

    # 准备桑基图数据
    source = []
    target = []
    value = []

    for (source_node, target_node), flow_count in flows.items():
        if flow_count >= 5:  # 只显示足够频繁的流向
            source.append(node_map[source_node])
            target.append(node_map[target_node])
            value.append(flow_count)

    if len(source) == 0:
        print(f"{label_name}: 没有足够的流向数据")
        return None

    # 创建桑基图
    fig = go.Figure(
        data=[
            go.Sankey(
                node=dict(
                    pad=15, thickness=20, line=dict(color="black", width=0.5), label=node_labels, color=node_colors, x=node_positions_x, y=node_positions_y
                ),
                link=dict(source=source, target=target, value=value),
            )
        ]
    )

    fig.update_layout(title_text=f"{label_name} Case Sequence Flow (Top {top_patterns} patterns per stage)", font_size=10, height=600)

    return fig


# %%
# 为成功和失败案例创建桑基图
success_sequences = pattern_sequences[pattern_sequences["label_mapped"] == 0]["pattern_sequence"].tolist()
failure_sequences = pattern_sequences[pattern_sequences["label_mapped"] == 1]["pattern_sequence"].tolist()

print("创建成功案例的序列流向图...")
success_sankey = create_sankey_flow_diagram(success_sequences, "Success", max_stages=5, top_patterns=15)

print("创建失败案例的序列流向图...")
failure_sankey = create_sankey_flow_diagram(failure_sequences, "Failure", max_stages=5, top_patterns=15)

# 保存和显示桑基图
if success_sankey:
    success_sankey.write_html("/Users/<USER>/GitFolders/algorithm/success_sequence_flow.html")
    success_sankey.show()

if failure_sankey:
    failure_sankey.write_html("/Users/<USER>/GitFolders/algorithm/failure_sequence_flow.html")
    failure_sankey.show()


# %%
# 创建序列步骤对比分析
def analyze_step_by_step_differences():
    """分析每个步骤的pattern差异"""

    print("=== 步骤对比分析 ===")

    # 为了公平比较，只分析最常见的序列长度
    success_lengths = [len(seq) for seq in success_sequences]
    failure_lengths = [len(seq) for seq in failure_sequences]

    common_length = max(set(success_lengths + failure_lengths), key=lambda x: success_lengths.count(x) + failure_lengths.count(x))
    print(f"分析长度为 {common_length} 的序列")

    # 筛选指定长度的序列
    success_fixed = [seq for seq in success_sequences if len(seq) == common_length]
    failure_fixed = [seq for seq in failure_sequences if len(seq) == common_length]

    print(f"成功案例: {len(success_fixed)} 个序列")
    print(f"失败案例: {len(failure_fixed)} 个序列")

    # 分析每个步骤的pattern分布
    step_analysis = []

    for step in range(min(common_length, 10)):  # 最多分析前10步
        success_step_patterns = Counter([seq[step] for seq in success_fixed if step < len(seq)])
        failure_step_patterns = Counter([seq[step] for seq in failure_fixed if step < len(seq)])

        print(f"\n第 {step+1} 步:")
        print(f"  成功案例最常见: {success_step_patterns.most_common(3)}")
        print(f"  失败案例最常见: {failure_step_patterns.most_common(3)}")

        # 找出这一步最具区分性的patterns
        all_patterns = set(success_step_patterns.keys()) | set(failure_step_patterns.keys())
        distinctive_patterns = []

        for pattern in all_patterns:
            success_count = success_step_patterns.get(pattern, 0)
            failure_count = failure_step_patterns.get(pattern, 0)

            if success_count + failure_count >= 5:
                success_rate = success_count / len(success_fixed) if len(success_fixed) > 0 else 0
                failure_rate = failure_count / len(failure_fixed) if len(failure_fixed) > 0 else 0

                if failure_rate > 0:
                    ratio = success_rate / failure_rate
                elif success_rate > 0:
                    ratio = float("inf")
                else:
                    continue

                distinctive_patterns.append((pattern, success_count, failure_count, ratio))

        # 排序显示最具区分性的
        distinctive_patterns.sort(key=lambda x: abs(np.log(x[3]) if x[3] != float("inf") else 10), reverse=True)

        if distinctive_patterns:
            print(f"  最具区分性的patterns:")
            for pattern, s_count, f_count, ratio in distinctive_patterns[:3]:
                pattern_short = pattern.replace("drain_pattern_", "P")
                if ratio == float("inf"):
                    print(f"    {pattern_short}: 只在成功案例中出现 ({s_count} 次)")
                elif ratio == 0:
                    print(f"    {pattern_short}: 只在失败案例中出现 ({f_count} 次)")
                else:
                    print(f"    {pattern_short}: 成功={s_count}, 失败={f_count}, 比值={ratio:.2f}")

    return step_analysis


# 执行步骤对比分析
step_analysis = analyze_step_by_step_differences()


# %%
# 创建序列阶段热力图
def create_sequence_heatmap():
    """创建序列各阶段的pattern热力图"""

    # 选择前10个最常见的patterns用于热力图
    all_patterns = []
    for seq in success_sequences + failure_sequences:
        all_patterns.extend(seq)

    top_patterns = [pattern for pattern, _ in Counter(all_patterns).most_common(20)]
    pattern_names = [p.replace("drain_pattern_", "P") for p in top_patterns]

    # 创建热力图数据
    max_steps = 10
    success_heatmap = np.zeros((len(top_patterns), max_steps))
    failure_heatmap = np.zeros((len(top_patterns), max_steps))

    # 统计每个pattern在每个步骤的出现频率
    for seq in success_sequences:
        for step, pattern in enumerate(seq[:max_steps]):
            if pattern in top_patterns:
                pattern_idx = top_patterns.index(pattern)
                success_heatmap[pattern_idx, step] += 1

    for seq in failure_sequences:
        for step, pattern in enumerate(seq[:max_steps]):
            if pattern in top_patterns:
                pattern_idx = top_patterns.index(pattern)
                failure_heatmap[pattern_idx, step] += 1

    # 归一化
    success_heatmap = success_heatmap / len(success_sequences) * 100
    failure_heatmap = failure_heatmap / len(failure_sequences) * 100

    # 创建子图
    fig = make_subplots(
        rows=1, cols=2, subplot_titles=("Success Cases: Pattern Distribution by Step", "Failure Cases: Pattern Distribution by Step"), horizontal_spacing=0.15
    )

    # 成功案例热力图
    fig.add_trace(
        go.Heatmap(
            z=success_heatmap,
            x=[f"Step {i+1}" for i in range(max_steps)],
            y=pattern_names,
            colorscale="Blues",
            name="Success",
            colorbar=dict(title="Frequency (%)", x=0.45),
        ),
        row=1,
        col=1,
    )

    # 失败案例热力图
    fig.add_trace(
        go.Heatmap(
            z=failure_heatmap,
            x=[f"Step {i+1}" for i in range(max_steps)],
            y=pattern_names,
            colorscale="Reds",
            name="Failure",
            colorbar=dict(title="Frequency (%)", x=1.02),
        ),
        row=1,
        col=2,
    )

    fig.update_layout(title="Pattern Distribution Across Sequence Steps: Success vs Failure", height=600, showlegend=False)

    fig.write_html("/Users/<USER>/GitFolders/algorithm/sequence_steps_heatmap.html")
    fig.show()

    return fig


# 创建序列阶段热力图
sequence_heatmap = create_sequence_heatmap()

print("\n=== 序列流向分析完成 ===")
print("生成的可视化文件:")
print("- 成功案例序列流向: success_sequence_flow.html")
print("- 失败案例序列流向: failure_sequence_flow.html")
print("- 序列步骤热力图: sequence_steps_heatmap.html")

# %%
