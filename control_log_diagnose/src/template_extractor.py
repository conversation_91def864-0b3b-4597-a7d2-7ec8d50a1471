#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志模板提取系统
独立运行的日志模板提取和匹配工具

Author: Claude
Date: 2025
"""

import os
import sys
import re
import json
import argparse
import pandas as pd
import numpy as np
import dashscope
from pathlib import Path
from collections import defaultdict, Counter
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.cluster import KMeans
from tqdm import tqdm

# 导入Drain算法
try:
    from drain3 import TemplateMiner
    DRAIN_AVAILABLE = True
except ImportError:
    DRAIN_AVAILABLE = False
    print("警告: drain3库未安装，Drain算法功能将不可用")

# 添加项目根目录到路径
BASE_PATH = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if BASE_PATH not in sys.path:
    sys.path.append(BASE_PATH)


class LogTemplateExtractor:
    """日志模板提取器"""
    
    def __init__(self, api_key=None, model="qwen3-coder-plus"):
        """初始化模板提取器
        
        Args:
            api_key: DashScope API密钥
            model: 使用的LLM模型
        """
        self.model = model
        if api_key:
            dashscope.api_key = api_key
        
        # 预编译正则表达式以提高性能
        self.preprocess_patterns = self._compile_preprocess_patterns()
    
    def _compile_preprocess_patterns(self):
        """预编译用于日志预处理的正则表达式"""
        return {
            # 时间戳模式 (多种格式)
            'timestamps': [
                re.compile(r'\d{4}-\d{2}-\d{2}[T\s]\d{2}:\d{2}:\d{2}(?:\.\d{3})?(?:[+-]\d{4}|\.\d+[+-]\d{4}|Z)?'),  # ISO格式
                re.compile(r'\d{4}/\d{2}/\d{2}\s+\d{2}:\d{2}:\d{2}'),  # 斜杠分隔
                re.compile(r'\d{2}/\d{2}/\d{4}\s+\d{2}:\d{2}:\d{2}'),  # MM/dd/yyyy
                re.compile(r'\d{10,13}'),  # Unix时间戳
                re.compile(r'(?:Fri|Sat|Sun|Thu|Mon|Tue|Wed)\s+(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+\d{1,2}\s+\d{2}:\d{2}:\d{2}\s+CST\s+\d{2,4}'),  # CST格式
                re.compile(r'(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+\d{1,2}\s+[0-2]\d(?::[0-5]\d){2}'),  # syslog格式
                re.compile(r'\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3,6}(?:Z|[+-]\d{2}:\d{2})?'),  # 高精度ISO
            ],
            
            # 方括号内容模式
            'brackets': [
                re.compile(r'\[pid=\d+[^\]]*\]'),  # [pid=5580,iso=0]
                re.compile(r'\[\d{4}/\d{2}/\d{2}\s+\d{2}:\d{2}:\d{2}\]'),  # [2025/07/22 18:10:57]
                re.compile(r'\[\d{10,13}\]'),  # [1753179057331]
                re.compile(r'\[(?:debug|info|warn|error|fatal|trace)\]', re.IGNORECASE),  # [info], [ERROR]等
                re.compile(r'\[[^\]]*\d+[^\]]*\]'),  # 包含数字的其他方括号内容
            ],
            
            # 日志级别模式 (独立出现)
            'log_levels': re.compile(r'\b(?:DEBUG|INFO|WARN|WARNING|ERROR|FATAL|TRACE)\b:?\s*', re.IGNORECASE),
            
            # 进程/线程ID
            'process_info': [
                re.compile(r'\bpid[=:]\s*\d+\b', re.IGNORECASE),
                re.compile(r'\btid[=:]\s*\d+\b', re.IGNORECASE),
                re.compile(r'\bthread[=:]\s*\d+\b', re.IGNORECASE),
            ],
            
            # 其他常见的元信息
            'metadata': [
                re.compile(r'\b\w+\s*=\s*[a-f0-9-]{8,}\b'),  # 类似 requestId=abc123def
                re.compile(r'\b\w+Id\s*=\s*[\w-]+'),  # xxxId=something
            ],
            
            # === 新增的内容替换模式 ===
            # IP地址模式
            'ip_addresses': [
                re.compile(r'\b(?:\d{1,3}\.){3}\d{1,3}\b'),  # IPv4
                re.compile(r'\b(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}\b'),  # IPv6完整
                re.compile(r'\b(?:[0-9a-fA-F]{1,4}:){1,7}:(?:[0-9a-fA-F]{1,4})?'),  # IPv6简化
            ],
            
            # 实例ID和资源ID模式
            'instance_ids': [
                re.compile(r'\bi-[a-z0-9]{17}\b'),  # ECS实例ID: i-2ze9c4ik6b5vx3zqy3jq
                re.compile(r'\bINSTANCE_i-[a-z0-9]{17}\b'),  # INSTANCE_i-xxx
                re.compile(r'\bslb-[a-z0-9]{17}\b'),  # SLB实例ID
                re.compile(r'\brds-[a-z0-9]{17}\b'),  # RDS实例ID
                re.compile(r'\bvolume-[a-z0-9]{17}\b'),  # 磁盘卷ID
                # 云服务器VM实例ID (多种前缀)
                re.compile(r'(?:(?<=[^A-Za-z0-9-_])|^)(?:i-|hbm-|AY|eci-|cp-|ic-|BVT|bvt|wh|cn|wy|qy|hm|VM|al|uh|ay|tmpvm|ace|vm|houyiecsay)[-]?[a-zA-Z0-9-_]{8,62}(?=(?:[^A-Z0-9])|$)'),
                # 更多VM实例格式
                re.compile(r'(?:qemu-BVT-CTRL-EcsBasic|qemu-iso-i|qemu-i|eci-ops|vnd|acs|iso-i|iso-BVT-DATARS|qemu|eni|i|dh|gds|ri-cr|sg|sgr|hbm|AY|eci|cp|ic|BVT|wh|cn|wy|qy|hm|VM|al|uh|ay|tmpvm|ace|vm|sd|e)-[a-zA-Z0-9]{8,24}|qemu-virT_CheCK_enV_VM|qemu-CNforVM'),
                re.compile(r'(?:vm:|Vm:|VM:|vmName:|performance)[a-zA-Z0-9-]{8,36}'),
                # 测试VM实例
                re.compile(r'(?:BVT|(?:qemu-|fc-|zhj-|jingshu-|ebs)test|qemu-test-net)([a-zA-Z0-9]){0,10}(-[a-zA-Z0-9]{1,20}){1,6}'),
            ],
            
            # 文件路径模式
            'file_paths': [
                re.compile(r'[a-zA-Z]:\\(?:[^\\/:*?"<>|\r\n]+\\)*[^\\/:*?"<>|\r\n]*'),  # Windows路径
                re.compile(r'/(?:[^/\0\n\r\t]+/)*[^/\0\n\r\t]*'),  # Unix路径
                re.compile(r'\\\\[^\\]+\\[^\\]+(?:\\[^\\]*)*'),  # UNC路径
            ],
            
            # 端口号模式
            'ports': [
                re.compile(r'\bport\s+(\d{1,5})\b', re.IGNORECASE),
                re.compile(r':(\d{1,5})\b'),  # :8080
            ],
            
            # UUID和哈希值模式
            'uuids': [
                re.compile(r'\b[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\b'),  # UUID
                re.compile(r'\b[0-9a-fA-F]{32}\b'),  # MD5哈希
                re.compile(r'\b[0-9a-fA-F]{40}\b'),  # SHA1哈希
                re.compile(r'\b[0-9a-fA-F]{64}\b'),  # SHA256哈希
            ],
            
            # 数字和编号模式
            'numbers': [
                re.compile(r'\b\d{6,}\b'),  # 长数字 (6位以上)
                re.compile(r'\b\d+\.\d+\.\d+(?:\.\d+)*\b'),  # 版本号
                re.compile(r'\b\d+ms\b|\b\d+s\b|\b\d+MB\b|\b\d+KB\b', re.IGNORECASE),  # 数量单位
            ],
            
            # 会话和认证令牌
            'tokens': [
                re.compile(r'\b[A-Za-z0-9+/]{20,}={0,2}\b'),  # Base64编码
                re.compile(r'\btoken[_=:]\s*[A-Za-z0-9._-]+', re.IGNORECASE),
                re.compile(r'\bsessionid[_=:]\s*[A-Za-z0-9._-]+', re.IGNORECASE),
                re.compile(r'access_?[kK]ey_?[iI]d=(?:[\\dA-Za-z=\'/\\+]{16,128})?(?:\s|,|;|$)'),  # Access Key ID
                re.compile(r'sign=(?:[\\dA-Za-z=\'/\\+]{16,128})?(?:\s|,|;|$)'),  # 签名
            ],
            
            # 阿里云特定资源标识
            'alibaba_resources': [
                re.compile(r'(?:(?:MOCK-)?ASW-)[A-Z0-9-.]{12,}'),  # ASW资源
                re.compile(r'(?:[^a-zA-Z0-9_-]|^)(?:[a-zA-Z0-9_-]+\.)+(?:alibaba-inc|aliyun-inc|aliyuncs)\.com'),  # 阿里云内网域名
                re.compile(r'.+aliyuncs\.com'),  # 阿里云服务域名
                re.compile(r'(?:cn|ap|me|eu|us|rus)(-[A-Z0-9a-z]+)+'),  # 地域信息
                re.compile(r'[0-9a-z]{7,12}\.cloud\.[0-9a-z]{3,6}(?:\.na\d{1,4})?'),  # 主机名
                re.compile(r'(?:(?:CN\d{0,3}\.|)[0-9a-z]{8,12}\.(?:cloud|sqa)|ecs-onebox[0-9]{8,15})\.[0-9a-z]{3,6}(?:\.na\d{1,4})?'),  # CN节点ID
            ],
            
            # 网络和硬件标识
            'network_hardware': [
                re.compile(r'[\da-fA-F]{2}(?::[\da-fA-F]{2}){5}'),  # MAC地址
                re.compile(r'(?:Ethernet)[0-9]{0,3}'),  # 以太网接口
                re.compile(r'drive-virtio-disk[0-9]{1,3}'),  # 虚拟磁盘设备
                re.compile(r'(?:virt|virtio-disk|net|vcpu|cache|nvme-disk|vf\.|pci\.\d{1,3}-net|pci\.\d{1,3}-(?:virtio|nvme)-disk)\d{1,6}'),  # 虚拟设备ID
                re.compile(r'[0-9a-f]{2,4}(?::[0-9a-f]{2,6}){5,10}'),  # 设备ID
                re.compile(r'[0-9]{4}:[0-9a-f]{2}:[0-9a-f]{2}\.[0-9]'),  # PCI设备ID
            ],
            
            # 内存地址和指针
            'memory_addresses': [
                re.compile(r'(at |\[<{0,1})*([0-9a-f]{8}`{0,1}[0-9a-f]{8})(>{0,1}\])*'),  # 内存地址
                re.compile(r'\+*0x[0-9a-f]{1,16}(/0x[0-9a-f]{1,16})*'),  # 十六进制地址
                re.compile(r'(?:^|[< ,\[\-:])(?:(?!CC|add|fade|be|bad|Add|ADD|Bad|BAD|BE|Be|Fade|FADE)[\da-fA-F]{2,20}(?:[ \]\-,.>:]|$)){1,10}'),  # 通用十六进制
                re.compile(r'(?:^|[ \[\(=])(?:0x)?0x[A-Fa-f0-9]{1,16}(?:[: ,\]\)]|$)'),  # 十六进制值
            ],
            
            # Java异常堆栈和反射
            'java_exceptions': [
                re.compile(r'\s(?:at )?[\d\w]{1,50}(?:\.[<>/\w\d$]+){1,10}\((?:(?:[$\w\d]{1,100}\.java(?::\d{1,5})?)|(?:<generated>)|(?:Native Method)|(?:Unknown Source))\)'),  # Java堆栈
                re.compile(r'\t\.\.\. \d{1,5} (?:more|common frames omitted)'),  # 省略的堆栈
                re.compile(r'sun\.reflect\.GeneratedConstructorAccessor[0-9]{0,5}\.newInstance'),  # 反射构造器
                re.compile(r'sun\.reflect\.GeneratedMethodAccessor[0-9]{0,5}\.invoke'),  # 反射方法
                re.compile(r' ~?\[na: ?\d+\.\d+\.\d+(?:_\d+)?\]'),  # jar版本信息
                re.compile(r' ~?\[(?:[a-z_0-9]+\-)+\d+\.\d+\.\d+(?:[\.-][\d-]+)?(?:\-FASTJSON|\-SNAPSHOT|\.RELEASE|\.noneautotype|\-ahas|-bugfix\.?\d*|-stable\.?\d*|(?:\-[a-z_0-9]+)+)?\.jar(?:!/)?:(?:na|\d+\.\d+\.\d+(?:\.RELEASE|-SNAPSHOT)?)\]'),
            ],
            
            # 容器和集群标识
            'containers': [
                re.compile(r'docker[0-9]{12}_na[0-9]{2}_xdragon_diagnose_center(_[0-9]{4}){2}'),  # Docker容器ID
                re.compile(r'DEFAULT\.[a-f0-9]{12}-[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}'),  # 作业ID
                re.compile(r're-[a-f0-9]{8}(?:-[a-f0-9]{4}){3}-[a-f0-9]{12}'),  # 发布ID
            ],
            
            # URL和服务端点
            'urls_endpoints': [
                re.compile(r'(?:http[s]?|dubbo)://(?:\d{1,3}(?:\.\d{1,3}){3}|[\w-]{2,20}(?:\.[\w-]{2,20}){0,4})(?::\d{1,5})?(?:/[\w\-#\.]{1,50}){1,10}'),  # HTTP/Dubbo URL
                re.compile(r'["\'(,]?(?:https?|dubbo)://\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}(?::\d{1,5})?(?:/\w{1,32}){0,10}(?:\.\w{1,50}){0,20}["\',:)]?'),  # IP格式URL
                re.compile(r'(?:dubbo|aliyun-nacos-ns)://.+/.+\?[_a-zA-Z0-9.-]+=[_a-zA-Z0-9-.]+(?:&[_a-zA-Z0-9.-]+=[^=&]{0,})+(?:, | |$)'),  # Dubbo配置URL
                re.compile(r'\?[^&= ]{1,20}=[^& ]{0,100}(?:(?:(?:&[^&= ]{1,30}=[^& ]{0,1000}){1,100})|\])(?:[, ;\.]|$)'),  # URL参数
            ]
        }
    
    def preprocess_log(self, log_text, preserve_structure=True, enable_content_replacement=True):
        """预处理单条日志
        
        Args:
            log_text: 原始日志文本
            preserve_structure: 是否保留基本结构（如冒号、等号等）
            enable_content_replacement: 是否启用内容替换（将显著内容替换为占位符）
            
        Returns:
            str: 预处理后的日志文本
        """
        if not log_text or not isinstance(log_text, str):
            return ""
        
        # 1. 去除换行符和回车符
        cleaned = log_text.replace('\r\n', ' ').replace('\r', ' ').replace('\n', ' ')
        
        # 2. 内容替换阶段（在去除其他内容之前进行）
        if enable_content_replacement:
            # 替换IP地址
            for ip_pattern in self.preprocess_patterns['ip_addresses']:
                cleaned = ip_pattern.sub('<IP>', cleaned)
            
            # 替换实例ID和资源ID
            for instance_pattern in self.preprocess_patterns['instance_ids']:
                cleaned = instance_pattern.sub('<INSTANCE_ID>', cleaned)
            
            # 替换文件路径
            for path_pattern in self.preprocess_patterns['file_paths']:
                cleaned = path_pattern.sub('<PATH>', cleaned)
            
            # 替换端口号
            for port_pattern in self.preprocess_patterns['ports']:
                if 'port' in port_pattern.pattern.lower():
                    cleaned = port_pattern.sub(r'port <PORT>', cleaned)
                else:
                    cleaned = port_pattern.sub(r':<PORT>', cleaned)
            
            # 替换UUID和哈希值
            for uuid_pattern in self.preprocess_patterns['uuids']:
                cleaned = uuid_pattern.sub('<UUID>', cleaned)
            
            # 替换数字和编号
            for number_pattern in self.preprocess_patterns['numbers']:
                if 'ms\\b|s\\b|MB\\b|KB\\b' in number_pattern.pattern:
                    # 保留单位，只替换数字
                    cleaned = re.sub(r'\b(\d+)(ms|s|MB|KB)\b', r'<NUM>\2', cleaned, flags=re.IGNORECASE)
                elif r'\d+\.\d+\.\d+' in number_pattern.pattern:
                    cleaned = number_pattern.sub('<VERSION>', cleaned)
                else:
                    cleaned = number_pattern.sub('<NUM>', cleaned)
            
            # 替换令牌和会话信息
            for token_pattern in self.preprocess_patterns['tokens']:
                if 'token' in token_pattern.pattern.lower():
                    cleaned = token_pattern.sub('token=<TOKEN>', cleaned)
                elif 'sessionid' in token_pattern.pattern.lower():
                    cleaned = token_pattern.sub('sessionid=<SESSION>', cleaned)
                elif 'access' in token_pattern.pattern.lower():
                    cleaned = token_pattern.sub('access_key_id=<ACCESS_KEY>', cleaned)
                elif 'sign' in token_pattern.pattern.lower():
                    cleaned = token_pattern.sub('sign=<SIGN>', cleaned)
                else:
                    cleaned = token_pattern.sub('<TOKEN>', cleaned)
            
            # 替换阿里云特定资源标识
            for alibaba_pattern in self.preprocess_patterns['alibaba_resources']:
                if 'ASW' in alibaba_pattern.pattern:
                    cleaned = alibaba_pattern.sub('<ASW_RESOURCE>', cleaned)
                elif 'aliyuncs' in alibaba_pattern.pattern or 'alibaba-inc' in alibaba_pattern.pattern:
                    cleaned = alibaba_pattern.sub('<ALIBABA_DOMAIN>', cleaned)
                elif 'cloud' in alibaba_pattern.pattern:
                    cleaned = alibaba_pattern.sub('<CLOUD_HOST>', cleaned)
                else:
                    cleaned = alibaba_pattern.sub('<REGION>', cleaned)
            
            # 替换网络和硬件标识
            for network_pattern in self.preprocess_patterns['network_hardware']:
                if 'da-fA-F' in network_pattern.pattern and ':' in network_pattern.pattern:
                    cleaned = network_pattern.sub('<MAC>', cleaned)
                elif 'Ethernet' in network_pattern.pattern:
                    cleaned = network_pattern.sub('<ETHERNET>', cleaned)
                elif 'drive-virtio' in network_pattern.pattern:
                    cleaned = network_pattern.sub('<VDISK>', cleaned)
                elif 'pci' in network_pattern.pattern or 'virtio' in network_pattern.pattern:
                    cleaned = network_pattern.sub('<DEVICE>', cleaned)
                else:
                    cleaned = network_pattern.sub('<DEVICE_ID>', cleaned)
            
            # 替换内存地址和指针
            for addr_pattern in self.preprocess_patterns['memory_addresses']:
                cleaned = addr_pattern.sub('<ADDR>', cleaned)
            
            # 替换Java异常堆栈
            for java_pattern in self.preprocess_patterns['java_exceptions']:
                if 'at ' in java_pattern.pattern:
                    cleaned = java_pattern.sub('\tat <JAVA_STACK>', cleaned)
                elif '...' in java_pattern.pattern:
                    cleaned = java_pattern.sub('\t... <MORE_FRAMES>', cleaned)
                elif 'reflect' in java_pattern.pattern:
                    cleaned = java_pattern.sub('<REFLECTION>', cleaned)
                else:
                    cleaned = java_pattern.sub('<JAR_INFO>', cleaned)
            
            # 替换容器和集群标识
            for container_pattern in self.preprocess_patterns['containers']:
                if 'docker' in container_pattern.pattern:
                    cleaned = container_pattern.sub('<DOCKER_ID>', cleaned)
                elif 'DEFAULT' in container_pattern.pattern:
                    cleaned = container_pattern.sub('<JOB_ID>', cleaned)
                else:
                    cleaned = container_pattern.sub('<RELEASE_ID>', cleaned)
            
            # 替换URL和服务端点
            for url_pattern in self.preprocess_patterns['urls_endpoints']:
                if 'dubbo' in url_pattern.pattern:
                    cleaned = url_pattern.sub('<DUBBO_URL>', cleaned)
                elif '?' in url_pattern.pattern and '&' in url_pattern.pattern:
                    cleaned = url_pattern.sub('<URL_PARAMS>', cleaned)
                else:
                    cleaned = url_pattern.sub('<URL>', cleaned)
        
        # 3. 去除时间戳
        for timestamp_pattern in self.preprocess_patterns['timestamps']:
            cleaned = timestamp_pattern.sub('', cleaned)
        
        # 4. 去除方括号内容
        for bracket_pattern in self.preprocess_patterns['brackets']:
            cleaned = bracket_pattern.sub('', cleaned)
        
        # 5. 去除日志级别
        cleaned = self.preprocess_patterns['log_levels'].sub('', cleaned)
        
        # 6. 去除进程/线程信息
        for process_pattern in self.preprocess_patterns['process_info']:
            cleaned = process_pattern.sub('', cleaned)
        
        # 7. 去除其他元信息（可选）
        if not preserve_structure:
            for metadata_pattern in self.preprocess_patterns['metadata']:
                cleaned = metadata_pattern.sub('', cleaned)
        
        # 8. 清理多余空格
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()
        
        # 9. 去除开头和结尾的特殊字符
        cleaned = cleaned.strip(' .,;:-_=')
        
        # 10. 最终清理连续的占位符
        cleaned = re.sub(r'(<[^>]+>)\s+\1', r'\1', cleaned)  # 去除重复的相同占位符
        
        return cleaned
    
    def batch_preprocess_logs(self, logs, show_examples=True, enable_content_replacement=True):
        """批量预处理日志
        
        Args:
            logs: 日志列表
            show_examples: 是否显示预处理示例
            enable_content_replacement: 是否启用内容替换
            
        Returns:
            list: 预处理后的日志列表
        """
        if not logs:
            return []
        
        preprocessed_logs = []
        skipped_count = 0
        
        for log in logs:
            processed = self.preprocess_log(log, enable_content_replacement=enable_content_replacement)
            
            if processed:  # 只保留非空的处理结果
                preprocessed_logs.append(processed)
                
                # 显示前几个预处理示例
                if show_examples and len(preprocessed_logs) <= 3:
                    print(f"预处理示例 {len(preprocessed_logs)}:")
                    print(f"  原始: {log}")
                    print(f"  处理后: {processed}")
                    print()
            else:
                skipped_count += 1
        
        replacement_status = "启用内容替换" if enable_content_replacement else "仅清理格式"
        print(f"预处理完成 ({replacement_status}): 处理 {len(logs)} 条 -> 保留 {len(preprocessed_logs)} 条 (跳过 {skipped_count} 条空日志)")
        
        return preprocessed_logs
        
    def load_processed_data(self, data_path):
        """加载已处理的日志数据
        
        Args:
            data_path: 数据文件路径，可以是：
                - .parquet文件
                - .csv文件  
                - 包含多个logstore文件的目录
                
        Returns:
            dict: {logstore_name: [{"content": log_content, ...}, ...]}
        """
        data_path = Path(data_path)
        
        if data_path.is_file():
            # 单个文件
            logstore_name = data_path.stem
            if data_path.suffix == '.parquet':
                df = pd.read_parquet(data_path)
            elif data_path.suffix == '.csv':
                df = pd.read_csv(data_path)
            else:
                raise ValueError(f"不支持的文件格式: {data_path.suffix}")
            
            return {logstore_name: df.to_dict('records')}
        
        elif data_path.is_dir():
            # 目录中的多个文件
            data = {}
            for file_path in data_path.glob("*.parquet"):
                logstore_name = file_path.stem
                df = pd.read_parquet(file_path)
                data[logstore_name] = df.to_dict('records')
            
            for file_path in data_path.glob("*.csv"):
                logstore_name = file_path.stem
                if logstore_name not in data:  # 优先使用parquet
                    df = pd.read_csv(file_path)
                    data[logstore_name] = df.to_dict('records')
            
            return data
        
        else:
            raise FileNotFoundError(f"数据路径不存在: {data_path}")

    def rough_group_logs(self, logs, n_groups=5):
        """粗筛日志，基于TF-IDF和K-means进行分组"""
        if len(logs) <= n_groups:
            return {i: [logs[i]] for i in range(len(logs))}

        try:
            # 使用TF-IDF向量化
            vectorizer = TfidfVectorizer(max_features=100, stop_words=None, lowercase=True)
            tfidf_matrix = vectorizer.fit_transform(logs)

            # K-means聚类
            kmeans = KMeans(n_clusters=min(n_groups, len(logs)), random_state=42, n_init=10)
            clusters = kmeans.fit_predict(tfidf_matrix)

            # 按聚类分组
            groups = defaultdict(list)
            for i, cluster_id in enumerate(clusters):
                groups[cluster_id].append(logs[i])

            return dict(groups)
        except:
            # 如果聚类失败，按长度分组
            sorted_logs = sorted(enumerate(logs), key=lambda x: len(x[1]))
            groups = defaultdict(list)
            for i, (_, log) in enumerate(sorted_logs):
                groups[i % n_groups].append(log)
            return dict(groups)

    def llm_query_template(self, data: list):
        """调用LLM提取日志模板"""
        data_str = "\n".join(data)
        user_content = f"""**Input Logs:**
        
        {data_str[:2000]}
        
        **Your Task:**
        Generate a single log template based on the rules and examples provided.
        """
        
        # 最终的 messages 结构
        messages = [
            {
                "role": "system",
                "content": """You are a highly specialized log analysis engine. Your sole task is to analyze raw log entries and extract a generalized, structured template.

### Core Rule
Identify the static text versus the variable parts of a log. Replace variable parts with a placeholder in the format `<|variable_type|>`, where `variable_type` describes the semantic meaning of the variable data (e.g., `ip_address`, `session_id`, `error_code`).

### Placeholder Dictionary
You MUST use the following specific placeholders when applicable:
- **IP Addresses**: `<|ip_address|>` (e.g., `***********`, `2001:0db8::8a2e:0370:7334`)
- **File Paths**: `<|path|>` (e.g., `/var/log/app.log`, `C:\\Users\\<USER>\\file.txt`)
- **SQL Queries**: `<|sql_query|>` (e.g., `SELECT * FROM users WHERE id = 123`)
- **Command Lines**: `<|commandline|>` (e.g., `ssh user@host -p 22`)
- **Stack Traces**: For any multi-line stack trace (from Java, Python, etc.), replace the ENTIRE trace block with the single placeholder `<|stack_trace|>`.

For other common variables, use descriptive placeholders like `<|uuid|>`, `<|session_id|>`, `<|timestamp|>`, `<|number|>`, `<|string|>`, etc.

### Quality & Formatting Rules
1.  **Meaningful Templates**: A template MUST contain some static, meaningful text. A template consisting only of placeholders (e.g., `<|timestamp|> <|string|> <|string|>`) is NOT acceptable. If a log has no discernible static structure, you MUST return the exact string `None`.
2.  **Single Output**: Return exactly ONE template string per request.
3.  **Clean Response**: The response must contain ONLY the template string itself. Do not include any explanations, markdown formatting (like ` ``` `), or any other text.
4.  **Regex Compatibility**: The generated template should be easily convertible to a regex pattern. The placeholders `<|...|>` serve as named capture groups.

### Example
**Input Log:**
`2023-10-27 10:30:00,123 INFO [thread-1] com.example.Service - Service request from ************* for user_id=abc-123 completed in 55 ms.`

**Correct Output Template:**
`<|other|> INFO [thread-1] com.example.Service - Service request from <|ip_address|> for user_id=<|string|> completed in <|number|> ms.`
"""
            },
            {
                "role": "user",
                "content": user_content
            }
        ]

        try:
            response = dashscope.Generation.call(model=self.model, messages=messages, result_format="message")
            template = response.output.choices[0].message.content.strip()
            return template if template and template != "None" else None
        except Exception as e:
            print(f"LLM查询错误: {e}")
            return None

    def template_to_regex(self, template, flexible=True):
        """将模板转换为正则表达式
        
        Args:
            template: 模板字符串
            flexible: 是否启用灵活匹配模式
        """
        if not template:
            return None

        # 先处理变量占位符
        patterns = {
            r'<\|commandline\|>': r'PLACEHOLDER_COMMANDLINE',
            r'<\|ip_address\|>': r'PLACEHOLDER_IP',
            r'<\|sql_query\|>': r'PLACEHOLDER_SQL',
            r'<\|path\|>': r'PLACEHOLDER_PATH',
            r'<\|stack_trace\|>': r'PLACEHOLDER_STACKTRACE',
            r'<\|TRACE\|>': r'PLACEHOLDER_TRACE',
            r'<\|other\|>': r'PLACEHOLDER_OTHER',
            r'<\|[^|]+\|>': r'PLACEHOLDER_GENERIC'  # 通用变量匹配
        }

        # 第一步：用占位符替换变量
        working_template = template
        for placeholder, replacement in patterns.items():
            working_template = re.sub(placeholder, replacement, working_template)

        # 第二步：转义正则表达式特殊字符
        escaped = re.escape(working_template)

        # 第三步：将占位符替换为实际的正则表达式
        if flexible:
            # 灵活匹配模式 - 更宽松的正则表达式
            final_patterns = {
                r'PLACEHOLDER_COMMANDLINE': r'[^\s]+',
                r'PLACEHOLDER_IP': r'(?:\d{1,3}\.){3}\d{1,3}',
                r'PLACEHOLDER_SQL': r'.+?',
                r'PLACEHOLDER_PATH': r'[^\s]+',
                r'PLACEHOLDER_STACKTRACE': r'.*?',
                r'PLACEHOLDER_TRACE': r'.*?',
                r'PLACEHOLDER_OTHER': r'[^\s]*(?:\s+[^\s]*)*?',  # 更灵活的匹配，可以包含空格
                r'PLACEHOLDER_GENERIC': r'[^\s]*(?:\s+[^\s]*)*?'
            }
        else:
            # 严格匹配模式
            final_patterns = {
                r'PLACEHOLDER_COMMANDLINE': r'[^\s]+',
                r'PLACEHOLDER_IP': r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}',
                r'PLACEHOLDER_SQL': r'.+?',
                r'PLACEHOLDER_PATH': r'[^\s]+',
                r'PLACEHOLDER_STACKTRACE': r'.+?',
                r'PLACEHOLDER_TRACE': r'.+?',
                r'PLACEHOLDER_OTHER': r'.+?',
                r'PLACEHOLDER_GENERIC': r'.+?'
            }

        regex_pattern = escaped
        for placeholder, regex in final_patterns.items():
            regex_pattern = regex_pattern.replace(placeholder, regex)

        # 如果是灵活模式，添加一些通用的优化
        if flexible:
            # 处理多余空格
            regex_pattern = re.sub(r'\\s\+', r'\\s+', regex_pattern)
            # 在行尾添加可选的空白字符
            regex_pattern += r'\s*'

        try:
            # 验证正则表达式
            re.compile(regex_pattern)
            return regex_pattern
        except re.error as e:
            print(f"正则表达式编译错误: {e}")
            return None

    def calculate_similarity(self, template, log):
        """计算模板和日志的相似度"""
        # 移除变量占位符进行基本文本比较
        template_cleaned = re.sub(r'<\|[^|]+\|>', '', template)
        
        # 计算编辑距离（简化版）
        def levenshtein_ratio(s1, s2):
            if len(s1) == 0:
                return len(s2)
            if len(s2) == 0:
                return len(s1)
            
            # 简化的相似度计算
            common_chars = sum(1 for c1, c2 in zip(s1.lower(), s2.lower()) if c1 == c2)
            return common_chars / max(len(s1), len(s2))
        
        return levenshtein_ratio(template_cleaned, log)

    def smart_match_logs_with_template(self, logs, template, min_matches=2, similarity_threshold=0.3, verbose=False):
        """智能匹配日志与模板，包含多种匹配策略"""
        if verbose:
            print(f"使用智能模板匹配: {template}")
            print(f"最小匹配数量: {min_matches}")
            print(f"相似度阈值: {similarity_threshold}")
            print(f"日志数量: {len(logs)}")

        # 处理多行模板
        template_lines = [line.strip() for line in template.strip().split('\n') if line.strip()]
        all_matched_logs = []
        
        for i, single_template in enumerate(template_lines):
            if verbose:
                print(f"\n=== 处理模板 {i+1}: {single_template} ===")
            
            matched_logs = []
            
            # 策略1: 灵活正则匹配
            regex_pattern_flexible = self.template_to_regex(single_template, flexible=True)
            if regex_pattern_flexible:
                try:
                    pattern = re.compile(regex_pattern_flexible, re.DOTALL | re.IGNORECASE)
                    for log in logs:
                        if pattern.search(log):
                            matched_logs.append(log)
                    if verbose:
                        print(f"灵活正则匹配: {len(matched_logs)} 条")
                except Exception as e:
                    if verbose:
                        print(f"灵活正则匹配错误: {e}")
            
            # 策略2: 严格正则匹配（如果灵活匹配结果太少）
            if len(matched_logs) < min_matches:
                if verbose:
                    print("尝试严格正则匹配...")
                strict_matched = []
                regex_pattern_strict = self.template_to_regex(single_template, flexible=False)
                if regex_pattern_strict:
                    try:
                        pattern = re.compile(regex_pattern_strict, re.DOTALL)
                        for log in logs:
                            if pattern.search(log) and log not in matched_logs:
                                strict_matched.append(log)
                        matched_logs.extend(strict_matched)
                        if verbose:
                            print(f"严格正则匹配新增: {len(strict_matched)} 条")
                    except Exception as e:
                        if verbose:
                            print(f"严格正则匹配错误: {e}")
            
            # 策略3: 相似性匹配（如果正则匹配结果仍然不足）
            if len(matched_logs) < min_matches and similarity_threshold > 0:
                if verbose:
                    print("尝试相似性匹配...")
                similarity_matched = []
                for log in logs:
                    if log not in matched_logs:
                        similarity = self.calculate_similarity(single_template, log)
                        if similarity >= similarity_threshold:
                            similarity_matched.append(log)
                            if verbose and len(similarity_matched) <= 3:
                                print(f"  相似性 {similarity:.3f}: {log[:100]}...")
                
                matched_logs.extend(similarity_matched)
                if verbose:
                    print(f"相似性匹配新增: {len(similarity_matched)} 条")
            
            # 策略4: 关键词匹配（最后的备选方案）
            if len(matched_logs) < min_matches:
                if verbose:
                    print("尝试关键词匹配...")
                # 提取模板中的关键词（非变量部分）
                keywords = []
                template_parts = re.split(r'<\|[^|]+\|>', single_template)
                for part in template_parts:
                    words = [w.strip() for w in part.split() if len(w.strip()) > 2]
                    keywords.extend(words)
                
                if keywords:
                    keyword_matched = []
                    for log in logs:
                        if log not in matched_logs:
                            # 检查是否包含所有关键词
                            match_count = sum(1 for kw in keywords if kw.lower() in log.lower())
                            if match_count >= len(keywords) * 0.7:  # 至少70%的关键词匹配
                                keyword_matched.append(log)
                    
                    matched_logs.extend(keyword_matched[:min_matches])  # 限制数量
                    if verbose:
                        print(f"关键词匹配 {keywords}: {len(keyword_matched)} 条")
            
            if verbose:
                print(f"模板 {i+1} 总匹配: {len(matched_logs)} 条日志")
                if matched_logs:
                    print(f"匹配示例: {matched_logs[:2]}")
            
            all_matched_logs.extend(matched_logs)
        
        # 去重
        all_matched_logs = list(set(all_matched_logs))
        
        if verbose:
            print(f"\n=== 总匹配结果 ===")
            print(f"匹配日志总数: {len(all_matched_logs)}")
            print(f"匹配率: {len(all_matched_logs)/len(logs)*100:.1f}%")
        
        is_valid = len(all_matched_logs) >= min_matches
        return all_matched_logs, is_valid
    
    def extract_templates_with_drain(self, logs, depth=4, sim_th=0.4, max_children=100, verbose=False):
        """使用Drain算法提取日志模板
        
        Args:
            logs: 日志列表
            depth: Drain树的深度
            sim_th: 相似度阈值
            max_children: 每个节点的最大子节点数
            verbose: 是否显示详细信息
            
        Returns:
            dict: {template_id: {'template': str, 'logs': list, 'cluster_size': int}}
        """
        if not DRAIN_AVAILABLE:
            print("错误: drain3库未安装，无法使用Drain算法")
            return {}
            
        if verbose:
            print(f"使用Drain算法处理 {len(logs)} 条日志")
            print(f"参数: depth={depth}, sim_th={sim_th}, max_children={max_children}")
        
        # 初始化Drain模板挖掘器
        # 使用默认配置，只传入关键参数
        try:
            template_miner = TemplateMiner()
            # 更新Drain参数
            template_miner.drain.depth = depth
            template_miner.drain.sim_th = sim_th
            template_miner.drain.max_children = max_children
        except Exception as e:
            if verbose:
                print(f"Drain初始化警告: {e}")
            template_miner = TemplateMiner()
        
        # 处理每条日志
        log_clusters = {}  # cluster_id -> log_list
        
        for i, log in enumerate(logs):
            if not log or not isinstance(log, str):
                continue
                
            # 添加日志到Drain
            result = template_miner.add_log_message(log)
            cluster_id = result["cluster_id"]
            
            # 记录日志所属的聚类
            if cluster_id not in log_clusters:
                log_clusters[cluster_id] = []
            log_clusters[cluster_id].append(log)
            
            if verbose and (i + 1) % 1000 == 0:
                print(f"已处理 {i + 1} 条日志，当前聚类数: {len(template_miner.drain.clusters)}")
        
        # 提取模板结果
        templates = {}
        try:
            # 获取所有聚类
            clusters_dict = dict(template_miner.drain.clusters)
            for cluster_id, cluster in clusters_dict.items():
                if cluster_id in log_clusters:
                    templates[cluster_id] = {
                        'template': cluster.get_template(),
                        'logs': log_clusters[cluster_id],
                        'cluster_size': cluster.size,
                        'cluster_id': cluster_id
                    }
        except Exception as e:
            if verbose:
                print(f"提取模板时出错: {e}")
            # 备用方式：直接从log_clusters创建基本模板
            for cluster_id, logs_list in log_clusters.items():
                if logs_list:
                    templates[cluster_id] = {
                        'template': logs_list[0],  # 使用第一条日志作为模板
                        'logs': logs_list,
                        'cluster_size': len(logs_list),
                        'cluster_id': cluster_id
                    }
        
        if verbose:
            print(f"Drain算法完成，提取到 {len(templates)} 个模板")
            for i, (cluster_id, template_info) in enumerate(list(templates.items())[:5]):
                print(f"  模板 {cluster_id}: {template_info['template']} ({len(template_info['logs'])} logs)")
        
        return templates
    
    def extract_templates_with_drain_and_save_ids(self, data_path, output_dir=None, logstores=None, 
                                                  drain_params=None, verbose=False, enable_preprocessing=True, 
                                                  enable_content_replacement=True):
        """使用Drain算法提取模板并将pattern_id写回原始日志文件
        
        Args:
            data_path: 数据路径
            output_dir: 输出目录
            logstores: 要处理的logstore列表，None表示处理所有
            drain_params: Drain算法参数
            verbose: 是否显示详细信息
            enable_preprocessing: 是否启用日志预处理
            enable_content_replacement: 是否启用内容替换
            
        Returns:
            dict: 处理结果统计
        """
        # 设置输出目录
        if output_dir is None:
            output_dir = Path(BASE_PATH) / "data/control_log/drain_pattern_results"
        else:
            output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 设置默认Drain参数
        if drain_params is None:
            drain_params = {'depth': 4, 'sim_th': 0.4, 'max_children': 100}
        
        # 加载数据
        print("加载数据...")
        data = self.load_processed_data(data_path)
        print(f"加载完成，共 {len(data)} 个logstore")
        
        # 过滤logstore
        if logstores:
            data = {k: v for k, v in data.items() if k in logstores}
            print(f"过滤后剩余 {len(data)} 个logstore: {list(data.keys())}")
        
        all_stats = {}
        
        for logstore, logs_data in data.items():
            print(f"\n{'='*50}")
            print(f"处理 Logstore: {logstore}")
            
            # 创建DataFrame副本用于后续处理
            df = pd.DataFrame(logs_data)
            original_log_count = len(df)
            print(f"原始日志数量: {original_log_count}")
            
            # 过滤有效的日志内容
            valid_mask = df['content'].notna() & (df['content'].str.strip() != '')
            df_valid = df[valid_mask].copy()
            print(f"有效日志数量: {len(df_valid)}")
            
            if len(df_valid) == 0:
                print(f"跳过 {logstore}: 无有效日志内容")
                continue
            
            # 提取日志内容列表
            contents = df_valid['content'].tolist()
            
            # 日志预处理
            original_to_processed_map = {}  # 原始日志到预处理日志的映射
            processed_to_original_map = {}  # 预处理日志到原始日志的映射
            
            if enable_preprocessing:
                print("\n执行日志预处理...")
                processed_contents = []
                
                for i, original_content in enumerate(contents):
                    processed_content = self.preprocess_log(
                        original_content, 
                        enable_content_replacement=enable_content_replacement
                    )
                    if processed_content:  # 只保留非空的处理结果
                        processed_contents.append(processed_content)
                        original_to_processed_map[original_content] = processed_content
                        processed_to_original_map[processed_content] = original_content
                
                print(f"预处理后日志数量: {len(processed_contents)}")
                
                if len(processed_contents) == 0:
                    print(f"跳过 {logstore}: 预处理后无有效日志")
                    continue
                    
                # 使用预处理后的内容进行模板提取
                contents_for_drain = processed_contents
            else:
                # 不预处理时，直接使用原始内容
                contents_for_drain = contents
                for content in contents:
                    original_to_processed_map[content] = content
                    processed_to_original_map[content] = content
            
            # 使用Drain算法提取模板
            print(f"\n使用Drain算法提取模板...")
            templates = self.extract_templates_with_drain(
                contents_for_drain, verbose=verbose, **drain_params
            )
            
            # 创建日志内容到模板ID的映射
            processed_to_pattern_id = {}  # 预处理后内容到pattern_id的映射
            pattern_id_to_template = {}
            
            for cluster_id, template_info in templates.items():
                template = template_info['template']
                pattern_id = f"drain_pattern_{cluster_id}"
                pattern_id_to_template[pattern_id] = template
                
                # 为该模板的所有日志建立映射
                for processed_log_content in template_info['logs']:
                    processed_to_pattern_id[processed_log_content] = pattern_id
            
            # 创建原始内容到pattern_id的映射
            original_content_to_pattern_id = {}
            for original_content in contents:
                processed_content = original_to_processed_map.get(original_content)
                if processed_content and processed_content in processed_to_pattern_id:
                    original_content_to_pattern_id[original_content] = processed_to_pattern_id[processed_content]
            
            # 在DataFrame中添加pattern_id列
            df_valid['pattern_id'] = df_valid['content'].map(original_content_to_pattern_id)
            
            # 对于未匹配的日志，设置为None或特殊标识
            unmatched_count = df_valid['pattern_id'].isna().sum()
            df_valid['pattern_id'] = df_valid['pattern_id'].fillna('unmatched')
            
            # 将结果合并回原始DataFrame
            df['pattern_id'] = 'unmatched'  # 默认值
            df.loc[valid_mask, 'pattern_id'] = df_valid['pattern_id']
            
            # 保存带有pattern_id的数据文件
            output_file_parquet = output_dir / f"{logstore}_with_pattern_ids.parquet"
            output_file_csv = output_dir / f"{logstore}_with_pattern_ids.csv"
            
            df.to_parquet(output_file_parquet, index=False, engine='pyarrow')
            df.to_csv(output_file_csv, index=False, encoding='utf-8')
            
            # 保存模板映射信息
            pattern_mapping = {
                'logstore': logstore,
                'drain_params': drain_params,
                'template_count': len(templates),
                'pattern_mappings': pattern_id_to_template,
                'statistics': {
                    'total_logs': int(original_log_count),
                    'valid_logs': int(len(df_valid)),
                    'matched_logs': int(len(df_valid) - unmatched_count),
                    'unmatched_logs': int(unmatched_count),
                    'coverage_rate': float((len(df_valid) - unmatched_count) / len(df_valid) if len(df_valid) > 0 else 0)
                }
            }
            
            # 保存模板映射到JSON文件
            mapping_file = output_dir / f"{logstore}_pattern_mapping.json"
            with open(mapping_file, 'w', encoding='utf-8') as f:
                json.dump(pattern_mapping, f, ensure_ascii=False, indent=2)
            
            # 统计信息
            stats = pattern_mapping['statistics']
            all_stats[logstore] = stats
            
            print(f"\n模板提取和ID化完成:")
            print(f"- 总日志数: {stats['total_logs']}")
            print(f"- 有效日志数: {stats['valid_logs']}")
            print(f"- 提取模板数: {pattern_mapping['template_count']}")
            print(f"- 匹配日志数: {stats['matched_logs']}")
            print(f"- 未匹配日志数: {stats['unmatched_logs']}")
            print(f"- 覆盖率: {stats['coverage_rate']*100:.1f}%")
            
            print(f"\n保存文件:")
            print(f"- 数据文件: {output_file_parquet}")
            print(f"- CSV文件: {output_file_csv}")
            print(f"- 模板映射: {mapping_file}")
            
            # 显示模板示例
            print("\n提取的Drain模板:")
            for i, (pattern_id, template) in enumerate(list(pattern_id_to_template.items())[:5]):
                log_count = sum(1 for pid in df_valid['pattern_id'] if pid == pattern_id)
                print(f"  {pattern_id}: {template} ({log_count} logs)")
                
        return all_stats
    
    def calculate_template_coverage(self, templates_dict, total_logs):
        """计算模板覆盖率
        
        Args:
            templates_dict: 模板字典 {id: {'logs': [...], ...}}
            total_logs: 总日志数量
            
        Returns:
            dict: 覆盖率统计
        """
        matched_logs = sum(len(t.get('logs', [])) for t in templates_dict.values())
        coverage_rate = matched_logs / total_logs if total_logs > 0 else 0
        
        return {
            'total_logs': total_logs,
            'matched_logs': matched_logs,
            'coverage_rate': coverage_rate,
            'template_count': len(templates_dict),
            'avg_logs_per_template': matched_logs / len(templates_dict) if templates_dict else 0
        }
    
    def compare_extraction_methods(self, logs, max_iterations=5, drain_params=None, verbose=False):
        """对比LLM和Drain两种模板提取方法
        
        Args:
            logs: 日志列表
            max_iterations: LLM方法的最大迭代次数
            drain_params: Drain算法参数
            verbose: 是否显示详细信息
            
        Returns:
            dict: 对比结果
        """
        if not logs:
            return {}
            
        if drain_params is None:
            drain_params = {'depth': 4, 'sim_th': 0.4, 'max_children': 100}
            
        print(f"开始对比两种模板提取方法，日志数量: {len(logs)}")
        
        # 方法1: LLM迭代提取
        print(f"\n{'='*30} LLM方法 {'='*30}")
        start_time = pd.Timestamp.now()
        llm_templates = self.iterative_template_extraction(logs, max_iterations=max_iterations, verbose=verbose)
        llm_time = (pd.Timestamp.now() - start_time).total_seconds()
        
        # 方法2: Drain算法提取
        print(f"\n{'='*30} Drain方法 {'='*30}")
        start_time = pd.Timestamp.now()
        drain_templates = self.extract_templates_with_drain(logs, verbose=verbose, **drain_params)
        drain_time = (pd.Timestamp.now() - start_time).total_seconds()
        
        # 计算覆盖率统计
        llm_stats = self.calculate_template_coverage(llm_templates, len(logs))
        drain_stats = self.calculate_template_coverage(drain_templates, len(logs))
        
        # 对比结果
        comparison_result = {
            'dataset_info': {
                'total_logs': len(logs),
                'unique_logs': len(set(logs))
            },
            'llm_method': {
                'templates': llm_templates,
                'stats': llm_stats,
                'execution_time': llm_time,
                'method': 'Iterative LLM'
            },
            'drain_method': {
                'templates': drain_templates, 
                'stats': drain_stats,
                'execution_time': drain_time,
                'method': 'Drain Algorithm',
                'parameters': drain_params
            }
        }
        
        return comparison_result
    
    def print_comparison_report(self, comparison_result):
        """打印对比报告"""
        if not comparison_result:
            print("无对比结果可显示")
            return
            
        dataset_info = comparison_result['dataset_info']
        llm_result = comparison_result['llm_method']
        drain_result = comparison_result['drain_method']
        
        print(f"\n{'='*60}")
        print("模板提取方法对比报告")
        print(f"{'='*60}")
        
        # 数据集信息
        print(f"\n📊 数据集信息:")
        print(f"  总日志数量: {dataset_info['total_logs']:,}")
        print(f"  唯一日志数量: {dataset_info['unique_logs']:,}")
        print(f"  重复率: {(1 - dataset_info['unique_logs']/dataset_info['total_logs'])*100:.1f}%")
        
        # LLM方法结果
        llm_stats = llm_result['stats']
        print(f"\n🤖 LLM迭代方法:")
        print(f"  模板数量: {llm_stats['template_count']}")
        print(f"  覆盖日志数: {llm_stats['matched_logs']:,}")
        print(f"  覆盖率: {llm_stats['coverage_rate']*100:.1f}%")
        print(f"  平均每模板日志数: {llm_stats['avg_logs_per_template']:.1f}")
        print(f"  执行时间: {llm_result['execution_time']:.1f}s")
        
        # Drain方法结果
        drain_stats = drain_result['stats']
        print(f"\n🌊 Drain算法方法:")
        print(f"  模板数量: {drain_stats['template_count']}")
        print(f"  覆盖日志数: {drain_stats['matched_logs']:,}")
        print(f"  覆盖率: {drain_stats['coverage_rate']*100:.1f}%")
        print(f"  平均每模板日志数: {drain_stats['avg_logs_per_template']:.1f}")
        print(f"  执行时间: {drain_result['execution_time']:.1f}s")
        print(f"  参数: {drain_result['parameters']}")
        
        # 对比分析
        print(f"\n📈 对比分析:")
        coverage_diff = (drain_stats['coverage_rate'] - llm_stats['coverage_rate']) * 100
        template_ratio = drain_stats['template_count'] / llm_stats['template_count'] if llm_stats['template_count'] > 0 else float('inf')
        time_ratio = drain_result['execution_time'] / llm_result['execution_time'] if llm_result['execution_time'] > 0 else float('inf')
        
        print(f"  覆盖率差异: {'+' if coverage_diff >= 0 else ''}{coverage_diff:.1f}% (Drain vs LLM)")
        print(f"  模板数量比: {template_ratio:.1f}x (Drain vs LLM)")
        print(f"  执行时间比: {time_ratio:.1f}x (Drain vs LLM)")
        
        # 优势分析
        print(f"\n🏆 方法优势:")
        if drain_stats['coverage_rate'] > llm_stats['coverage_rate']:
            print(f"  覆盖率: Drain更优 ({drain_stats['coverage_rate']*100:.1f}% vs {llm_stats['coverage_rate']*100:.1f}%)")
        else:
            print(f"  覆盖率: LLM更优 ({llm_stats['coverage_rate']*100:.1f}% vs {drain_stats['coverage_rate']*100:.1f}%)")
            
        if drain_result['execution_time'] < llm_result['execution_time']:
            print(f"  执行效率: Drain更快 ({drain_result['execution_time']:.1f}s vs {llm_result['execution_time']:.1f}s)")
        else:
            print(f"  执行效率: LLM更快 ({llm_result['execution_time']:.1f}s vs {drain_result['execution_time']:.1f}s)")
            
        if llm_stats['template_count'] < drain_stats['template_count']:
            print(f"  模板简洁性: LLM更简洁 ({llm_stats['template_count']} vs {drain_stats['template_count']} 模板)")
        else:
            print(f"  模板简洁性: Drain更简洁 ({drain_stats['template_count']} vs {llm_stats['template_count']} 模板)")
        
        # 模板示例对比
        print(f"\n📋 模板示例对比:")
        print(f"  LLM模板示例:")
        for i, (tid, tinfo) in enumerate(list(llm_result['templates'].items())[:3]):
            print(f"    {i+1}. {tinfo.get('template', 'N/A')} ({len(tinfo.get('logs', []))} logs)")
            
        print(f"  Drain模板示例:")  
        for i, (tid, tinfo) in enumerate(list(drain_result['templates'].items())[:3]):
            print(f"    {i+1}. {tinfo.get('template', 'N/A')} ({len(tinfo.get('logs', []))} logs)")
        
        print(f"{'='*60}")
    
    def save_comparison_results(self, comparison_result, output_dir, logstore_name):
        """保存对比结果"""
        if not comparison_result:
            return
            
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存详细对比结果
        with open(output_dir / f"{logstore_name}_comparison.json", 'w', encoding='utf-8') as f:
            # 简化保存的数据（只保留统计信息和少量示例）
            simplified_result = {
                'dataset_info': comparison_result['dataset_info'],
                'llm_method': {
                    'stats': comparison_result['llm_method']['stats'],
                    'execution_time': comparison_result['llm_method']['execution_time'],
                    'method': comparison_result['llm_method']['method'],
                    'sample_templates': {k: {'template': v.get('template'), 'log_count': len(v.get('logs', []))} 
                                       for k, v in list(comparison_result['llm_method']['templates'].items())[:10]}
                },
                'drain_method': {
                    'stats': comparison_result['drain_method']['stats'],
                    'execution_time': comparison_result['drain_method']['execution_time'],
                    'method': comparison_result['drain_method']['method'],
                    'parameters': comparison_result['drain_method']['parameters'],
                    'sample_templates': {k: {'template': v.get('template'), 'log_count': len(v.get('logs', []))} 
                                       for k, v in list(comparison_result['drain_method']['templates'].items())[:10]}
                }
            }
            json.dump(simplified_result, f, ensure_ascii=False, indent=2)
        
        # 保存CSV对比表
        comparison_data = [{
            'method': 'LLM',
            'template_count': comparison_result['llm_method']['stats']['template_count'],
            'coverage_rate': comparison_result['llm_method']['stats']['coverage_rate'],
            'matched_logs': comparison_result['llm_method']['stats']['matched_logs'],
            'execution_time': comparison_result['llm_method']['execution_time'],
            'avg_logs_per_template': comparison_result['llm_method']['stats']['avg_logs_per_template']
        }, {
            'method': 'Drain',
            'template_count': comparison_result['drain_method']['stats']['template_count'],
            'coverage_rate': comparison_result['drain_method']['stats']['coverage_rate'],
            'matched_logs': comparison_result['drain_method']['stats']['matched_logs'],
            'execution_time': comparison_result['drain_method']['execution_time'],
            'avg_logs_per_template': comparison_result['drain_method']['stats']['avg_logs_per_template']
        }]
        
        comparison_df = pd.DataFrame(comparison_data)
        comparison_df.to_csv(output_dir / f"{logstore_name}_comparison.csv", index=False, encoding='utf-8')
        
        print(f"对比结果已保存到: {output_dir}")
        
        return comparison_result

    def match_logs_with_template(self, logs, template, min_matches=2, verbose=False):
        """使用模板匹配日志的主入口函数"""
        # 优先使用智能匹配
        return self.smart_match_logs_with_template(
            logs, template, min_matches=min_matches, 
            similarity_threshold=0.4, verbose=verbose
        )

    def iterative_template_extraction(self, logs, max_iterations=10, verbose=False):
        """迭代进行模板提取和匹配"""
        all_templates = {}  # template_id -> {'template': str, 'regex': str, 'logs': list}
        remaining_logs = logs[:]
        template_id = 0

        if verbose:
            print(f"开始处理 {len(logs)} 条日志")

        for iteration in range(max_iterations):
            if len(remaining_logs) < 2:  # 剩余日志太少，停止迭代
                break

            if verbose:
                print(f"\n=== 迭代 {iteration + 1} ===")
                print(f"剩余日志数量: {len(remaining_logs)}")

            # 1. 粗筛分组
            groups = self.rough_group_logs(remaining_logs, n_groups=min(5, len(remaining_logs)))
            if verbose:
                print(f"分成 {len(groups)} 个组")

            found_valid_template = False

            # 2. 对每个组进行模板提取
            for group_id, group_logs in groups.items():
                if len(group_logs) < 2:
                    continue

                if verbose:
                    print(f"\n处理组 {group_id} ({len(group_logs)} 条日志)")

                # 3. LLM提取模板
                sample_size = min(10, len(group_logs))
                sample_logs = np.random.choice(group_logs, sample_size, replace=False).tolist()

                template = self.llm_query_template(sample_logs)
                if not template:
                    if verbose:
                        print("未提取到有效模板")
                    continue

                if verbose:
                    print(f"提取的模板: {template}")

                # 如果LLM返回多行，选择第一行作为主模板
                if '\n' in template:
                    template_lines = [line.strip() for line in template.strip().split('\n') if line.strip()]
                    template = template_lines[0]  # 使用第一行作为主模板
                    if verbose:
                        print(f"使用第一行作为主模板: {template}")

                # 4. 验证模板并匹配日志
                matched_logs, is_valid = self.match_logs_with_template(group_logs, template, min_matches=2, verbose=verbose)

                if is_valid and matched_logs:
                    if verbose:
                        print(f"模板有效，匹配到 {len(matched_logs)} 条日志")

                    # 5. 使用模板匹配所有剩余日志
                    all_matched_logs, _ = self.match_logs_with_template(remaining_logs, template, min_matches=1, verbose=False)

                    if all_matched_logs:
                        # 保存模板和匹配的日志
                        all_templates[template_id] = {
                            "template": template,
                            "regex": self.template_to_regex(template),
                            "logs": all_matched_logs,
                            "iteration": iteration + 1,
                        }

                        # 从剩余日志中移除已匹配的
                        remaining_logs = [log for log in remaining_logs if log not in all_matched_logs]

                        if verbose:
                            print(f"总匹配日志: {len(all_matched_logs)}, 剩余日志: {len(remaining_logs)}")
                        template_id += 1
                        found_valid_template = True
                        break  # 找到有效模板后处理下一次迭代
                else:
                    if verbose:
                        print("模板无效或匹配日志数量不足")

            if not found_valid_template:
                if verbose:
                    print("本轮未找到有效模板，停止迭代")
                break

        # 注意：不再在此处直接添加未匹配日志作为单独的模板
        # 未匹配的日志会在完整迭代过程结束后，如果仍有剩余，才标记为未匹配
        
        # 最终处理：只有在所有迭代完成后仍有剩余日志时，才标记为未匹配
        if remaining_logs and len(remaining_logs) > 0:
            # 检查是否所有迭代都已用完或者剩余日志数量太少无法继续
            if iteration == max_iterations - 1 or len(remaining_logs) < 2:
                all_templates[template_id] = {"template": "<未匹配>", "regex": None, "logs": remaining_logs, "iteration": -1}

        return all_templates

    def save_template_results(self, logstore, templates, output_dir):
        """保存模板提取结果"""
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        # 保存详细结果
        detailed_results = []
        summary_stats = {
            "logstore": logstore,
            "total_logs": sum(len(t["logs"]) for t in templates.values()),
            "total_templates": len(templates),
            "matched_logs": sum(len(t["logs"]) for t in templates.values() if t["template"] != "<未匹配>"),
            "unmatched_logs": sum(len(t["logs"]) for t in templates.values() if t["template"] == "<未匹配>"),
        }

        for template_id, template_info in templates.items():
            detailed_results.append(
                {
                    "template_id": template_id,
                    "template": template_info["template"],
                    "regex": template_info["regex"],
                    "log_count": len(template_info["logs"]),
                    "iteration": template_info["iteration"],
                    "logs": template_info["logs"][:10],  # 只保存前10条作为示例
                }
            )

        # 保存到JSON
        with open(output_dir / f"{logstore}_templates.json", "w", encoding="utf-8") as f:
            json.dump({"summary": summary_stats, "templates": detailed_results}, f, ensure_ascii=False, indent=2)

        # 保存到CSV用于查看
        template_df = pd.DataFrame([
            {
                "template_id": tid,
                "template": info["template"],
                "log_count": len(info["logs"]),
                "iteration": info["iteration"]
            } for tid, info in templates.items()
        ])

        template_df.to_csv(output_dir / f"{logstore}_summary.csv", index=False, encoding="utf-8")

        return summary_stats

    def extract_templates(self, data_path, output_dir=None, logstores=None, max_iterations=10, verbose=False, enable_preprocessing=True, enable_content_replacement=True, compare_methods=False):
        """主要的模板提取流程
        
        Args:
            data_path: 数据路径
            output_dir: 输出目录
            logstores: 要处理的logstore列表，None表示处理所有
            max_iterations: 最大迭代次数
            verbose: 是否显示详细信息
            enable_preprocessing: 是否启用日志预处理
            enable_content_replacement: 是否启用内容替换
            compare_methods: 是否进行LLM和Drain方法对比
        """
        # 设置输出目录
        if output_dir is None:
            output_dir = Path(BASE_PATH) / "data/control_log/template_results"
        else:
            output_dir = Path(output_dir)

        # 加载数据
        print("加载数据...")
        data = self.load_processed_data(data_path)
        print(f"加载完成，共 {len(data)} 个logstore")

        # 过滤logstore
        if logstores:
            data = {k: v for k, v in data.items() if k in logstores}
            print(f"过滤后剩余 {len(data)} 个logstore: {list(data.keys())}")

        # 处理每个logstore
        all_stats = {}
        for logstore, logs in data.items():
            contents = [log["content"] for log in logs if "content" in log and isinstance(log["content"], str) and log["content"].strip()]

            if len(contents) == 0:
                print(f"跳过 {logstore}: 无有效日志内容")
                continue

            print(f"\n{'='*50}")
            print(f"处理 Logstore: {logstore}")
            print(f"原始日志数量: {len(contents)}")

            # 日志预处理
            if enable_preprocessing:
                print("\n执行日志预处理...")
                contents = self.batch_preprocess_logs(
                    contents, 
                    show_examples=verbose, 
                    enable_content_replacement=enable_content_replacement
                )
                print(f"预处理后日志数量: {len(contents)}")
                
                if len(contents) == 0:
                    print(f"跳过 {logstore}: 预处理后无有效日志")
                    continue

            # 选择执行模式
            if compare_methods:
                # 对比模式：同时运行LLM和Drain方法
                comparison_result = self.compare_extraction_methods(
                    contents, max_iterations=max_iterations, verbose=verbose
                )
                
                # 打印对比报告
                self.print_comparison_report(comparison_result)
                
                # 保存对比结果
                self.save_comparison_results(comparison_result, output_dir, logstore)
                
                # 统计信息基于LLM结果
                stats = comparison_result['llm_method']['stats']
                all_stats[logstore] = {
                    'llm_stats': stats,
                    'drain_stats': comparison_result['drain_method']['stats'],
                    'comparison': True
                }
                
            else:
                # 单独LLM模式
                templates = self.iterative_template_extraction(contents, max_iterations=max_iterations, verbose=verbose)

                # 保存结果
                stats = self.save_template_results(logstore, templates, output_dir)
                all_stats[logstore] = stats

                # 打印统计信息
                print(f"\n模板提取完成:")
                print(f"- 总日志数: {stats['total_logs']}")
                print(f"- 提取模板数: {stats['total_templates']}")
                print(f"- 匹配日志数: {stats['matched_logs']}")
                print(f"- 未匹配日志数: {stats['unmatched_logs']}")
                print(f"- 匹配率: {stats['matched_logs']/stats['total_logs']*100:.1f}%")

                # 显示模板示例
                print("\n提取的模板:")
                for i, (template_id, template_info) in enumerate(templates.items()):
                    if i >= 5:  # 最多显示5个模板
                        break
                    print(f"  {template_id}: {template_info['template']} ({len(template_info['logs'])} logs)")

        return all_stats

    def test_template_matching(self):
        """测试模板匹配功能"""
        print("=== 测试模板匹配功能 ===")

        # 测试用例0: Drain算法测试
        print("\n测试用例0: Drain算法测试")
        test_logs_drain = [
            "QEMU |  No machine 'qemu-i-abc123' known",
            "QEMU |  No machine 'qemu-eci-def456' known", 
            "QEMU |  No machine 'qemu-i-xyz789' known",
            "Connection failed to ***********00 on port 22",
            "Connection failed to ******** on port 443",
            "Process java exited with code 0",
            "Process python exited with code 1",
        ]
        
        if DRAIN_AVAILABLE:
            drain_templates = self.extract_templates_with_drain(test_logs_drain, verbose=True)
            print(f"Drain提取结果: {len(drain_templates)} 个模板")
        else:
            print("Drain库未安装，跳过Drain测试")

        # 测试用例1: 增强的日志预处理
        print("\n测试用例1: 增强的日志预处理")
        raw_logs = [
            "2025-07-22 18:10:59.329+0000[pid=5580,iso=0][2025/07/22 18:10:57][1753179057331][info] instanceId = i-2ze9c4ik6b5vx3zqy3jq\r",
            "[2025-07-22 18:11:00][DEBUG] QEMU |  No machine 'qemu-eci-abc123' known\n",
            "2025/07/22 18:11:01 [ERROR] Connection failed to ***********00 on port 22: Connection refused",
            "[pid=1234][info] Process [java -jar app.jar] exited with code 0",
            "c:\\ProgramData\\aliyun\\vminit/INSTANCE_i-2ze9c4ik6b5vx3zqy3jq/FAILED_PLUGIN",
            "User abc123 login from 198.19.125.35 with session_id=xyz789def456",
            "Request 12345678901234567890 processed in 150ms with UUID a1b2c3d4-e5f6-7890-abcd-ef1234567890"
        ]
        
        print("🔧 不启用内容替换的预处理:")
        for i, raw_log in enumerate(raw_logs[:3], 1):
            cleaned = self.preprocess_log(raw_log, enable_content_replacement=False)
            print(f"  {i}. 原始: {raw_log[:60]}...")
            print(f"     清理: {cleaned}")
            print()
            
        print("🎯 启用内容替换的预处理:")
        for i, raw_log in enumerate(raw_logs, 1):
            cleaned = self.preprocess_log(raw_log, enable_content_replacement=True)
            print(f"  {i}. 原始: {raw_log[:60]}...")
            print(f"     替换: {cleaned}")
            print()

        # 测试用例2: QEMU模板
        print(f"\n{'='*30}")
        print("测试用例2: QEMU模板")
        template1 = "QEMU |  No machine 'qemu-<|other|>' known"
        test_logs1 = [
            "QEMU |  No machine 'qemu-i-j6c3hy027sbq648valvs.164404790.1305257' known",
            "QEMU |  No machine 'qemu-eci-6we0hfjmlrp68cur5u49.1859907.22553' known",
            "QEMU |  No machine 'qemu-eci-t4n0jya7swzt4pipcde8.183958137.4937724' known",
            "QEMU |  different log message",
            "完全不同的日志"
        ]

        print(f"模板: {template1}")
        print(f"测试日志数量: {len(test_logs1)}")

        # 测试灵活正则
        regex_flexible = self.template_to_regex(template1, flexible=True)
        print(f"灵活正则: {regex_flexible}")
        
        # 测试严格正则
        regex_strict = self.template_to_regex(template1, flexible=False)
        print(f"严格正则: {regex_strict}")

        # 测试智能匹配
        matched_logs, is_valid = self.match_logs_with_template(test_logs1, template1, min_matches=2, verbose=True)
        print(f"\n最终匹配结果: {len(matched_logs)} 条日志")
        print(f"模板有效性: {is_valid}")

        # 测试用例3: 复杂模板
        print(f"\n{'='*50}")
        print("测试用例3: 复杂模板")
        template2 = "Error connecting to <|ip_address|> on port <|other|>: Connection refused"
        test_logs2 = [
            "Error connecting to ***********00 on port 22: Connection refused",
            "Error connecting to ******** on port 443: Connection refused",
            "Warning: Failed to connect to server",
            "Error connecting to localhost on port 8080: Connection timeout",
            "Successfully connected to ***********"
        ]

        print(f"模板: {template2}")
        matched_logs2, is_valid2 = self.match_logs_with_template(test_logs2, template2, min_matches=2, verbose=True)
        print(f"\n最终匹配结果: {len(matched_logs2)} 条日志")
        print(f"模板有效性: {is_valid2}")

        # 测试用例4: 边缘情况
        print(f"\n{'='*50}")
        print("测试用例4: 边缘情况 - 空格和特殊字符")
        template3 = "Process [<|other|>] exited with code <|other|>"
        test_logs3 = [
            "Process [java -jar app.jar] exited with code 0",
            "Process [python script.py] exited with code 1",
            "Process [nginx] exited with code 143",
            "Process started successfully",
            "System shutdown initiated"
        ]

        print(f"模板: {template3}")
        matched_logs3, is_valid3 = self.match_logs_with_template(test_logs3, template3, min_matches=2, verbose=True)
        print(f"\n最终匹配结果: {len(matched_logs3)} 条日志")
        print(f"模板有效性: {is_valid3}")

        print("=" * 50)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="日志模板提取工具")
    parser.add_argument("data_path", nargs='?', help="处理后的数据路径（文件或目录）", default="data/control_log/processed_data")
    parser.add_argument("-o", "--output", help="输出目录", default=None)
    parser.add_argument("-l", "--logstores", nargs="+", help="指定处理的logstore", default=None)
    parser.add_argument("-m", "--max-iterations", type=int, help="最大迭代次数", default=10)
    parser.add_argument("-v", "--verbose", action="store_true", help="显示详细信息")
    parser.add_argument("--test", action="store_true", help="运行测试")
    parser.add_argument("--api-key", help="DashScope API密钥")
    parser.add_argument("--no-preprocess", action="store_true", help="禁用日志预处理")
    parser.add_argument("--no-content-replacement", action="store_true", help="禁用内容替换（仅清理格式）")
    parser.add_argument("--compare", action="store_true", help="对比LLM和Drain两种方法")

    args = parser.parse_args()

    # 初始化提取器
    extractor = LogTemplateExtractor(api_key=args.api_key)

    # 运行测试
    if args.test:
        extractor.test_template_matching()
        return

    # 执行模板提取
    try:
        stats = extractor.extract_templates(
            data_path=args.data_path,
            output_dir=args.output,
            logstores=args.logstores,
            max_iterations=args.max_iterations,
            verbose=args.verbose,
            enable_preprocessing=not args.no_preprocess,
            enable_content_replacement=not args.no_content_replacement,
            compare_methods=args.compare
        )
        
        print(f"\n{'='*50}")
        print("全部处理完成！")
        print("\n总体统计:")
        for logstore, stat in stats.items():
            print(f"{logstore}: {stat['matched_logs']}/{stat['total_logs']} ({stat['matched_logs']/stat['total_logs']*100:.1f}%)")
            
    except Exception as e:
        print(f"处理出错: {e}")
        return 1

    return 0


if __name__ == "__main__":
    sys.exit(main())