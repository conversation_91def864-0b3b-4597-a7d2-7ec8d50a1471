#!/usr/bin/env python3
"""
基于embedding的日志聚类模块
使用DashScope API对日志进行向量化，然后应用多种聚类算法
"""

import pandas as pd
import numpy as np
import dashscope
from http import HTTPStatus
import time
import json
from typing import List, Dict, Optional, Union, Tuple
from pathlib import Path
import logging
from dataclasses import dataclass
import pickle
import re

# 导入日志预处理模块
try:
    import sys
    sys.path.append('..')  # 添加父目录到路径
    from control_log_diagnose.src.template_extractor import LogTemplateExtractor
    TEMPLATE_EXTRACTOR_AVAILABLE = True
except ImportError:
    TEMPLATE_EXTRACTOR_AVAILABLE = False
    print("Warning: template_extractor not available. Preprocessing will be skipped.")

# 聚类算法
from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering, OPTICS
from sklearn.mixture import GaussianMixture
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import silhouette_score, calinski_harabasz_score, davies_bouldin_score
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE
import umap.umap_ as umap

# 可视化
import matplotlib.pyplot as plt
import seaborn as sns
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


@dataclass
class ClusteringConfig:
    """聚类配置参数"""
    # DashScope配置
    api_key: Optional[str] = None
    model: str = "text-embedding-v4"
    dimension: int = 1024
    output_type: str = "dense"
    
    # 数据处理配置
    log_column: str = "log_content"
    batch_size: int = 100
    max_retries: int = 3
    retry_delay: float = 1.0
    
    # 预处理配置
    enable_preprocessing: bool = True
    preserve_structure: bool = True  # 保留基本结构（如冒号、等号等）
    enable_content_replacement: bool = True  # 启用内容替换（将显著内容替换为占位符）
    preprocess_before_embedding: bool = True  # 在embedding前进行预处理
    
    # 聚类配置
    clustering_methods: List[str] = None
    auto_select_clusters: bool = True
    max_clusters: int = 50
    
    # 输出配置
    save_embeddings: bool = True
    save_results: bool = True
    output_dir: str = "clustering_results"
    
    def __post_init__(self):
        if self.clustering_methods is None:
            self.clustering_methods = ["kmeans", "dbscan", "hierarchical"]


class LogEmbeddingClustering:
    """基于embedding的日志聚类器"""
    
    def __init__(self, config: ClusteringConfig):
        self.config = config
        self.logger = self._setup_logger()
        
        # 设置DashScope API Key
        if config.api_key:
            dashscope.api_key = config.api_key
        
        # 初始化存储
        self.embeddings = None
        self.log_data = None
        self.processed_logs = None  # 存储预处理后的日志
        self.clustering_results = {}
        self.scaler = StandardScaler()
        
        # 初始化预处理器
        self.preprocessor = None
        if config.enable_preprocessing and TEMPLATE_EXTRACTOR_AVAILABLE:
            try:
                self.preprocessor = LogTemplateExtractor()
                self.logger.info("Log preprocessor initialized successfully")
            except Exception as e:
                self.logger.warning(f"Failed to initialize preprocessor: {e}")
                self.preprocessor = None
        elif config.enable_preprocessing and not TEMPLATE_EXTRACTOR_AVAILABLE:
            self.logger.warning("Preprocessing enabled but template_extractor not available")
        
        # 创建输出目录
        Path(config.output_dir).mkdir(parents=True, exist_ok=True)
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger(self.__class__.__name__)
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def load_data(self, file_path: str) -> pd.DataFrame:
        """加载日志数据"""
        self.logger.info(f"Loading data from {file_path}")
        
        file_path = Path(file_path)
        if file_path.suffix.lower() == '.parquet':
            df = pd.read_parquet(file_path)
        elif file_path.suffix.lower() == '.csv':
            df = pd.read_csv(file_path)
        else:
            raise ValueError(f"Unsupported file format: {file_path.suffix}")
        
        if self.config.log_column not in df.columns:
            raise ValueError(f"Log column '{self.config.log_column}' not found in data")
        
        # 清理数据
        df = df.dropna(subset=[self.config.log_column])
        df = df[df[self.config.log_column].str.strip() != ""]
        
        self.log_data = df
        self.logger.info(f"Loaded {len(df)} log entries")
        
        return df
    
    def preprocess_logs(self, logs: Optional[List[str]] = None) -> List[str]:
        """预处理日志文本"""
        if logs is None:
            if self.log_data is None:
                raise ValueError("No log data available. Run load_data() first.")
            logs = self.log_data[self.config.log_column].tolist()
        
        if not self.config.enable_preprocessing or self.preprocessor is None:
            self.logger.info("Preprocessing disabled or preprocessor not available")
            self.processed_logs = logs
            return logs
        
        self.logger.info(f"Preprocessing {len(logs)} log entries...")
        
        processed = []
        for i, log in enumerate(logs):
            try:
                processed_log = self.preprocessor.preprocess_log(
                    log, 
                    preserve_structure=self.config.preserve_structure,
                    enable_content_replacement=self.config.enable_content_replacement
                )
                processed.append(processed_log)
                
                # 进度提示
                if (i + 1) % 1000 == 0:
                    self.logger.info(f"Preprocessed {i + 1}/{len(logs)} logs")
                    
            except Exception as e:
                self.logger.warning(f"Failed to preprocess log {i}: {e}")
                # 如果预处理失败，使用原始日志
                processed.append(log)
        
        self.processed_logs = processed
        self.logger.info(f"Preprocessing completed. {len(processed)} logs processed.")
        
        # 保存预处理结果（可选）
        if self.config.save_results:
            processed_path = Path(self.config.output_dir) / "preprocessed_logs.txt"
            with open(processed_path, 'w', encoding='utf-8') as f:
                for log in processed:
                    f.write(log + '\n')
            self.logger.info(f"Saved preprocessed logs to {processed_path}")
        
        return processed
    
    def _get_embedding_batch(self, texts: List[str]) -> Optional[np.ndarray]:
        """批量获取文本embedding"""
        for attempt in range(self.config.max_retries):
            try:
                resp = dashscope.TextEmbedding.call(
                    model=self.config.model,
                    input=texts,
                    dimension=self.config.dimension,
                    output_type=self.config.output_type
                )
                
                if resp.status_code == HTTPStatus.OK:
                    embeddings = []
                    for item in resp.output['embeddings']:
                        if self.config.output_type == "dense":
                            embeddings.append(item['embedding'])
                        else:  # dense&sparse
                            embeddings.append(item['embedding']['dense'])
                    
                    return np.array(embeddings)
                else:
                    self.logger.warning(f"API call failed: {resp}")
                    
            except Exception as e:
                self.logger.warning(f"Attempt {attempt + 1} failed: {e}")
                if attempt < self.config.max_retries - 1:
                    time.sleep(self.config.retry_delay)
        
        return None
    
    def generate_embeddings(self, texts: Optional[List[str]] = None) -> np.ndarray:
        """生成文本embedding"""
        if texts is None:
            # 根据配置决定是否使用预处理后的日志
            if self.config.preprocess_before_embedding and self.processed_logs is not None:
                texts = self.processed_logs
                self.logger.info("Using preprocessed logs for embedding generation")
            elif self.config.preprocess_before_embedding:
                # 如果配置要求预处理但还没有预处理，先进行预处理
                texts = self.preprocess_logs()
                self.logger.info("Preprocessed logs before embedding generation")
            else:
                texts = self.log_data[self.config.log_column].tolist()
                self.logger.info("Using original logs for embedding generation")
        
        self.logger.info(f"Generating embeddings for {len(texts)} texts")
        
        all_embeddings = []
        
        # 分批处理
        for i in range(0, len(texts), self.config.batch_size):
            batch = texts[i:i + self.config.batch_size]
            self.logger.info(f"Processing batch {i//self.config.batch_size + 1}/{(len(texts)-1)//self.config.batch_size + 1}")
            
            embeddings = self._get_embedding_batch(batch)
            if embeddings is not None:
                all_embeddings.append(embeddings)
            else:
                self.logger.error(f"Failed to get embeddings for batch starting at {i}")
                # 填充零向量作为备选
                all_embeddings.append(np.zeros((len(batch), self.config.dimension)))
            
            # 避免API限流
            time.sleep(0.1)
        
        self.embeddings = np.vstack(all_embeddings)
        
        # 保存embeddings
        if self.config.save_embeddings:
            embedding_path = Path(self.config.output_dir) / "embeddings.npy"
            np.save(embedding_path, self.embeddings)
            self.logger.info(f"Saved embeddings to {embedding_path}")
        
        return self.embeddings
    
    def _determine_optimal_clusters(self, embeddings: np.ndarray, method: str) -> int:
        """自动确定最优聚类数量"""
        if method in ['dbscan', 'optics']:
            return -1  # 这些方法自动确定聚类数
        
        # 标准化embedding
        embeddings_scaled = self.scaler.fit_transform(embeddings)
        
        best_score = -1
        best_k = 2
        
        k_range = range(2, min(self.config.max_clusters, len(embeddings) // 2))
        scores = []
        
        for k in k_range:
            if method == "kmeans":
                clusterer = KMeans(n_clusters=k, random_state=42, n_init=10)
            elif method == "hierarchical":
                clusterer = AgglomerativeClustering(n_clusters=k)
            elif method == "gmm":
                clusterer = GaussianMixture(n_components=k, random_state=42)
            else:
                continue
            
            try:
                if method == "gmm":
                    labels = clusterer.fit_predict(embeddings_scaled)
                else:
                    labels = clusterer.fit_predict(embeddings_scaled)
                
                if len(set(labels)) > 1:  # 至少有2个聚类
                    score = silhouette_score(embeddings_scaled, labels)
                    scores.append(score)
                    
                    if score > best_score:
                        best_score = score
                        best_k = k
                else:
                    scores.append(-1)
                    
            except Exception as e:
                self.logger.warning(f"Failed to evaluate k={k} for {method}: {e}")
                scores.append(-1)
        
        self.logger.info(f"Optimal number of clusters for {method}: {best_k} (silhouette score: {best_score:.3f})")
        return best_k
    
    def _apply_kmeans(self, embeddings: np.ndarray, n_clusters: int = None) -> Dict:
        """应用K-Means聚类"""
        embeddings_scaled = self.scaler.fit_transform(embeddings)
        
        if n_clusters is None:
            n_clusters = self._determine_optimal_clusters(embeddings, "kmeans")
        
        clusterer = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
        labels = clusterer.fit_predict(embeddings_scaled)
        
        return {
            "method": "kmeans",
            "labels": labels,
            "n_clusters": n_clusters,
            "clusterer": clusterer,
            "centers": clusterer.cluster_centers_
        }
    
    def _apply_dbscan(self, embeddings: np.ndarray, eps: float = None, min_samples: int = None) -> Dict:
        """应用DBSCAN聚类"""
        embeddings_scaled = self.scaler.fit_transform(embeddings)
        
        # 自动调参
        if eps is None:
            from sklearn.neighbors import NearestNeighbors
            neighbors = NearestNeighbors(n_neighbors=10)
            neighbors_fit = neighbors.fit(embeddings_scaled)
            distances, indices = neighbors_fit.kneighbors(embeddings_scaled)
            distances = np.sort(distances[:, -1])
            eps = distances[int(len(distances) * 0.95)]  # 95th percentile
        
        if min_samples is None:
            min_samples = max(2, int(np.log(len(embeddings))))
        
        clusterer = DBSCAN(eps=eps, min_samples=min_samples)
        labels = clusterer.fit_predict(embeddings_scaled)
        
        n_clusters = len(set(labels)) - (1 if -1 in labels else 0)
        
        return {
            "method": "dbscan",
            "labels": labels,
            "n_clusters": n_clusters,
            "clusterer": clusterer,
            "eps": eps,
            "min_samples": min_samples,
            "n_noise": list(labels).count(-1)
        }
    
    def _apply_hierarchical(self, embeddings: np.ndarray, n_clusters: int = None) -> Dict:
        """应用层次聚类"""
        embeddings_scaled = self.scaler.fit_transform(embeddings)
        
        if n_clusters is None:
            n_clusters = self._determine_optimal_clusters(embeddings, "hierarchical")
        
        clusterer = AgglomerativeClustering(n_clusters=n_clusters, linkage='ward')
        labels = clusterer.fit_predict(embeddings_scaled)
        
        return {
            "method": "hierarchical",
            "labels": labels,
            "n_clusters": n_clusters,
            "clusterer": clusterer
        }
    
    def _apply_optics(self, embeddings: np.ndarray) -> Dict:
        """应用OPTICS聚类"""
        embeddings_scaled = self.scaler.fit_transform(embeddings)
        
        clusterer = OPTICS(min_samples=max(2, int(np.log(len(embeddings)))))
        labels = clusterer.fit_predict(embeddings_scaled)
        
        n_clusters = len(set(labels)) - (1 if -1 in labels else 0)
        
        return {
            "method": "optics",
            "labels": labels,
            "n_clusters": n_clusters,
            "clusterer": clusterer,
            "n_noise": list(labels).count(-1)
        }
    
    def _apply_gmm(self, embeddings: np.ndarray, n_components: int = None) -> Dict:
        """应用高斯混合模型聚类"""
        embeddings_scaled = self.scaler.fit_transform(embeddings)
        
        if n_components is None:
            n_components = self._determine_optimal_clusters(embeddings, "gmm")
        
        clusterer = GaussianMixture(n_components=n_components, random_state=42)
        clusterer.fit(embeddings_scaled)
        labels = clusterer.predict(embeddings_scaled)
        
        return {
            "method": "gmm",
            "labels": labels,
            "n_clusters": n_components,
            "clusterer": clusterer,
            "probabilities": clusterer.predict_proba(embeddings_scaled)
        }
    
    def perform_clustering(self, embeddings: np.ndarray = None) -> Dict[str, Dict]:
        """执行聚类分析"""
        if embeddings is None:
            embeddings = self.embeddings
        
        if embeddings is None:
            raise ValueError("No embeddings available. Run generate_embeddings() first.")
        
        self.logger.info(f"Performing clustering with methods: {self.config.clustering_methods}")
        
        results = {}
        
        for method in self.config.clustering_methods:
            self.logger.info(f"Applying {method} clustering...")
            
            try:
                if method == "kmeans":
                    result = self._apply_kmeans(embeddings)
                elif method == "dbscan":
                    result = self._apply_dbscan(embeddings)
                elif method == "hierarchical":
                    result = self._apply_hierarchical(embeddings)
                elif method == "optics":
                    result = self._apply_optics(embeddings)
                elif method == "gmm":
                    result = self._apply_gmm(embeddings)
                else:
                    self.logger.warning(f"Unknown clustering method: {method}")
                    continue
                
                # 计算评估指标
                if result["n_clusters"] > 1:
                    embeddings_scaled = self.scaler.transform(embeddings)
                    valid_labels = result["labels"][result["labels"] >= 0]  # 排除噪声点
                    valid_embeddings = embeddings_scaled[result["labels"] >= 0]
                    
                    if len(set(valid_labels)) > 1:
                        result["silhouette_score"] = silhouette_score(valid_embeddings, valid_labels)
                        result["calinski_harabasz_score"] = calinski_harabasz_score(valid_embeddings, valid_labels)
                        result["davies_bouldin_score"] = davies_bouldin_score(valid_embeddings, valid_labels)
                
                results[method] = result
                self.logger.info(f"{method} clustering completed. Found {result['n_clusters']} clusters.")
                
            except Exception as e:
                self.logger.error(f"Failed to apply {method} clustering: {e}")
        
        self.clustering_results = results
        return results
    
    def evaluate_clustering(self) -> pd.DataFrame:
        """评估聚类结果"""
        evaluation_data = []
        
        for method, result in self.clustering_results.items():
            row = {
                "method": method,
                "n_clusters": result["n_clusters"],
                "silhouette_score": result.get("silhouette_score", np.nan),
                "calinski_harabasz_score": result.get("calinski_harabasz_score", np.nan),
                "davies_bouldin_score": result.get("davies_bouldin_score", np.nan)
            }
            
            if "n_noise" in result:
                row["n_noise"] = result["n_noise"]
                row["noise_ratio"] = result["n_noise"] / len(result["labels"])
            
            evaluation_data.append(row)
        
        evaluation_df = pd.DataFrame(evaluation_data)
        
        # 保存评估结果
        if self.config.save_results:
            eval_path = Path(self.config.output_dir) / "clustering_evaluation.csv"
            evaluation_df.to_csv(eval_path, index=False)
            self.logger.info(f"Saved evaluation results to {eval_path}")
        
        return evaluation_df
    
    def save_results(self, method: str = None) -> Dict[str, str]:
        """保存聚类结果"""
        if not self.clustering_results:
            raise ValueError("No clustering results available")
        
        saved_files = {}
        
        methods_to_save = [method] if method else self.clustering_results.keys()
        
        for method_name in methods_to_save:
            if method_name not in self.clustering_results:
                continue
            
            result = self.clustering_results[method_name]
            
            # 创建结果DataFrame
            result_df = self.log_data.copy()
            result_df["embedding"] = [emb.tolist() for emb in self.embeddings]
            result_df["cluster_id"] = result["labels"]
            
            # 添加预处理相关信息
            if self.processed_logs is not None:
                result_df["preprocessed_log"] = self.processed_logs
                result_df["preprocessing_enabled"] = True
                result_df["preserve_structure"] = self.config.preserve_structure
                result_df["content_replacement_enabled"] = self.config.enable_content_replacement
            else:
                result_df["preprocessing_enabled"] = False
            
            # 添加额外信息
            if "probabilities" in result:
                prob_df = pd.DataFrame(result["probabilities"], 
                                     columns=[f"prob_cluster_{i}" for i in range(result["n_clusters"])])
                result_df = pd.concat([result_df, prob_df], axis=1)
            
            # 保存为parquet格式（包含embedding）
            parquet_path = Path(self.config.output_dir) / f"{method_name}_results.parquet"
            result_df.to_parquet(parquet_path, index=False)
            saved_files[f"{method_name}_parquet"] = str(parquet_path)
            
            # 保存为CSV格式（不含embedding）
            csv_df = result_df.drop(columns=["embedding"])
            csv_path = Path(self.config.output_dir) / f"{method_name}_results.csv"
            csv_df.to_csv(csv_path, index=False)
            saved_files[f"{method_name}_csv"] = str(csv_path)
            
            # 保存聚类模型
            model_path = Path(self.config.output_dir) / f"{method_name}_model.pkl"
            with open(model_path, 'wb') as f:
                pickle.dump({
                    "clusterer": result["clusterer"],
                    "scaler": self.scaler,
                    "config": result
                }, f)
            saved_files[f"{method_name}_model"] = str(model_path)
        
        self.logger.info(f"Saved results to {len(saved_files)} files")
        return saved_files
    
    def visualize_clusters(self, method: str = None, reduction_method: str = "tsne", 
                          save_plot: bool = True) -> None:
        """可视化聚类结果"""
        if not self.clustering_results:
            raise ValueError("No clustering results available")
        
        methods_to_plot = [method] if method else list(self.clustering_results.keys())
        
        for method_name in methods_to_plot:
            if method_name not in self.clustering_results:
                continue
            
            result = self.clustering_results[method_name]
            labels = result["labels"]
            
            # 降维
            embeddings_scaled = self.scaler.transform(self.embeddings)
            
            if reduction_method == "tsne":
                reducer = TSNE(n_components=2, random_state=42)
                coords = reducer.fit_transform(embeddings_scaled)
            elif reduction_method == "pca":
                reducer = PCA(n_components=2, random_state=42)
                coords = reducer.fit_transform(embeddings_scaled)
            elif reduction_method == "umap":
                reducer = umap.UMAP(n_components=2, random_state=42)
                coords = reducer.fit_transform(embeddings_scaled)
            else:
                raise ValueError(f"Unknown reduction method: {reduction_method}")
            
            # 绘图
            plt.figure(figsize=(12, 8))
            
            unique_labels = np.unique(labels)
            colors = plt.cm.Set3(np.linspace(0, 1, len(unique_labels)))
            
            for label, color in zip(unique_labels, colors):
                if label == -1:  # 噪声点
                    plt.scatter(coords[labels == label, 0], coords[labels == label, 1], 
                              c='black', marker='x', s=50, alpha=0.6, label='Noise')
                else:
                    plt.scatter(coords[labels == label, 0], coords[labels == label, 1], 
                              c=[color], s=50, alpha=0.7, label=f'Cluster {label}')
            
            plt.title(f'{method_name.upper()} Clustering Results ({reduction_method.upper()} visualization)')
            plt.xlabel(f'{reduction_method.upper()} Component 1')
            plt.ylabel(f'{reduction_method.upper()} Component 2')
            
            # 只显示前10个聚类的图例
            handles, labels_legend = plt.gca().get_legend_handles_labels()
            if len(handles) > 11:  # 10个聚类 + 噪声
                plt.legend(handles[:11], labels_legend[:11], bbox_to_anchor=(1.05, 1), loc='upper left')
            else:
                plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
            
            plt.tight_layout()
            
            if save_plot:
                plot_path = Path(self.config.output_dir) / f"{method_name}_{reduction_method}_visualization.png"
                plt.savefig(plot_path, dpi=300, bbox_inches='tight')
                self.logger.info(f"Saved plot to {plot_path}")
            
            plt.show()
    
    def get_cluster_summary(self, method: str, top_n: int = 5) -> pd.DataFrame:
        """获取聚类摘要信息"""
        if method not in self.clustering_results:
            raise ValueError(f"Method {method} not found in results")
        
        result = self.clustering_results[method]
        labels = result["labels"]
        
        summary_data = []
        
        for cluster_id in np.unique(labels):
            if cluster_id == -1:  # 噪声点
                continue
            
            cluster_mask = labels == cluster_id
            cluster_logs = self.log_data[cluster_mask][self.config.log_column].tolist()
            
            summary_data.append({
                "cluster_id": cluster_id,
                "size": len(cluster_logs),
                "percentage": len(cluster_logs) / len(labels) * 100,
                "sample_logs": cluster_logs[:top_n]
            })
        
        summary_df = pd.DataFrame(summary_data)
        summary_df = summary_df.sort_values("size", ascending=False).reset_index(drop=True)
        
        return summary_df
    
    def run_full_pipeline(self, file_path: str) -> Dict:
        """运行完整的聚类流程"""
        # 1. 加载数据
        self.load_data(file_path)
        
        # 2. 生成embedding
        self.generate_embeddings()
        
        # 3. 执行聚类
        clustering_results = self.perform_clustering()
        
        # 4. 评估结果
        evaluation = self.evaluate_clustering()
        
        # 5. 保存结果
        saved_files = self.save_results()
        
        # 6. 可视化（可选）
        try:
            self.visualize_clusters()
        except Exception as e:
            self.logger.warning(f"Visualization failed: {e}")
        
        return {
            "clustering_results": clustering_results,
            "evaluation": evaluation,
            "saved_files": saved_files
        }


# 使用示例
if __name__ == "__main__":
    # 配置
    config = ClusteringConfig(
        api_key="your-dashscope-api-key",  # 设置你的API Key
        log_column="log_content",
        clustering_methods=["kmeans", "dbscan", "hierarchical"],
        batch_size=50,
        output_dir="clustering_results"
    )
    
    # 创建聚类器
    clusterer = LogEmbeddingClustering(config)
    
    # 运行完整流程
    # results = clusterer.run_full_pipeline("your_log_file.csv")
    
    print("Log embedding clustering module created successfully!")
    print("Usage:")
    print("1. Set your DashScope API key in the config")
    print("2. Call clusterer.run_full_pipeline('your_file.csv') to run the complete process")
    print("3. Check the results in the specified output directory")