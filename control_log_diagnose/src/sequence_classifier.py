# %%
"""
基于日志序列模式的成功/失败分类器

基于序列分析的insights设计的多特征分类算法：
1. 序列统计特征 (长度、多样性等)
2. 转换模式特征 (A->B 转换频率)
3. 路径模式特征 (起始->中间->结束)
4. 网络图特征 (节点度数、中心性等)
5. 序列相似性特征
"""

import os
import sys
import pandas as pd
import numpy as np
from collections import Counter, defaultdict
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score
from sklearn.preprocessing import StandardScaler
import networkx as nx
import pickle
import warnings

warnings.filterwarnings("ignore")

BASE_PATH = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if BASE_PATH not in sys.path:
    sys.path.append(BASE_PATH)


class LogSequenceClassifier:
    """日志序列分类器"""

    def __init__(self):
        self.model = None
        self.scaler = StandardScaler()
        self.feature_names = []
        self.transition_features = {}
        self.path_features = {}
        self.network_features = {}

    def extract_statistical_features(self, sequence):
        """提取序列统计特征"""
        features = {}

        # 基本统计特征
        features["seq_length"] = len(sequence)
        features["unique_patterns"] = len(set(sequence))
        features["diversity_ratio"] = len(set(sequence)) / len(sequence) if len(sequence) > 0 else 0

        # 重复模式分析
        pattern_counts = Counter(sequence)
        features["max_pattern_freq"] = max(pattern_counts.values()) if pattern_counts else 0
        features["pattern_entropy"] = self._calculate_entropy(list(pattern_counts.values()))

        # 序列位置特征
        features["start_pattern"] = sequence[0] if sequence else "EMPTY"
        features["end_pattern"] = sequence[-1] if sequence else "EMPTY"

        # 序列稳定性 - 连续重复的模式
        consecutive_repeats = 0
        max_consecutive = 0
        current_consecutive = 1

        for i in range(1, len(sequence)):
            if sequence[i] == sequence[i - 1]:
                current_consecutive += 1
                consecutive_repeats += 1
            else:
                max_consecutive = max(max_consecutive, current_consecutive)
                current_consecutive = 1

        features["consecutive_repeats"] = consecutive_repeats
        features["max_consecutive"] = max(max_consecutive, current_consecutive)
        features["stability_ratio"] = consecutive_repeats / len(sequence) if len(sequence) > 0 else 0

        return features

    def extract_transition_features(self, sequence, transition_vocab=None):
        """提取转换模式特征"""
        features = {}

        # 二元转换 (A -> B)
        transitions_2gram = []
        for i in range(len(sequence) - 1):
            transition = f"{sequence[i]} -> {sequence[i+1]}"
            transitions_2gram.append(transition)

        # 三元转换 (A -> B -> C)
        transitions_3gram = []
        for i in range(len(sequence) - 2):
            transition = f"{sequence[i]} -> {sequence[i+1]} -> {sequence[i+2]}"
            transitions_3gram.append(transition)

        # 转换统计
        features["num_transitions_2gram"] = len(transitions_2gram)
        features["num_transitions_3gram"] = len(transitions_3gram)
        features["unique_transitions_2gram"] = len(set(transitions_2gram))
        features["unique_transitions_3gram"] = len(set(transitions_3gram))

        # 转换多样性
        if len(transitions_2gram) > 0:
            features["transition_diversity_2gram"] = len(set(transitions_2gram)) / len(transitions_2gram)
        else:
            features["transition_diversity_2gram"] = 0

        if len(transitions_3gram) > 0:
            features["transition_diversity_3gram"] = len(set(transitions_3gram)) / len(transitions_3gram)
        else:
            features["transition_diversity_3gram"] = 0

        # 如果有预定义的转换词汇表，计算特定转换的出现次数
        if transition_vocab:
            transition_counts_2gram = Counter(transitions_2gram)
            transition_counts_3gram = Counter(transitions_3gram)

            for trans in transition_vocab.get("success_indicators_2gram", []):
                features[f"success_trans_2gram_{trans}"] = transition_counts_2gram.get(trans, 0)

            for trans in transition_vocab.get("failure_indicators_2gram", []):
                features[f"failure_trans_2gram_{trans}"] = transition_counts_2gram.get(trans, 0)

            for trans in transition_vocab.get("success_indicators_3gram", []):
                features[f"success_trans_3gram_{trans}"] = transition_counts_3gram.get(trans, 0)

            for trans in transition_vocab.get("failure_indicators_3gram", []):
                features[f"failure_trans_3gram_{trans}"] = transition_counts_3gram.get(trans, 0)

        return features

    def extract_path_features(self, sequence):
        """提取路径模式特征"""
        features = {}

        if len(sequence) < 3:
            features["path_pattern"] = "SHORT_SEQUENCE"
            features["path_complexity"] = 0
            return features

        # 起始->中间->结束模式
        start = sequence[0]
        end = sequence[-1]

        # 分析中间部分
        middle_section = sequence[1:-1]
        if middle_section:
            middle_counter = Counter(middle_section)
            dominant_middle = middle_counter.most_common(1)[0][0]
            features["path_pattern"] = f"{start} -> {dominant_middle} -> {end}"

            # 中间部分的复杂性
            features["middle_diversity"] = len(set(middle_section)) / len(middle_section)
            features["middle_dominant_ratio"] = middle_counter.most_common(1)[0][1] / len(middle_section)
        else:
            features["path_pattern"] = f"{start} -> DIRECT -> {end}"
            features["middle_diversity"] = 0
            features["middle_dominant_ratio"] = 0

        # 路径复杂性指标
        features["path_complexity"] = len(set(sequence)) / len(sequence)

        # 路径连续性 - 检查是否有明显的阶段性
        stage_changes = 0
        for i in range(1, len(sequence)):
            if sequence[i] != sequence[i - 1]:
                stage_changes += 1
        features["stage_changes"] = stage_changes
        features["stage_change_ratio"] = stage_changes / (len(sequence) - 1) if len(sequence) > 1 else 0

        return features

    def extract_network_features(self, sequence):
        """提取网络图特征"""
        features = {}

        if len(sequence) < 2:
            return {"network_nodes": 0, "network_edges": 0, "network_density": 0, "avg_degree": 0, "max_degree": 0}

        # 构建有向图
        G = nx.DiGraph()

        # 添加边
        for i in range(len(sequence) - 1):
            G.add_edge(sequence[i], sequence[i + 1])

        # 基本网络特征
        features["network_nodes"] = G.number_of_nodes()
        features["network_edges"] = G.number_of_edges()
        features["network_density"] = nx.density(G)

        # 度数特征
        degrees = [d for n, d in G.degree()]
        features["avg_degree"] = np.mean(degrees) if degrees else 0
        features["max_degree"] = max(degrees) if degrees else 0
        features["degree_std"] = np.std(degrees) if degrees else 0

        # 中心性特征
        if G.number_of_nodes() > 0:
            try:
                centrality = nx.degree_centrality(G)
                features["max_centrality"] = max(centrality.values()) if centrality else 0
                features["avg_centrality"] = np.mean(list(centrality.values())) if centrality else 0
            except:
                features["max_centrality"] = 0
                features["avg_centrality"] = 0
        else:
            features["max_centrality"] = 0
            features["avg_centrality"] = 0

        # 图的连通性
        if G.number_of_nodes() > 1:
            features["is_strongly_connected"] = int(nx.is_strongly_connected(G))
            features["num_strongly_connected_components"] = nx.number_strongly_connected_components(G)
        else:
            features["is_strongly_connected"] = 0
            features["num_strongly_connected_components"] = 1

        return features

    def extract_similarity_features(self, sequence, reference_sequences=None):
        """提取序列相似性特征"""
        features = {}

        if not reference_sequences:
            return {}

        # 计算与成功序列的相似性
        success_similarities = []
        failure_similarities = []

        for ref_seq, label in reference_sequences:
            similarity = self._sequence_jaccard_similarity(sequence, ref_seq)
            if label == 0:  # 成功
                success_similarities.append(similarity)
            else:  # 失败
                failure_similarities.append(similarity)

        features["max_success_similarity"] = max(success_similarities) if success_similarities else 0
        features["avg_success_similarity"] = np.mean(success_similarities) if success_similarities else 0
        features["max_failure_similarity"] = max(failure_similarities) if failure_similarities else 0
        features["avg_failure_similarity"] = np.mean(failure_similarities) if failure_similarities else 0

        # 相似性比率
        if features["avg_failure_similarity"] > 0:
            features["similarity_ratio"] = features["avg_success_similarity"] / features["avg_failure_similarity"]
        else:
            features["similarity_ratio"] = features["avg_success_similarity"] * 10  # 大值表示更像成功

        return features

    def _sequence_jaccard_similarity(self, seq1, seq2):
        """计算两个序列的Jaccard相似性"""
        set1 = set(seq1)
        set2 = set(seq2)
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))
        return intersection / union if union > 0 else 0

    def _calculate_entropy(self, values):
        """计算熵值"""
        if not values:
            return 0

        total = sum(values)
        probabilities = [v / total for v in values]
        entropy = -sum(p * np.log2(p) for p in probabilities if p > 0)
        return entropy

    def extract_all_features(self, sequence, transition_vocab=None, reference_sequences=None):
        """提取所有特征"""
        features = {}

        # 合并所有特征
        features.update(self.extract_statistical_features(sequence))
        features.update(self.extract_transition_features(sequence, transition_vocab))
        features.update(self.extract_path_features(sequence))
        features.update(self.extract_network_features(sequence))
        features.update(self.extract_similarity_features(sequence, reference_sequences))

        return features

    def build_vocabulary(self, sequences_df):
        """构建特征词汇表"""
        print("构建特征词汇表...")

        success_sequences = sequences_df[sequences_df["label_mapped"] == 0]["pattern_sequence"]
        failure_sequences = sequences_df[sequences_df["label_mapped"] == 1]["pattern_sequence"]

        # 构建转换词汇表
        success_transitions_2gram = defaultdict(int)
        failure_transitions_2gram = defaultdict(int)
        success_transitions_3gram = defaultdict(int)
        failure_transitions_3gram = defaultdict(int)

        # 收集转换模式
        for seq in success_sequences:
            # 二元转换
            for i in range(len(seq) - 1):
                trans = f"{seq[i]} -> {seq[i+1]}"
                success_transitions_2gram[trans] += 1
            # 三元转换
            for i in range(len(seq) - 2):
                trans = f"{seq[i]} -> {seq[i+1]} -> {seq[i+2]}"
                success_transitions_3gram[trans] += 1

        for seq in failure_sequences:
            # 二元转换
            for i in range(len(seq) - 1):
                trans = f"{seq[i]} -> {seq[i+1]}"
                failure_transitions_2gram[trans] += 1
            # 三元转换
            for i in range(len(seq) - 2):
                trans = f"{seq[i]} -> {seq[i+1]} -> {seq[i+2]}"
                failure_transitions_3gram[trans] += 1

        # 找出最具区分性的转换
        def find_distinctive_transitions(success_trans, failure_trans, min_freq=5, top_k=10):
            distinctive = []

            total_success = sum(success_trans.values())
            total_failure = sum(failure_trans.values())

            all_trans = set(success_trans.keys()) | set(failure_trans.keys())

            for trans in all_trans:
                s_count = success_trans.get(trans, 0)
                f_count = failure_trans.get(trans, 0)

                if s_count + f_count >= min_freq:
                    s_rate = s_count / total_success if total_success > 0 else 0
                    f_rate = f_count / total_failure if total_failure > 0 else 0

                    if f_rate > 0:
                        ratio = s_rate / f_rate
                    elif s_rate > 0:
                        ratio = 100  # 很大的值
                    else:
                        continue

                    distinctive.append((trans, ratio, s_count, f_count))

            # 分别取成功和失败的指标
            distinctive.sort(key=lambda x: x[1], reverse=True)
            success_indicators = [x[0] for x in distinctive[:top_k]]

            distinctive.sort(key=lambda x: x[1])
            failure_indicators = [x[0] for x in distinctive[:top_k]]

            return success_indicators, failure_indicators

        # 构建转换词汇表
        success_2gram, failure_2gram = find_distinctive_transitions(success_transitions_2gram, failure_transitions_2gram)
        success_3gram, failure_3gram = find_distinctive_transitions(success_transitions_3gram, failure_transitions_3gram)

        self.transition_features = {
            "success_indicators_2gram": success_2gram,
            "failure_indicators_2gram": failure_2gram,
            "success_indicators_3gram": success_3gram,
            "failure_indicators_3gram": failure_3gram,
        }

        # 构建参考序列（用于相似性计算）
        reference_sequences = []
        # 随机采样一些代表性序列
        success_sample = success_sequences.sample(n=min(100, len(success_sequences)), random_state=42)
        failure_sample = failure_sequences.sample(n=min(100, len(failure_sequences)), random_state=42)

        for seq in success_sample:
            reference_sequences.append((seq, 0))
        for seq in failure_sample:
            reference_sequences.append((seq, 1))

        self.reference_sequences = reference_sequences

        print(f"词汇表构建完成:")
        print(f"- 成功指标转换 (2-gram): {len(success_2gram)}")
        print(f"- 失败指标转换 (2-gram): {len(failure_2gram)}")
        print(f"- 成功指标转换 (3-gram): {len(success_3gram)}")
        print(f"- 失败指标转换 (3-gram): {len(failure_3gram)}")
        print(f"- 参考序列数量: {len(reference_sequences)}")

    def prepare_features(self, sequences_df):
        """准备训练特征"""
        print("提取特征...")

        feature_list = []
        labels = []

        for idx, row in sequences_df.iterrows():
            sequence = row["pattern_sequence"]
            label = row["label_mapped"]

            features = self.extract_all_features(sequence, self.transition_features, self.reference_sequences)

            feature_list.append(features)
            labels.append(label)

            if (idx + 1) % 1000 == 0:
                print(f"已处理 {idx + 1}/{len(sequences_df)} 条序列")

        # 转换为DataFrame
        features_df = pd.DataFrame(feature_list)

        # 处理分类特征
        categorical_features = ["start_pattern", "end_pattern", "path_pattern"]
        for col in categorical_features:
            if col in features_df.columns:
                # 使用频率编码
                freq_encoding = features_df[col].value_counts().to_dict()
                features_df[f"{col}_freq"] = features_df[col].map(freq_encoding)
                features_df = features_df.drop(columns=[col])

        # 处理缺失值
        features_df = features_df.fillna(0)

        self.feature_names = features_df.columns.tolist()

        print(f"特征提取完成，共 {len(self.feature_names)} 个特征")
        return features_df.values, np.array(labels)

    def train(self, sequences_df, test_size=0.2, random_state=42):
        """训练模型"""
        print("开始训练序列分类器...")

        # 构建词汇表
        self.build_vocabulary(sequences_df)

        # 准备特征
        X, y = self.prepare_features(sequences_df)

        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=test_size, random_state=random_state, stratify=y)

        # 特征标准化
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)

        # 训练多个模型进行集成
        models = {
            "random_forest": RandomForestClassifier(
                n_estimators=200, max_depth=15, min_samples_split=5, min_samples_leaf=2, random_state=random_state, n_jobs=-1
            ),
            "gradient_boosting": GradientBoostingClassifier(n_estimators=200, max_depth=8, learning_rate=0.1, random_state=random_state),
        }

        best_score = 0
        best_model = None
        best_model_name = None

        for name, model in models.items():
            print(f"\n训练 {name}...")

            # 交叉验证
            cv_scores = cross_val_score(model, X_train_scaled, y_train, cv=5, scoring="roc_auc")
            print(f"{name} CV AUC: {cv_scores.mean():.4f} (+/- {cv_scores.std() * 2:.4f})")

            # 训练模型
            model.fit(X_train_scaled, y_train)

            # 测试集评估
            test_score = roc_auc_score(y_test, model.predict_proba(X_test_scaled)[:, 1])
            print(f"{name} Test AUC: {test_score:.4f}")

            if test_score > best_score:
                best_score = test_score
                best_model = model
                best_model_name = name

        self.model = best_model
        print(f"\n最佳模型: {best_model_name} (AUC: {best_score:.4f})")

        # 详细评估
        y_pred = self.model.predict(X_test_scaled)
        y_prob = self.model.predict_proba(X_test_scaled)[:, 1]

        print("\n分类报告:")
        print(classification_report(y_test, y_pred, target_names=["Success", "Failure"]))

        print("\n混淆矩阵:")
        cm = confusion_matrix(y_test, y_pred)
        print(cm)

        # 特征重要性
        if hasattr(self.model, "feature_importances_"):
            feature_importance = pd.DataFrame({"feature": self.feature_names, "importance": self.model.feature_importances_}).sort_values(
                "importance", ascending=False
            )

            print("\n前15个重要特征:")
            print(feature_importance.head(15))

        return {
            "test_auc": best_score,
            "model_name": best_model_name,
            "feature_importance": feature_importance if hasattr(self.model, "feature_importances_") else None,
        }

    def predict(self, sequence):
        """预测单个序列"""
        if self.model is None:
            raise ValueError("模型未训练，请先调用 train() 方法")

        # 提取特征
        features = self.extract_all_features(sequence, self.transition_features, self.reference_sequences)

        # 转换为DataFrame并处理分类特征
        features_df = pd.DataFrame([features])

        categorical_features = ["start_pattern", "end_pattern", "path_pattern"]
        for col in categorical_features:
            if col in features_df.columns:
                # 使用训练时的频率编码（如果存在）
                features_df[f"{col}_freq"] = 0  # 默认值，对未见过的值
                features_df = features_df.drop(columns=[col])

        # 确保特征顺序和数量一致
        for feature_name in self.feature_names:
            if feature_name not in features_df.columns:
                features_df[feature_name] = 0

        features_df = features_df[self.feature_names]
        features_df = features_df.fillna(0)

        # 标准化
        X_scaled = self.scaler.transform(features_df.values)

        # 预测
        prediction = self.model.predict(X_scaled)[0]
        probability = self.model.predict_proba(X_scaled)[0]

        return {
            "prediction": int(prediction),
            "prediction_label": "Failure" if prediction == 1 else "Success",
            "probability_success": probability[0],
            "probability_failure": probability[1],
            "confidence": max(probability),
        }

    def save_model(self, filepath):
        """保存模型"""
        model_data = {
            "model": self.model,
            "scaler": self.scaler,
            "feature_names": self.feature_names,
            "transition_features": self.transition_features,
            "reference_sequences": self.reference_sequences,
        }

        with open(filepath, "wb") as f:
            pickle.dump(model_data, f)

        print(f"模型已保存到: {filepath}")

    def load_model(self, filepath):
        """加载模型"""
        with open(filepath, "rb") as f:
            model_data = pickle.load(f)

        self.model = model_data["model"]
        self.scaler = model_data["scaler"]
        self.feature_names = model_data["feature_names"]
        self.transition_features = model_data["transition_features"]
        self.reference_sequences = model_data["reference_sequences"]

        print(f"模型已从 {filepath} 加载")


# %%
# 使用示例和测试
if __name__ == "__main__":
    # 加载数据
    data_path = os.path.join(BASE_PATH, "data/control_log/drain_pattern_results/ecs_regionmaster_info_log_with_pattern_ids.parquet")
    data = pd.read_parquet(data_path, engine="pyarrow")

    # 预处理数据
    data["label_mapped"] = data["label"].map({-1: 1, 0: 0})
    available_data = data[data["log_time"] - 10 < data["timestamp"]]

    # 生成序列数据
    pattern_sequences = (
        available_data.groupby(["request_id", "label_mapped"])
        .apply(lambda x: x.sort_values("log_time")["pattern_id"].tolist(), include_groups=False)
        .reset_index(name="pattern_sequence")
    )

    print(f"总序列数: {len(pattern_sequences)}")
    print(f"标签分布: {pattern_sequences['label_mapped'].value_counts().sort_index().to_dict()}")

    # 创建分类器
    classifier = LogSequenceClassifier()

    # 训练模型
    results = classifier.train(pattern_sequences)

    # 保存模型
    model_path = os.path.join(BASE_PATH, "control_log_diagnose/models/sequence_classifier.pkl")
    os.makedirs(os.path.dirname(model_path), exist_ok=True)
    classifier.save_model(model_path)

    # 测试预测
    print("\n=== 测试预测 ===")

    # 测试几个样本
    test_samples = pattern_sequences.sample(n=5, random_state=42)

    for idx, row in test_samples.iterrows():
        sequence = row["pattern_sequence"]
        true_label = row["label_mapped"]

        prediction = classifier.predict(sequence)

        print(f"\n序列长度: {len(sequence)}")
        print(f"真实标签: {'Failure' if true_label == 1 else 'Success'}")
        print(f"预测结果: {prediction['prediction_label']}")
        print(f"成功概率: {prediction['probability_success']:.3f}")
        print(f"失败概率: {prediction['probability_failure']:.3f}")
        print(f"置信度: {prediction['confidence']:.3f}")
        print(f"预测正确: {'✓' if prediction['prediction'] == true_label else '✗'}")

# %%
