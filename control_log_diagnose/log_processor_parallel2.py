import asyncio
import json
import os
import sys
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any, Optional
import yaml
import logging
import aiofiles
from dataclasses import dataclass
from queue import Queue
import threading
from concurrent.futures import ThreadPoolExecutor
from rich.progress import Progress, TaskID
from rich.console import Console
import time

# 添加基础路径
BASE_PATH = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if BASE_PATH not in sys.path:
    sys.path.append(BASE_PATH)

from connector.sls import get_related_logs
import re


@dataclass
class LogTask:
    """日志处理任务数据结构"""

    request_id: str
    instance_id: str
    region_name: str
    action_time: str
    nc_ip: str
    action_type: str
    start_time: float
    end_time: float


@dataclass
class LogResult:
    """日志处理结果数据结构"""

    request_id: str
    action_type: str
    logstore_results: Dict[str, List[Dict]]
    logcluster_results: Dict[str, List[Dict]]
    error: Optional[str] = None


class LogFieldExtractor:
    """根据配置提取日志字段的类"""

    def __init__(self, extraction_config: Dict[str, Any]):
        self.extraction_config = extraction_config
        self.logstore_configs = extraction_config.get("logstore_configs", {})
        self.default_config = extraction_config.get("default_config", {})

    def extract_fields(
        self, log_entry: Dict[str, Any], logstore_name: str, request_id: str, instance_id: str, action_type: str, action_time: str, nc_ip: str
    ) -> Dict[str, Any]:
        """根据logstore配置提取日志字段"""
        extracted_fields = {"request_id": request_id, "instance_id": instance_id, "action_type": action_type, "action_time": action_time, "nc_ip": nc_ip}

        log_info = {}
        config = self.logstore_configs.get(logstore_name, {})

        if not config:
            config = self.default_config

        # 提取内容字段
        content_field = config.get("content_field")
        if content_field and content_field in log_entry.contents:
            log_info["content"] = log_entry.contents[content_field]
        else:
            content_fields = self.default_config.get("content_fields", ["content", "message", "detail"])
            for field in content_fields:
                if field in log_entry.contents:
                    log_info["content"] = log_entry.contents[field]
                    break

        log_info["log_time"] = log_entry.timestamp
        log_info["source"] = log_entry.source

        # 提取额外字段
        additional_fields = config.get("additional_fields", [])
        for field in additional_fields:
            if field in log_entry.contents:
                log_info[field] = log_entry.contents.get(field)

        extracted_fields["log"] = log_info
        return extracted_fields


class ProjectMapper:
    """处理 project - region 映射关系的类"""

    def __init__(self, config: Dict[str, Any]):
        self.region_project_mapping = config.get("region_project_mapping", {})
        with open(os.path.join(BASE_PATH, "control_log_diagnose", "sls_region_conf.json"), "r") as f:
            self.region_endpoint_mapping = json.load(f)

    def get_project_by_region(self, project: str, region_name: str) -> str:
        """根据region_name和project选择对应的子project"""
        if region_name in self.region_project_mapping and project in self.region_project_mapping[region_name]:
            return self.region_project_mapping[region_name][project]
        return project

    def get_endpoint_by_region(self, region_name: str) -> str:
        """根据region_name获取对应的endpoint"""
        return self.region_endpoint_mapping.get(region_name, None)


class LogQueryGenerator:
    """生成日志查询语句的类"""

    @staticmethod
    def generate_logstore_query(item: Dict[str, Any], instance_id: str, request_id: str, nc_ip: str) -> str:
        """生成logstore查询语句"""
        query_str = "*"
        ipv4_pattern = r"^(\d{1,3}\.){3}\d{1,3}$"
        key_fields = item.get("key_fields", [])

        if item.get("request_id", "none") == "none":
            if re.match(ipv4_pattern, nc_ip):
                query_str += f" AND __source__: {nc_ip}"
            query_str += (
                f" | set session parallel_sql=true; select * where {key_fields[0]} LIKE '%{instance_id}%' OR {key_fields[0]} LIKE '%{request_id}%' limit 10000"
            )
        else:
            if item["logstore"] == "schedule_trace":
                query_str += f" and requestId: '{request_id}'"
            else:
                query_str += f" and request_id: '{request_id}'"

        return query_str

    @staticmethod
    def generate_logcluster_query(item: str, instance_id: str, request_id: str, nc_ip: str) -> str:
        """生成logcluster查询语句"""
        query = "*"
        if item.startswith("ecs_region_master"):
            query += f" | set session parallel_sql=true; select * where content LIKE '%{instance_id}%' limit 10000"
        else:
            ipv4_pattern = r"^(\d{1,3}\.){3}\d{1,3}$"
            if re.match(ipv4_pattern, nc_ip):
                query += f" AND nc: {nc_ip} | set session parallel_sql=true; select * where content LIKE '%{request_id}%' OR content LIKE '%{instance_id}%' limit 10000"
            else:
                query += f" | set session parallel_sql=true; select * where content LIKE '%{request_id}%' OR content LIKE '%{instance_id}%' limit 10000"
        return query


class LogRetriever:
    """负责从日志系统获取日志的类"""

    def __init__(self, project_mapper: ProjectMapper):
        self.project_mapper = project_mapper

    def retrieve_logstore_logs(
        self, item: Dict[str, Any], instance_id: str, request_id: str, start_time: float, end_time: float, nc_ip: str, region_name: str
    ) -> tuple:
        """获取logstore日志"""
        try:
            query_str = LogQueryGenerator.generate_logstore_query(item, instance_id, request_id, nc_ip)

            # 尝试不同的region组合来找到匹配的project
            region_parts = region_name.split("-")
            project = item["project"]
            mapped_project = None
            mapped_region = None

            for i in range(2, min(5, len(region_parts) + 1)):
                region_key = "-".join(region_parts[:i])
                temp_mapped_project = self.project_mapper.get_project_by_region(item["project"], region_key)
                if temp_mapped_project != item["project"]:
                    mapped_project = temp_mapped_project
                    mapped_region = region_key
                    break
            else:
                temp_mapped_project = self.project_mapper.get_project_by_region(item["project"], region_name)
                if temp_mapped_project != item["project"]:
                    mapped_project = temp_mapped_project
                    mapped_region = region_name

            # 查询原始project
            logs = get_related_logs(
                item["project"],
                item["logstore"],
                start_time,
                end_time,
                query_str=query_str,
                endpoint=self.project_mapper.get_endpoint_by_region(region_name),
                max_retries=5,
                retry_delay=2,
            )

            # 如果映射project存在且不同，也查询它
            if mapped_project and mapped_project != item["project"]:
                mapped_endpoint = (
                    self.project_mapper.get_endpoint_by_region(mapped_region) if mapped_region else self.project_mapper.get_endpoint_by_region(region_name)
                )
                mapped_logs = get_related_logs(
                    mapped_project,
                    item["logstore"],
                    start_time,
                    end_time,
                    query_str=query_str,
                    endpoint=mapped_endpoint,
                    max_retries=5,
                    retry_delay=2,
                )
                logs.extend(mapped_logs)

            project = mapped_project if mapped_project else item["project"]
            return logs, project
        except Exception as e:
            return [], item["project"]

    def retrieve_logcluster_logs(self, item: str, value: str, instance_id: str, request_id: str, nc_ip: str, start_time: float, end_time: float) -> List[Any]:
        """获取logcluster日志"""
        try:
            query = LogQueryGenerator.generate_logcluster_query(item, instance_id, request_id, nc_ip)
            logs = get_related_logs(
                "log-cluster",
                value,
                start_time,
                end_time,
                query_str=query,
                max_retries=5,
                retry_delay=2,
            )
            return logs
        except Exception as e:
            return []


class CheckpointManager:
    """检查点管理器，负责跟踪已处理的请求"""

    def __init__(self, checkpoint_file: str):
        self.checkpoint_file = checkpoint_file
        self.processed_requests = self._load_checkpoint()
        self.lock = threading.Lock()

    def _load_checkpoint(self) -> Dict[str, Any]:
        """加载检查点文件"""
        if os.path.exists(self.checkpoint_file):
            try:
                with open(self.checkpoint_file, "r") as f:
                    return json.load(f)
            except Exception:
                return {}
        return {}

    def is_processed(self, request_id: str) -> bool:
        """检查请求是否已处理"""
        with self.lock:
            return request_id in self.processed_requests

    def mark_processed(self, request_id: str):
        """标记请求为已处理"""
        with self.lock:
            self.processed_requests[request_id] = True

    def save_checkpoint(self):
        """保存检查点"""
        with self.lock:
            try:
                with open(self.checkpoint_file, "w") as f:
                    json.dump(self.processed_requests, f, indent=2)
            except Exception as e:
                logging.warning(f"Failed to save checkpoint: {e}")


class ResultManager:
    """结果管理器，负责保存处理结果"""

    def __init__(self, output_file: str, max_file_size: int = 100 * 1024 * 1024):
        self.output_file = output_file
        self.max_file_size = max_file_size
        self.file_counter = 60
        self.lock = threading.Lock()
        self.pending_results = []
        self.batch_size = 10

    async def save_result(self, result: LogResult):
        """保存单个结果"""
        with self.lock:
            self.pending_results.append(result)
            if len(self.pending_results) >= self.batch_size:
                asyncio.create_task(self._flush_results())

    async def _flush_results(self):
        """批量刷新结果到文件"""
        results_to_save = []
        with self.lock:
            if not self.pending_results:
                return
            results_to_save = self.pending_results.copy()
            self.pending_results.clear()

        await self._write_results_to_file(results_to_save)

    async def _write_results_to_file(self, results: List[LogResult]):
        """将结果写入文件"""
        try:
            # 检查文件大小，如果过大则创建新文件
            if os.path.exists(self.output_file) and os.path.getsize(self.output_file) > self.max_file_size:
                self.file_counter += 1
                base_name = os.path.splitext(self.output_file)[0]
                new_file_name = f"{base_name}_{self.file_counter}.jsonl"
                os.rename(self.output_file, new_file_name)

            async with aiofiles.open(self.output_file, "a", encoding="utf-8") as f:
                for result in results:
                    result_data = {
                        result.request_id: {
                            "action_type": result.action_type,
                            "logstore": result.logstore_results,
                            "log_cluster": result.logcluster_results,
                            "error": result.error,
                        }
                    }
                    await f.write(json.dumps(result_data, ensure_ascii=False) + "\n")
        except Exception as e:
            logging.error(f"Failed to write results to file: {e}")

    async def finalize(self):
        """完成所有剩余的写入操作"""
        await self._flush_results()


class TaskProducer:
    """任务生产者，负责生成处理任务"""

    def __init__(self, records: pd.DataFrame, checkpoint_manager: CheckpointManager, task_queue: Queue):
        self.records = records
        self.checkpoint_manager = checkpoint_manager
        self.task_queue = task_queue

    def produce_tasks(self):
        """生产任务到队列"""
        for _, row in self.records.iterrows():
            request_id = row["request_id"]

            # 跳过已处理的请求
            if self.checkpoint_manager.is_processed(request_id):
                continue

            # 计算时间范围
            action_time = row["gmt_create"]
            start_time = datetime.strptime(action_time, "%Y-%m-%d %H:%M:%S").timestamp() - 60 * 5
            end_time = start_time + 60 * 10

            task = LogTask(
                request_id=request_id,
                instance_id=row["instance_id"],
                region_name=row["region_name"],
                action_time=action_time,
                nc_ip=row["nc_ip"],
                action_type=row["action_type"],
                start_time=start_time,
                end_time=end_time,
            )

            self.task_queue.put(task)

        # 添加结束标记
        self.task_queue.put(None)


class TaskConsumer:
    """任务消费者，负责处理日志任务"""

    def __init__(
        self,
        config: Dict[str, Any],
        task_queue: Queue,
        result_manager: ResultManager,
        checkpoint_manager: CheckpointManager,
        progress: Progress,
        task_id: TaskID,
    ):
        self.config = config
        self.task_queue = task_queue
        self.result_manager = result_manager
        self.checkpoint_manager = checkpoint_manager
        self.progress = progress
        self.task_id = task_id

        # 初始化组件
        self.project_mapper = ProjectMapper(config)
        self.log_retriever = LogRetriever(self.project_mapper)

        # 加载字段提取配置
        extraction_config_path = os.path.join(BASE_PATH, "control_log_diagnose", "log_extraction_config.yaml")
        if os.path.exists(extraction_config_path):
            with open(extraction_config_path, "r", encoding="utf-8") as f:
                extraction_config = yaml.safe_load(f)
        else:
            extraction_config = {}

        self.field_extractor = LogFieldExtractor(extraction_config)

        # 设置日志
        self.logger = logging.getLogger(f"consumer-{threading.current_thread().name}")

    async def consume_tasks(self):
        """消费任务队列中的任务"""
        while True:
            try:
                # 获取任务（非阻塞）
                task = self.task_queue.get_nowait()
                if task is None:  # 结束标记
                    break

                # 处理任务
                result = await self._process_task(task)

                # 保存结果
                await self.result_manager.save_result(result)

                # 标记为已处理
                self.checkpoint_manager.mark_processed(task.request_id)

                # 更新进度
                self.progress.update(self.task_id, advance=1)

                # 标记任务完成
                self.task_queue.task_done()

            except:
                # 队列为空，稍等一下
                await asyncio.sleep(0.1)

    async def _process_task(self, task: LogTask) -> LogResult:
        """处理单个任务"""
        try:
            self.logger.info(f"Processing request: {task.request_id}")

            # 处理logstore日志
            logstore_results = await self._process_logstores(task)

            # 处理logcluster日志
            logcluster_results = await self._process_logclusters(task)

            return LogResult(request_id=task.request_id, action_type=task.action_type, logstore_results=logstore_results, logcluster_results=logcluster_results)

        except Exception as e:
            self.logger.error(f"Error processing task {task.request_id}: {e}")
            return LogResult(request_id=task.request_id, action_type=task.action_type, logstore_results={}, logcluster_results={}, error=str(e))

    async def _process_logstores(self, task: LogTask) -> Dict[str, List[Dict]]:
        """处理logstore日志"""
        results = {}

        for item in self.config.get("logstores", []):
            try:
                # 在线程池中执行同步的日志检索
                loop = asyncio.get_event_loop()
                logs, project = await loop.run_in_executor(
                    None,
                    self.log_retriever.retrieve_logstore_logs,
                    item,
                    task.instance_id,
                    task.request_id,
                    task.start_time,
                    task.end_time,
                    task.nc_ip,
                    task.region_name,
                )

                # 提取字段
                extracted_logs = []
                if logs:
                    for log_entry in logs:
                        extracted_log = self.field_extractor.extract_fields(
                            log_entry, item["logstore"], task.request_id, task.instance_id, task.action_type, task.action_time, task.nc_ip
                        )
                        extracted_logs.append(extracted_log)

                results[item["logstore"]] = extracted_logs

                if extracted_logs:
                    self.logger.info(f"FOUND LOGS: {len(extracted_logs)} logs for {task.request_id} in {project}/{item['logstore']}")
                else:
                    self.logger.info(f"NO LOGS: No logs found for {task.request_id} in {project}/{item['logstore']}")

            except Exception as e:
                self.logger.error(f"Error processing logstore {item['logstore']} for {task.request_id}: {e}")
                results[item["logstore"]] = []

        return results

    async def _process_logclusters(self, task: LogTask) -> Dict[str, List[Dict]]:
        """处理logcluster日志"""
        results = {}

        for item, value in self.config.get("log_clusters", {}).items():
            try:
                # 在线程池中执行同步的日志检索
                loop = asyncio.get_event_loop()
                logs = await loop.run_in_executor(
                    None,
                    self.log_retriever.retrieve_logcluster_logs,
                    item,
                    value,
                    task.instance_id,
                    task.request_id,
                    task.nc_ip,
                    task.start_time,
                    task.end_time,
                )

                # 提取字段
                extracted_logs = []
                if logs:
                    for log_entry in logs:
                        extracted_log = self.field_extractor.extract_fields(
                            log_entry, value, task.request_id, task.instance_id, task.action_type, task.action_time, task.nc_ip
                        )
                        extracted_logs.append(extracted_log)

                results[item] = extracted_logs

                if extracted_logs:
                    self.logger.info(f"FOUND LOGS: {len(extracted_logs)} logs for {task.request_id} in log-cluster/{value}")
                else:
                    self.logger.info(f"NO LOGS: No logs found for {task.request_id} in log-cluster/{value}")

            except Exception as e:
                self.logger.error(f"Error processing logcluster {item} for {task.request_id}: {e}")
                results[item] = []

        return results


class LogProcessorParallel:
    """并行日志处理器主类"""

    def __init__(self, config: Dict[str, Any], max_workers: int = 10, verbose: bool = True):
        self.config = config
        self.max_workers = max_workers
        self.verbose = verbose

        # 创建输出目录
        self.log_output_dir = os.path.join(BASE_PATH, "logs_output")
        os.makedirs(self.log_output_dir, exist_ok=True)

        # 设置文件路径
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        timestamp = "20250805_224346"
        self.output_file = os.path.join(self.log_output_dir, f"all_results_{timestamp}.jsonl")
        self.checkpoint_file = os.path.join(self.log_output_dir, f"checkpoint_{timestamp}.json")

        # 初始化组件
        self.checkpoint_manager = CheckpointManager(self.checkpoint_file)
        self.result_manager = ResultManager(self.output_file)
        self.task_queue = Queue(maxsize=max_workers * 2)

        # 设置日志
        self._setup_logging()

        self.console = Console()

    def _setup_logging(self):
        """设置日志配置"""
        log_file = os.path.join(self.log_output_dir, "parallel_processor.log")
        logging.basicConfig(
            level=logging.INFO if self.verbose else logging.WARNING,
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            handlers=[logging.FileHandler(log_file, encoding="utf-8"), logging.StreamHandler()],
        )
        self.logger = logging.getLogger(__name__)

    async def process_records(self, records: pd.DataFrame):
        """处理所有记录"""
        # 过滤已处理的记录
        unprocessed_records = records[~records["request_id"].apply(self.checkpoint_manager.is_processed)]
        total_records = len(unprocessed_records)

        if total_records == 0:
            self.console.print("[green]All records have been processed!")
            return

        self.logger.info(f"Processing {total_records} unprocessed records with {self.max_workers} workers")

        with Progress() as progress:
            task_id = progress.add_task(f"[green]Processing {total_records} records...", total=total_records)

            # 创建生产者
            producer = TaskProducer(unprocessed_records, self.checkpoint_manager, self.task_queue)

            # 在线程中运行生产者
            producer_thread = threading.Thread(target=producer.produce_tasks)
            producer_thread.start()

            # 创建消费者任务
            consumer_tasks = []
            for i in range(self.max_workers):
                consumer = TaskConsumer(self.config, self.task_queue, self.result_manager, self.checkpoint_manager, progress, task_id)
                task = asyncio.create_task(consumer.consume_tasks())
                consumer_tasks.append(task)

            # 等待所有消费者完成
            await asyncio.gather(*consumer_tasks)

            # 等待生产者完成
            producer_thread.join()

            # 完成结果保存
            await self.result_manager.finalize()

            # 保存检查点
            self.checkpoint_manager.save_checkpoint()

        self.console.print(f"[green]Processing completed! Results saved to {self.output_file}")


async def main(verbose: bool = True, max_workers: int = 10):
    """主函数"""
    # 加载配置
    config = yaml.safe_load(open("control_log_diagnose/logstore_config.yaml", "r"))

    # 加载region映射
    with open(os.path.join(BASE_PATH, "control_log_diagnose", "parsed_region_mapping.json"), "r") as f:
        region_project_mapping = json.load(f)
    config["region_project_mapping"] = region_project_mapping

    # 加载数据
    records = pd.read_csv("data/control_log/start_vm_records/merged_start_vm_records.csv")
    records = records[::-1].reset_index(drop=True)  # 倒序处理

    # 创建处理器并运行
    processor = LogProcessorParallel(config, max_workers=max_workers, verbose=verbose)
    await processor.process_records(records)


if __name__ == "__main__":
    asyncio.run(main(verbose=False, max_workers=10))
