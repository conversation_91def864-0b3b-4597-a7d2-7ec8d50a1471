#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建用于测试Drain模板提取的示例数据
"""

import os
import pandas as pd
import numpy as np
from pathlib import Path


def create_sample_log_data():
    """创建示例日志数据"""
    
    # 示例日志模板和变化
    sample_logs = [
        # 模板1: QEMU机器启动失败
        "2025-07-22 18:10:59.329+0000[pid=5580,iso=0][2025/07/22 18:10:57][1753179057331][error] QEMU |  No machine 'qemu-i-j6c3hy027sbq648valvs' known",
        "2025-07-22 18:11:00.142+0000[pid=5581,iso=0][2025/07/22 18:11:58][1753179057445][error] QEMU |  No machine 'qemu-eci-6we0hfjmlrp68cur5u49' known",
        "2025-07-22 18:11:01.256+0000[pid=5582,iso=0][2025/07/22 18:11:59][1753179057567][error] QEMU |  No machine 'qemu-eci-t4n0jya7swzt4pipcde8' known",
        "2025-07-22 18:11:02.389+0000[pid=5583,iso=0][2025/07/22 18:12:00][1753179057689][error] QEMU |  No machine 'qemu-i-abc123def456' known",
        "2025-07-22 18:11:03.512+0000[pid=5584,iso=0][2025/07/22 18:12:01][1753179057812][error] QEMU |  No machine 'qemu-eci-xyz789uvw012' known",
        
        # 模板2: 网络连接失败
        "2025-07-22 18:10:59.521+0000[pid=5580,iso=0][2025/07/22 18:10:57][1753179057490][error] Connection failed to ************* on port 22: Connection refused",
        "2025-07-22 18:11:00.634+0000[pid=5581,iso=0][2025/07/22 18:11:58][1753179057634][error] Connection failed to ******** on port 443: Connection refused", 
        "2025-07-22 18:11:01.745+0000[pid=5582,iso=0][2025/07/22 18:11:59][1753179057745][error] Connection failed to ********** on port 8080: Connection timeout",
        "2025-07-22 18:11:02.856+0000[pid=5583,iso=0][2025/07/22 18:12:00][1753179057856][error] Connection failed to ************ on port 3306: Connection refused",
        
        # 模板3: 实例ID相关
        "2025-07-22 18:10:59.666+0000[pid=5580,iso=0][2025/07/22 18:10:57][1753179057681][info] instanceId = i-2ze9c4ik6b5vx3zqy3jq",
        "2025-07-22 18:11:00.777+0000[pid=5581,iso=0][2025/07/22 18:11:58][1753179057777][info] instanceId = i-bp15hdmn8sld7q9xr8kv",
        "2025-07-22 18:11:01.888+0000[pid=5582,iso=0][2025/07/22 18:11:59][1753179057888][info] instanceId = i-uf62k8j9d3ml6vbnx2wy",
        
        # 模板4: 文件操作错误
        "2025-07-22 18:10:59.666+0000[pid=5580,iso=0][2025/07/22 18:10:57][1753179057681][error] RetryFailedPlugin failed:open c:\\ProgramData\\aliyun\\vminit/INSTANCE_i-2ze9c4ik6b5vx3zqy3jq/FAILED_PLUGIN: The system cannot find the file specified.",
        "2025-07-22 18:11:00.999+0000[pid=5581,iso=0][2025/07/22 18:11:58][1753179057999][error] RetryFailedPlugin failed:open c:\\ProgramData\\aliyun\\vminit/INSTANCE_i-bp15hdmn8sld7q9xr8kv/FAILED_PLUGIN: The system cannot find the file specified.",
        "2025-07-22 18:11:02.111+0000[pid=5582,iso=0][2025/07/22 18:11:59][1753179058111][error] RetryFailedPlugin failed:open c:\\ProgramData\\aliyun\\vminit/INSTANCE_i-uf62k8j9d3ml6vbnx2wy/FAILED_PLUGIN: The system cannot find the file specified.",
        
        # 模板5: 进程退出
        "2025-07-22 18:11:03.222+0000[pid=5583,iso=0][2025/07/22 18:12:00][1753179058222][info] Process [java -jar applicat] exited with code 0",
        "2025-07-22 18:11:04.333+0000[pid=5584,iso=0][2025/07/22 18:12:01][1753179058333][info] Process [python3 script.py] exited with code 1",
        "2025-07-22 18:11:05.444+0000[pid=5585,iso=0][2025/07/22 18:12:02][1753179058444][info] Process [nginx -s reload] exited with code 0",
        
        # 模板6: ACK节点初始化
        "2025-07-22 18:10:59.387+0000[ACK-NODE-INIT] [req_abc123] [i-2ze9c4ik6b5vx3zqy3jq] [elapsed_time:120] [exit_code:0] [succeed to execute]",
        "2025-07-22 18:11:00.498+0000[ACK-NODE-INIT] [req_def456] [i-bp15hdmn8sld7q9xr8kv] [elapsed_time:135] [exit_code:0] [succeed to execute]",
        "2025-07-22 18:11:01.609+0000[ACK-NODE-INIT] [req_ghi789] [i-uf62k8j9d3ml6vbnx2wy] [elapsed_time:98] [exit_code:1] [failed to execute]",
        
        # 一些独特的日志（无模板）
        "2025-07-22 18:11:06.555+0000[pid=5586,iso=0][2025/07/22 18:12:03][1753179058555][warning] System memory usage is high: 89.5%",
        "2025-07-22 18:11:07.666+0000[pid=5587,iso=0][2025/07/22 18:12:04][1753179058666][info] Disk space check completed successfully",
        "2025-07-22 18:11:08.777+0000[pid=5588,iso=0][2025/07/22 18:12:05][1753179058777][debug] Cache cleanup initiated",
    ]
    
    # 生成更多的样本数据，通过添加变化
    expanded_logs = []
    for log in sample_logs:
        expanded_logs.append(log)
        
        # 为每个日志生成1-2个变体
        if "qemu-" in log:
            # 对QEMU日志生成变体
            variant = log.replace("qemu-i-j6c3hy027sbq648valvs", "qemu-eci-random123456")
            expanded_logs.append(variant)
        elif "Connection failed" in log:
            # 对连接失败日志生成变体
            variant = log.replace("*************", "10.10.10.10")
            expanded_logs.append(variant)
        elif "instanceId" in log:
            # 对实例ID日志生成变体
            variant = log.replace("i-2ze9c4ik6b5vx3zqy3jq", "i-newinstance123456")
            expanded_logs.append(variant)
    
    return expanded_logs


def create_test_dataset():
    """创建测试数据集"""
    
    # 创建数据目录
    BASE_PATH = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    data_dir = Path(BASE_PATH) / "data" / "control_log" / "processed_data"
    data_dir.mkdir(parents=True, exist_ok=True)
    
    # 生成示例日志数据
    sample_logs = create_sample_log_data()
    
    # 创建DataFrame
    log_data = []
    for i, content in enumerate(sample_logs):
        log_data.append({
            'content': content,
            'log_time': 1753179057000 + i * 1000,  # 递增的时间戳
            'label': np.random.choice([0, 1], p=[0.7, 0.3]),  # 70%为0，30%为1
            'timestamp': int(1753179057000 + i * 1000),
        })
    
    df = pd.DataFrame(log_data)
    
    # 保存为parquet和csv格式
    parquet_file = data_dir / "console_log.parquet"
    csv_file = data_dir / "console_log.csv"
    
    df.to_parquet(parquet_file, index=False, engine='pyarrow')
    df.to_csv(csv_file, index=False, encoding='utf-8')
    
    print(f"创建测试数据集成功！")
    print(f"- 数据目录: {data_dir}")
    print(f"- 日志数量: {len(df)}")
    print(f"- Parquet文件: {parquet_file}")
    print(f"- CSV文件: {csv_file}")
    
    # 显示数据预览
    print(f"\n数据预览:")
    print(df.head())
    
    return str(data_dir)


if __name__ == "__main__":
    create_test_dataset()