#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志分析和模板提取工具
按照 logstore/action_type 分类组织，分析日志共性，提取日志模板
"""

import json
import os
import re
import glob
from collections import defaultdict, Counter
from typing import Dict, List, Set, Tuple
import pandas as pd
from datetime import datetime
import difflib
import hashlib


class LogAnalyzer:
    def __init__(self, logs_output_dir: str):
        self.logs_output_dir = logs_output_dir
        self.log_data = []
        self.logstore_action_stats = defaultdict(lambda: defaultdict(int))
        self.logstore_action_samples = defaultdict(lambda: defaultdict(list))
        self.log_templates = defaultdict(set)
        self.structured_logs = []

    def load_logs(self):
        """加载所有 JSONL 文件"""
        print("开始加载日志文件...")
        jsonl_files = glob.glob(os.path.join(self.logs_output_dir, "all_results_*.jsonl"))

        total_records = 0
        for file_path in jsonl_files:
            print(f"正在处理文件: {os.path.basename(file_path)}")
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    for line_num, line in enumerate(f, 1):
                        try:
                            if line.strip():
                                data = json.loads(line.strip())
                                self.log_data.append(data)
                                total_records += 1
                        except json.JSONDecodeError as e:
                            print(f"JSON解析错误 {file_path}:{line_num}: {e}")
                            continue
            except Exception as e:
                print(f"文件读取错误 {file_path}: {e}")

        print(f"总计加载了 {total_records} 条记录")

    def analyze_logstore_action_distribution(self):
        """分析 logstore/action_type 分布"""
        print("\n开始分析 logstore/action_type 分布...")

        for record in self.log_data:
            for request_id, request_data in record.items():
                action_type = request_data.get("action_type", "unknown")

                # 分析每个 logstore 的日志
                logstore_data = request_data.get("logstore", {})
                for logstore_name, logs in logstore_data.items():
                    if isinstance(logs, list) and logs:
                        self.logstore_action_stats[logstore_name][action_type] += len(logs)

                        # 保存样本数据用于分析
                        if len(self.logstore_action_samples[logstore_name][action_type]) < 10:
                            self.logstore_action_samples[logstore_name][action_type].extend(logs[:5])

        # 输出统计结果
        print("\n=== LOGSTORE/ACTION_TYPE 分布统计 ===")
        for logstore_name in sorted(self.logstore_action_stats.keys()):
            print(f"\n📊 {logstore_name}:")
            action_stats = self.logstore_action_stats[logstore_name]
            total_logs = sum(action_stats.values())

            for action_type in sorted(action_stats.keys()):
                count = action_stats[action_type]
                percentage = (count / total_logs) * 100
                print(f"  - {action_type}: {count:,} 条日志 ({percentage:.1f}%)")

    def extract_log_content_patterns(self):
        """提取日志内容模式和模板"""
        print("\n开始提取日志模式和模板...")

        for logstore_name in self.logstore_action_samples:
            print(f"\n🔍 分析 {logstore_name} 的日志模式:")

            for action_type in self.logstore_action_samples[logstore_name]:
                logs = self.logstore_action_samples[logstore_name][action_type]

                # 提取日志内容
                log_contents = []
                for log in logs:
                    if isinstance(log, dict) and "log" in log:
                        content = log["log"].get("content", "")
                        if content:
                            log_contents.append(content)

                if log_contents:
                    print(f"  📝 {action_type} ({len(log_contents)} 个样本):")

                    # 生成日志模板
                    templates = self._generate_log_templates(log_contents)
                    for template in templates[:5]:  # 显示前5个模板
                        print(f"    模板: {template}")

                    self.log_templates[f"{logstore_name}_{action_type}"] = templates

    def _generate_log_templates(self, log_contents: List[str]) -> List[str]:
        """生成日志模板"""
        if not log_contents:
            return []

        templates = []

        # 方法1: 基于正则表达式的参数化
        for content in log_contents[:3]:  # 取前3个作为模板基础
            template = self._parameterize_log(content)
            if template not in templates:
                templates.append(template)

        # 方法2: 基于相似度聚类的模板提取
        if len(log_contents) > 1:
            clustered_templates = self._cluster_based_templates(log_contents)
            templates.extend(clustered_templates)

        return list(set(templates))  # 去重

    def _parameterize_log(self, log_content: str) -> str:
        """将日志内容参数化，提取模板"""
        template = log_content

        # 替换常见的变量模式
        patterns = [
            (r"\b\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\b", "<TIMESTAMP>"),  # 时间戳
            (r"\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b", "<IP_ADDRESS>"),  # IP地址
            (r"\b[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}\b", "<UUID>"),  # UUID
            (r"\beci-[a-zA-Z0-9]+\b", "<INSTANCE_ID>"),  # 实例ID
            (r"\beni-[a-zA-Z0-9]+\b", "<ENI_ID>"),  # ENI ID
            (r"\b\d+ms\b", "<DURATION_MS>"),  # 毫秒时间
            (r"\b\d+ns\b", "<DURATION_NS>"),  # 纳秒时间
            (r"\bcost\s*[:=]\s*\d+", "cost: <NUMBER>"),  # 耗时
            (r"\b\d+\b", "<NUMBER>"),  # 数字
            (r"\b0x[a-fA-F0-9]+\b", "<HEX_NUMBER>"),  # 十六进制数
        ]

        for pattern, replacement in patterns:
            template = re.sub(pattern, replacement, template)

        return template

    def _cluster_based_templates(self, log_contents: List[str]) -> List[str]:
        """基于相似度聚类生成模板"""
        if len(log_contents) < 2:
            return []

        # 计算日志内容的相似度，找出共同模式
        templates = []

        # 简单的方法：找出最长公共子序列作为模板
        for i in range(len(log_contents)):
            for j in range(i + 1, len(log_contents)):
                common_parts = self._find_common_structure(log_contents[i], log_contents[j])
                if common_parts and len(common_parts) > 20:  # 至少20个字符
                    templates.append(common_parts)

        return templates[:3]  # 返回前3个

    def _find_common_structure(self, str1: str, str2: str) -> str:
        """找出两个字符串的公共结构"""
        # 使用 difflib 找出相似部分
        matcher = difflib.SequenceMatcher(None, str1, str2)
        matching_blocks = matcher.get_matching_blocks()

        common_parts = []
        for match in matching_blocks:
            if match.size > 5:  # 至少5个字符的匹配
                common_parts.append(str1[match.a : match.a + match.size])

        return " [...] ".join(common_parts)

    def structure_logs(self):
        """将原始日志结构化"""
        print("\n开始结构化日志...")

        structured_count = 0
        for record in self.log_data[:100]:  # 处理前100条作为示例
            for request_id, request_data in record.items():
                action_type = request_data.get("action_type", "unknown")

                logstore_data = request_data.get("logstore", {})
                for logstore_name, logs in logstore_data.items():
                    if isinstance(logs, list):
                        for log_entry in logs:
                            if isinstance(log_entry, dict) and "log" in log_entry:
                                structured_log = self._structure_single_log(log_entry, logstore_name, action_type, request_id)
                                if structured_log:
                                    self.structured_logs.append(structured_log)
                                    structured_count += 1

        print(f"结构化了 {structured_count} 条日志")

    def _structure_single_log(self, log_entry: dict, logstore_name: str, action_type: str, request_id: str) -> dict:
        """结构化单条日志"""
        log_content = log_entry.get("log", {})

        structured = {
            "request_id": request_id,
            "logstore": logstore_name,
            "action_type": action_type,
            "timestamp": log_content.get("log_time"),
            "level": log_content.get("level", "INFO"),
            "source": log_content.get("source"),
            "original_content": log_content.get("content", ""),
            "instance_id": log_entry.get("instance_id"),
            "nc_ip": log_entry.get("nc_ip"),
            "action_time": log_entry.get("action_time"),
        }

        # 提取结构化字段
        content = log_content.get("content", "")
        structured.update(self._extract_structured_fields(content))

        return structured

    def _extract_structured_fields(self, content: str) -> dict:
        """从日志内容中提取结构化字段"""
        fields = {}

        # 提取常见字段
        patterns = {
            "method": r"method[=:]\s*(\w+)",
            "vm_name": r"vmName[=:]?\s*([a-zA-Z0-9-]+)",
            "cost_ms": r"cost[=:]?\s*(\d+)(?:ms)?",
            "cost_ns": r"cost[=:]?\s*(\d+)ns",
            "status": r"status[=:]?\s*(\w+)",
            "ip_address": r"\b(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\b",
            "uuid": r"\b([a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12})\b",
            "error_code": r'code[=:]?\s*["\']?(\d+)["\']?',
            "memory": r"memory[=:]?\s*(\d+)",
            "cpu": r"cpu[=:]?\s*(\d+)",
        }

        for field_name, pattern in patterns.items():
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                fields[field_name] = match.group(1)

        # 提取操作类型
        if "start" in content.lower():
            fields["operation"] = "start"
        elif "stop" in content.lower():
            fields["operation"] = "stop"
        elif "query" in content.lower():
            fields["operation"] = "query"
        elif "create" in content.lower():
            fields["operation"] = "create"
        elif "delete" in content.lower():
            fields["operation"] = "delete"

        return fields

    def export_analysis_results(self):
        """导出分析结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 1. 导出统计报告
        report_file = f"log_analysis_report_{timestamp}.md"
        with open(report_file, "w", encoding="utf-8") as f:
            f.write("# 日志分析报告\n\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            f.write("## 1. LOGSTORE/ACTION_TYPE 分布统计\n\n")
            for logstore_name in sorted(self.logstore_action_stats.keys()):
                f.write(f"### {logstore_name}\n\n")
                action_stats = self.logstore_action_stats[logstore_name]
                total_logs = sum(action_stats.values())

                f.write("| Action Type | 日志数量 | 占比 |\n")
                f.write("|-------------|----------|------|\n")

                for action_type in sorted(action_stats.keys()):
                    count = action_stats[action_type]
                    percentage = (count / total_logs) * 100
                    f.write(f"| {action_type} | {count:,} | {percentage:.1f}% |\n")
                f.write("\n")

            f.write("## 2. 日志模板\n\n")
            for template_key, templates in self.log_templates.items():
                f.write(f"### {template_key}\n\n")
                for i, template in enumerate(templates[:5], 1):
                    f.write(f"{i}. `{template}`\n")
                f.write("\n")

        # 2. 导出结构化日志
        if self.structured_logs:
            structured_file = f"structured_logs_{timestamp}.json"
            with open(structured_file, "w", encoding="utf-8") as f:
                json.dump(self.structured_logs, f, ensure_ascii=False, indent=2)

            # 导出为 CSV
            csv_file = f"structured_logs_{timestamp}.csv"
            df = pd.DataFrame(self.structured_logs)
            df.to_csv(csv_file, index=False, encoding="utf-8")

        # 3. 导出模板文件
        templates_file = f"log_templates_{timestamp}.json"
        with open(templates_file, "w", encoding="utf-8") as f:
            templates_dict = {k: list(v) for k, v in self.log_templates.items()}
            json.dump(templates_dict, f, ensure_ascii=False, indent=2)

        print(f"\n✅ 分析结果已导出:")
        print(f"  - 分析报告: {report_file}")
        if self.structured_logs:
            print(f"  - 结构化日志 (JSON): {structured_file}")
            print(f"  - 结构化日志 (CSV): {csv_file}")
        print(f"  - 日志模板: {templates_file}")

    def run_analysis(self):
        """运行完整的分析流程"""
        self.load_logs()
        self.analyze_logstore_action_distribution()
        self.extract_log_content_patterns()
        self.structure_logs()
        self.export_analysis_results()

        print("\n🎉 日志分析完成！")


class LogTemplateExtractor:
    """专门用于日志模板提取的类"""

    def __init__(self):
        self.templates = {}
        self.template_stats = {}

    def extract_templates_by_similarity(self, log_contents: List[str], similarity_threshold: float = 0.8) -> List[str]:
        """基于相似度提取日志模板"""
        if not log_contents:
            return []

        # 预处理日志内容
        processed_logs = [self._preprocess_log(log) for log in log_contents]

        # 聚类相似的日志
        clusters = self._cluster_logs(processed_logs, similarity_threshold)

        # 为每个聚类生成模板
        templates = []
        for cluster in clusters:
            if len(cluster) > 1:  # 只为有多个日志的聚类生成模板
                template = self._generate_cluster_template(cluster)
                if template:
                    templates.append(template)

        return templates

    def _preprocess_log(self, log_content: str) -> str:
        """预处理日志内容"""
        # 移除时间戳、IP、UUID等变化的部分
        processed = log_content

        # 标准化数字
        processed = re.sub(r"\b\d+\b", "<NUM>", processed)

        # 标准化时间戳
        processed = re.sub(r"\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}", "<TIMESTAMP>", processed)

        # 标准化IP地址
        processed = re.sub(r"\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b", "<IP>", processed)

        # 标准化UUID
        processed = re.sub(r"\b[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}\b", "<UUID>", processed)

        return processed

    def _cluster_logs(self, logs: List[str], threshold: float) -> List[List[str]]:
        """聚类相似的日志"""
        clusters = []

        for log in logs:
            added_to_cluster = False

            for cluster in clusters:
                # 计算与聚类中第一个日志的相似度
                similarity = difflib.SequenceMatcher(None, log, cluster[0]).ratio()
                if similarity >= threshold:
                    cluster.append(log)
                    added_to_cluster = True
                    break

            if not added_to_cluster:
                clusters.append([log])

        return clusters

    def _generate_cluster_template(self, cluster: List[str]) -> str:
        """为聚类生成模板"""
        if not cluster:
            return ""

        if len(cluster) == 1:
            return cluster[0]

        # 找出聚类中所有日志的公共模式
        template = cluster[0]

        for log in cluster[1:]:
            template = self._merge_templates(template, log)

        return template

    def _merge_templates(self, template1: str, template2: str) -> str:
        """合并两个模板"""
        # 简单的实现：使用最长公共子序列
        matcher = difflib.SequenceMatcher(None, template1, template2)

        result = []
        for tag, i1, i2, j1, j2 in matcher.get_opcodes():
            if tag == "equal":
                result.append(template1[i1:i2])
            elif tag in ["delete", "insert", "replace"]:
                result.append("<VAR>")

        return "".join(result)


def main():
    """主函数"""
    logs_output_dir = "/Users/<USER>/GitFolders/algorithm/logs_output"

    analyzer = LogAnalyzer(logs_output_dir)
    analyzer.run_analysis()

    print("\n🔍 高级模板提取...")

    # 使用专门的模板提取器
    extractor = LogTemplateExtractor()

    # 示例：为 ecs_regionmaster_info_log 提取更精确的模板
    if analyzer.logstore_action_samples:
        for logstore_name in analyzer.logstore_action_samples:
            for action_type in analyzer.logstore_action_samples[logstore_name]:
                logs = analyzer.logstore_action_samples[logstore_name][action_type]
                log_contents = [log["log"]["content"] for log in logs if isinstance(log, dict) and "log" in log and "content" in log["log"]]

                if log_contents:
                    advanced_templates = extractor.extract_templates_by_similarity(log_contents)
                    if advanced_templates:
                        print(f"\n📋 {logstore_name}_{action_type} 高级模板:")
                        for i, template in enumerate(advanced_templates[:3], 1):
                            print(f"  {i}. {template}")


if __name__ == "__main__":
    main()
