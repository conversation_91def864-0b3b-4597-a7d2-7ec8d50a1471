#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行Drain算法模板提取并将pattern_id写回原始日志文件的脚本
"""

import os
from control_log_diagnose.src.template_extractor import LogTemplateExtractor


def main():
    # 设置数据路径
    BASE_PATH = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    data_path = os.path.join(BASE_PATH, "data/control_log/processed_data")

    # 初始化提取器
    extractor = LogTemplateExtractor()

    print("🚀 开始Drain算法模板提取并保存pattern_id")

    # 可以指定处理特定的logstore
    logstores = ["console_log"]  # 可以根据需要修改

    logstores = None

    # 执行Drain模板提取和ID保存
    stats = extractor.extract_templates_with_drain_and_save_ids(
        data_path=data_path,
        output_dir=None,  # 使用默认输出目录
        logstores=logstores,
        drain_params={"depth": 4, "sim_th": 0.4, "max_children": 100},
        verbose=True,  # 显示详细处理过程
        enable_preprocessing=True,  # 启用日志预处理
        enable_content_replacement=True,  # 启用内容替换
    )

    print(f"\n{'='*60}")
    print("🎯 Drain模板提取和ID保存完成！")

    # 汇总统计结果
    if stats:
        print("\n📊 最终统计:")
        for logstore, stat in stats.items():
            print(f"\n{logstore}:")
            print(f"  总日志数: {stat['total_logs']:,}")
            print(f"  有效日志数: {stat['valid_logs']:,}")
            print(f"  匹配日志数: {stat['matched_logs']:,}")
            print(f"  未匹配日志数: {stat['unmatched_logs']:,}")
            print(f"  覆盖率: {stat['coverage_rate']*100:.1f}%")
    else:
        print("未找到处理结果")


if __name__ == "__main__":
    main()
