#!/usr/bin/env python3
"""
Async Producer-Consumer Log Processor
重新设计的异步日志处理器，使用生产者消费者模型
只保留核心业务逻辑，使用rich进度条
"""

import asyncio
import json
import os
import sys
import pandas as pd
import yaml
import logging
import aiofiles
import signal
import gzip
from datetime import datetime
from typing import Dict, List, Any, Set
from dataclasses import dataclass
from rich.progress import Progress, TaskID
import re
import time
from pathlib import Path

try:
    import pyarrow as pa
    import pyarrow.parquet as pq

    PARQUET_AVAILABLE = True
except ImportError:
    PARQUET_AVAILABLE = False

try:
    import msgpack

    MSGPACK_AVAILABLE = True
except ImportError:
    MSGPACK_AVAILABLE = False

# 添加项目根目录到路径
BASE_PATH = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if BASE_PATH not in sys.path:
    sys.path.append(BASE_PATH)

from connector.sls import get_related_logs


@dataclass
class LogRecord:
    """日志记录数据类"""

    request_id: str
    instance_id: str
    region_name: str
    action_time: str
    nc_ip: str
    action_type: str
    start_time: float
    end_time: float

    def to_dict(self) -> Dict[str, Any]:
        """将LogRecord转换为字典"""
        return {
            "request_id": self.request_id,
            "instance_id": self.instance_id,
            "region_name": self.region_name,
            "action_time": self.action_time,
            "nc_ip": self.nc_ip,
            "action_type": self.action_type,
            "start_time": self.start_time,
            "end_time": self.end_time,
        }


class LogFieldExtractor:
    """日志字段提取器"""

    def __init__(self, extraction_config: Dict[str, Any]):
        self.extraction_config = extraction_config
        self.logstore_configs = extraction_config.get("logstore_configs", {})
        self.default_config = extraction_config.get("default_config", {})

    def extract_fields(self, log_entry: Any, logstore_name: str, record: LogRecord) -> Dict[str, Any]:
        """提取日志字段"""
        log_info = {}
        config = self.logstore_configs.get(logstore_name, self.default_config)

        # 提取内容字段
        content_field = config.get("content_field")
        if content_field and hasattr(log_entry, "contents") and content_field in log_entry.contents:
            log_info["content"] = log_entry.contents[content_field]
        else:
            content_fields = self.default_config.get("content_fields", ["content", "message", "detail"])
            for field in content_fields:
                if hasattr(log_entry, "contents") and field in log_entry.contents:
                    log_info["content"] = log_entry.contents[field]
                    break

        # 提取时间和源字段
        log_info["__time__"] = getattr(log_entry, "timestamp", "")
        log_info["__source__"] = getattr(log_entry, "source", "")

        # 提取额外字段
        # additional_fields = config.get("additional_fields", [])
        additional_fields = list(log_entry.contents.keys())

        for field in additional_fields:
            if hasattr(log_entry, "contents") and field in log_entry.contents:
                log_info[field] = log_entry.contents.get(field)

        return log_info


class ProjectMapper:
    """项目区域映射器"""

    def __init__(self, config: Dict[str, Any]):
        self.region_project_mapping = config.get("region_project_mapping", {})
        with open(os.path.join(BASE_PATH, "control_log_diagnose", "sls_region_conf.json"), "r") as f:
            self.region_endpoint_mapping = json.load(f)

    def get_project_by_region(self, project: str, region_name: str) -> str:
        """根据区域获取项目名"""
        if region_name in self.region_project_mapping and project in self.region_project_mapping[region_name]:
            return self.region_project_mapping[region_name][project]
        return project

    def get_endpoint_by_region(self, region_name: str) -> str:
        """根据区域获取端点"""
        return self.region_endpoint_mapping.get(region_name, None)


class LogQueryGenerator:
    """日志查询生成器"""

    @staticmethod
    def generate_logstore_query(item: Dict[str, Any], record: LogRecord) -> str:
        """生成logstore查询语句"""
        query_str = "*"
        ipv4_pattern = r"^(\d{1,3}\.){3}\d{1,3}$"
        key_fields = item.get("key_fields", [])

        if item.get("request_id", "none") == "none":
            if re.match(ipv4_pattern, record.nc_ip):
                query_str += f" AND __source__: {record.nc_ip}"
            query_str += (
                f" | set session parallel_sql=true; select * where {key_fields[0]} LIKE '%{record.instance_id}%' "
                f"OR {key_fields[0]} LIKE '%{record.request_id}%' limit 10000"
            )
        else:
            if item["logstore"] == "schedule_trace":
                query_str += f" and requestId: '{record.request_id}'"
            else:
                query_str += f" and request_id: '{record.request_id}'"

        return query_str

    @staticmethod
    def generate_logcluster_query(item: str, record: LogRecord) -> str:
        """生成logcluster查询语句"""
        query = "*"
        if item.startswith("ecs_region_master"):
            query += f" | set session parallel_sql=true; select * where content LIKE '%{record.instance_id}%' limit 10000"
        else:
            ipv4_pattern = r"^(\d{1,3}\.){3}\d{1,3}$"
            if re.match(ipv4_pattern, record.nc_ip):
                query += (
                    f" AND nc: {record.nc_ip} | set session parallel_sql=true; select * where "
                    f"content LIKE '%{record.request_id}%' OR content LIKE '%{record.instance_id}%' limit 10000"
                )
            else:
                query += (
                    f" | set session parallel_sql=true; select * where "
                    f"content LIKE '%{record.request_id}%' OR content LIKE '%{record.instance_id}%' limit 10000"
                )
        return query


class AsyncLogRetriever:
    """异步日志获取器"""

    def __init__(self, project_mapper: ProjectMapper):
        self.project_mapper = project_mapper

    async def retrieve_logstore_logs(self, item: Dict[str, Any], record: LogRecord) -> tuple:
        """异步获取logstore日志"""
        try:
            query_str = LogQueryGenerator.generate_logstore_query(item, record)

            # 处理区域映射
            region_parts = record.region_name.split("-")
            project = item["project"]
            mapped_project = None
            mapped_region = None

            # 尝试不同的区域组合
            for i in range(2, min(5, len(region_parts) + 1)):
                region_key = "-".join(region_parts[:i])
                temp_mapped_project = self.project_mapper.get_project_by_region(item["project"], region_key)
                if temp_mapped_project != item["project"]:
                    mapped_project = temp_mapped_project
                    mapped_region = region_key
                    break
            else:
                temp_mapped_project = self.project_mapper.get_project_by_region(item["project"], record.region_name)
                if temp_mapped_project != item["project"]:
                    mapped_project = temp_mapped_project
                    mapped_region = record.region_name

            # 在执行器中运行同步的日志获取函数
            loop = asyncio.get_event_loop()

            # 获取原始项目日志
            logs = await loop.run_in_executor(
                None,
                lambda: get_related_logs(
                    item["project"],
                    item["logstore"],
                    record.start_time,
                    record.end_time,
                    query_str=query_str,
                    endpoint=self.project_mapper.get_endpoint_by_region(record.region_name),
                    max_retries=5,
                    retry_delay=2,
                ),
            )

            # 如果有映射项目，也获取其日志
            if mapped_project and mapped_project != item["project"]:
                mapped_endpoint = (
                    self.project_mapper.get_endpoint_by_region(mapped_region)
                    if mapped_region
                    else self.project_mapper.get_endpoint_by_region(record.region_name)
                )
                mapped_logs = await loop.run_in_executor(
                    None,
                    lambda: get_related_logs(
                        mapped_project,
                        item["logstore"],
                        record.start_time,
                        record.end_time,
                        query_str=query_str,
                        endpoint=mapped_endpoint,
                        max_retries=5,
                        retry_delay=2,
                    ),
                )
                logs.extend(mapped_logs)

            project = mapped_project if mapped_project else item["project"]
            return logs, project

        except Exception:
            return [], item["project"]

    async def retrieve_logcluster_logs(self, item: str, value: str, record: LogRecord) -> List[Any]:
        """异步获取logcluster日志"""
        try:
            query = LogQueryGenerator.generate_logcluster_query(item, record)

            loop = asyncio.get_event_loop()
            logs = await loop.run_in_executor(
                None,
                lambda: get_related_logs(
                    "log-cluster",
                    value,
                    record.start_time,
                    record.end_time,
                    query_str=query,
                    max_retries=5,
                    retry_delay=2,
                ),
            )
            return logs

        except Exception:
            return []


class AsyncLogProcessor:
    """异步生产者消费者日志处理器"""

    def __init__(
        self,
        config: Dict[str, Any],
        test_mode: bool = False,
        max_workers: int = 10,
        batch_size: int = 100,
        max_file_size: int = 100 * 1024 * 1024,
        storage_format: str = "jsonl",
    ):
        self.config = config
        self.test_mode = test_mode
        self.max_workers = max_workers
        self.batch_size = batch_size  # 批量保存大小
        self.max_file_size = max_file_size  # 单个文件最大大小 (100MB)
        self.storage_format = storage_format  # 存储格式: jsonl, jsonl_gz, msgpack, parquet

        # 初始化组件
        self.project_mapper = ProjectMapper(config)
        self.log_retriever = AsyncLogRetriever(self.project_mapper)

        # 加载字段提取配置
        extraction_config_path = os.path.join(BASE_PATH, "control_log_diagnose", "config", "log_extraction_config.yaml")
        if os.path.exists(extraction_config_path):
            with open(extraction_config_path, "r", encoding="utf-8") as f:
                self.extraction_config = yaml.safe_load(f)
        else:
            self.extraction_config = {}

        self.field_extractor = LogFieldExtractor(self.extraction_config)

        # 队列和信号量
        self.input_queue = asyncio.Queue(maxsize=max_workers * 2)
        self.output_queue = asyncio.Queue(maxsize=batch_size * 2)  # 增大输出队列
        self.processing_semaphore = asyncio.Semaphore(max_workers)

        # 输出目录和文件管理
        self.output_dir = Path(BASE_PATH) / "logs_output"
        self.output_dir.mkdir(exist_ok=True)

        # 文件管理
        self.current_file_index = 0
        self.current_file_size = 0
        self.batch_buffer = []

        # checkpoint管理
        self.checkpoint_file = self.output_dir / "checkpoint.json"
        self.processed_request_ids: Set[str] = set()
        self.last_checkpoint_time = time.time()
        self.checkpoint_interval = 30  # 30秒保存一次checkpoint

        # 统计信息
        self.stats = {
            "processed": 0,
            "saved_files": 0,
            "skipped": 0,
            "logstore_with_logs": 0,
            "logstore_without_logs": 0,
            "logcluster_with_logs": 0,
            "logcluster_without_logs": 0,
        }

        # 优雅关闭控制
        self._shutdown_event = asyncio.Event()
        self._tasks = []

        # 加载已有的checkpoint
        self._load_checkpoint()

    def _load_checkpoint(self):
        """加载checkpoint文件"""
        if self.checkpoint_file.exists():
            try:
                with open(self.checkpoint_file, "r", encoding="utf-8") as f:
                    checkpoint_data = json.load(f)
                    self.processed_request_ids = set(checkpoint_data.get("processed_request_ids", []))
                    self.current_file_index = checkpoint_data.get("current_file_index", 0)
                    self.stats.update(checkpoint_data.get("stats", {}))
                    logging.info(f"Loaded checkpoint: {len(self.processed_request_ids)} processed records")
            except Exception as e:
                logging.warning(f"Failed to load checkpoint: {e}")
                self.processed_request_ids = set()

    async def _save_checkpoint(self):
        """保存checkpoint"""
        try:
            checkpoint_data = {
                "processed_request_ids": list(self.processed_request_ids),
                "current_file_index": self.current_file_index,
                "stats": self.stats,
                "timestamp": datetime.now().isoformat(),
            }

            # 使用临时文件确保原子性写入
            temp_file = self.checkpoint_file.with_suffix(".tmp")
            async with aiofiles.open(temp_file, "w", encoding="utf-8") as f:
                await f.write(json.dumps(checkpoint_data, ensure_ascii=False, indent=2))

            # 原子性替换
            temp_file.replace(self.checkpoint_file)
            self.last_checkpoint_time = time.time()

        except Exception as e:
            logging.error(f"Failed to save checkpoint: {e}")

    async def producer(self, records: pd.DataFrame, progress: Progress, task_id: TaskID):
        """生产者：将记录放入队列"""
        _ = progress, task_id  # 未使用的参数

        try:
            for idx, (_, row) in enumerate(records.iterrows()):
                # 检查是否需要关闭
                if self._shutdown_event.is_set():
                    logging.info("Producer received shutdown signal")
                    break

                if self.test_mode and idx >= 10:
                    break

                request_id = row["request_id"]

                # 跳过已处理的记录
                if request_id in self.processed_request_ids:
                    self.stats["skipped"] += 1
                    continue

                # 创建LogRecord对象
                gmt_create = row["gmt_create"]
                start_time = datetime.strptime(gmt_create, "%Y-%m-%d %H:%M:%S").timestamp() - 60 * 5
                end_time = start_time + 60 * 10

                log_record = LogRecord(
                    request_id=request_id,
                    instance_id=row["instance_id"],
                    region_name=row["region_name"],
                    action_time=row["gmt_create"],
                    nc_ip=row["nc_ip"],
                    action_type=row["action_type"],
                    start_time=start_time,
                    end_time=end_time,
                )

                await self.input_queue.put(log_record)

        except Exception as e:
            logging.error(f"Producer error: {e}")
        finally:
            # 发送结束信号
            for _ in range(self.max_workers):
                await self.input_queue.put(None)

    async def consumer(self, consumer_id: int):
        """消费者：处理记录"""
        try:
            while not self._shutdown_event.is_set():
                try:
                    record = await asyncio.wait_for(self.input_queue.get(), timeout=1.0)
                    if record is None:
                        break

                    async with self.processing_semaphore:
                        try:
                            result = await self.process_single_record(record)
                            await self.output_queue.put(result)
                        except Exception as e:
                            logging.error(f"Consumer {consumer_id} error processing {record.request_id}: {e}")
                            await self.output_queue.put({"request_id": record.request_id, "error": str(e)})
                        finally:
                            self.input_queue.task_done()

                except asyncio.TimeoutError:
                    continue
                except asyncio.CancelledError:
                    logging.info(f"Consumer {consumer_id} cancelled")
                    break

        except Exception as e:
            logging.error(f"Consumer {consumer_id} fatal error: {e}")
        finally:
            logging.info(f"Consumer {consumer_id} shutdown")

    async def process_single_record(self, record: LogRecord) -> Dict[str, Any]:
        """处理单条记录"""
        result = {"record": record.to_dict(), "logstore": {}, "log_cluster": {}, "action_type": record.action_type}

        # 处理logstore日志
        logstore_tasks = []
        for item in self.config.get("logstores", []):
            task = self.process_logstore_item(item, record)
            logstore_tasks.append(task)

        if logstore_tasks:
            logstore_results = await asyncio.gather(*logstore_tasks, return_exceptions=True)
            for item, logstore_result in zip(self.config.get("logstores", []), logstore_results):
                if isinstance(logstore_result, Exception):
                    logging.error(f"Logstore error for {record.request_id}, {item['logstore']}: {logstore_result}")
                    result["logstore"][item["logstore"]] = []
                else:
                    result["logstore"][item["logstore"]] = logstore_result
                    if logstore_result:
                        self.stats["logstore_with_logs"] += 1
                    else:
                        self.stats["logstore_without_logs"] += 1

        # 处理logcluster日志
        logcluster_tasks = []
        for item, value in self.config.get("log_clusters", {}).items():
            task = self.process_logcluster_item(item, value, record)
            logcluster_tasks.append(task)

        if logcluster_tasks:
            logcluster_results = await asyncio.gather(*logcluster_tasks, return_exceptions=True)
            for (item, value), logcluster_result in zip(self.config.get("log_clusters", {}).items(), logcluster_results):
                if isinstance(logcluster_result, Exception):
                    logging.error(f"Logcluster error for {record.request_id}, {item}: {logcluster_result}")
                    result["log_cluster"][item] = []
                else:
                    result["log_cluster"][item] = logcluster_result
                    if logcluster_result:
                        self.stats["logcluster_with_logs"] += 1
                    else:
                        self.stats["logcluster_without_logs"] += 1

        return result

    async def process_logstore_item(self, item: Dict[str, Any], record: LogRecord) -> List[Dict[str, Any]]:
        """处理单个logstore项"""
        logs, _ = await self.log_retriever.retrieve_logstore_logs(item, record)

        extracted_logs = []
        if logs:
            for log_entry in logs:
                extracted_log = self.field_extractor.extract_fields(log_entry, item["logstore"], record)
                extracted_logs.append(extracted_log)

        return extracted_logs

    async def process_logcluster_item(self, item: str, value: str, record: LogRecord) -> List[Dict[str, Any]]:
        """处理单个logcluster项"""
        logs = await self.log_retriever.retrieve_logcluster_logs(item, value, record)

        extracted_logs = []
        if logs:
            for log_entry in logs:
                extracted_log = self.field_extractor.extract_fields(log_entry, value, record)
                extracted_logs.append(extracted_log)

        return extracted_logs

    async def _get_current_output_file(self) -> Path:
        """获取当前输出文件路径"""
        extensions = {"jsonl": ".jsonl", "jsonl_gz": ".jsonl.gz", "msgpack": ".msgpack", "parquet": ".parquet"}
        ext = extensions.get(self.storage_format, ".jsonl")
        return self.output_dir / f"results_{self.current_file_index:04d}{ext}"

    async def _save_batch_to_file(self, batch_data: List[Dict[str, Any]]) -> None:
        """批量保存数据到文件"""
        if not batch_data:
            return

        current_file = await self._get_current_output_file()

        # 检查文件大小，如果过大则创建新文件
        if current_file.exists() and current_file.stat().st_size > self.max_file_size:
            self.current_file_index += 1
            current_file = await self._get_current_output_file()
            self.current_file_size = 0

        try:
            if self.storage_format == "jsonl":
                await self._save_jsonl(current_file, batch_data)
            elif self.storage_format == "jsonl_gz":
                await self._save_jsonl_gz(current_file, batch_data)
            elif self.storage_format == "msgpack" and MSGPACK_AVAILABLE:
                await self._save_msgpack(current_file, batch_data)
            elif self.storage_format == "parquet" and PARQUET_AVAILABLE:
                await self._save_parquet(current_file, batch_data)
            else:
                # 回退到默认格式
                await self._save_jsonl(current_file, batch_data)

            self.stats["saved_files"] = self.current_file_index + 1
            logging.info(f"Saved batch of {len(batch_data)} records to {current_file.name} ({self.storage_format})")

        except Exception as e:
            logging.error(f"Failed to save batch to {current_file}: {e}")
            raise

    async def _save_jsonl(self, file_path: Path, batch_data: List[Dict[str, Any]]) -> None:
        """保存为JSONL格式"""
        async with aiofiles.open(file_path, "a", encoding="utf-8") as f:
            for result in batch_data:
                json_line = json.dumps(result, ensure_ascii=False)
                await f.write(json_line + "\n")
                self.current_file_size += len(json_line.encode("utf-8")) + 1
            await f.flush()

    async def _save_jsonl_gz(self, file_path: Path, batch_data: List[Dict[str, Any]]) -> None:
        """保存为压缩JSONL格式"""
        # 先写入内存，然后压缩
        content = []
        for result in batch_data:
            json_line = json.dumps(result, ensure_ascii=False)
            content.append(json_line)

        content_str = "\n".join(content) + "\n"
        compressed_data = gzip.compress(content_str.encode("utf-8"))

        # 追加模式写入压缩数据
        async with aiofiles.open(file_path, "ab") as f:
            await f.write(compressed_data)
            self.current_file_size += len(compressed_data)

    async def _save_msgpack(self, file_path: Path, batch_data: List[Dict[str, Any]]) -> None:
        """保存为MessagePack格式"""
        # MessagePack不支持追加，所以需要读取现有数据
        existing_data = []
        if file_path.exists():
            async with aiofiles.open(file_path, "rb") as f:
                content = await f.read()
                if content:
                    existing_data = msgpack.unpackb(content, raw=False)

        # 合并数据
        all_data = existing_data + batch_data

        # 写入所有数据
        packed_data = msgpack.packb(all_data, use_bin_type=True)
        async with aiofiles.open(file_path, "wb") as f:
            await f.write(packed_data)
            self.current_file_size = len(packed_data)

    async def _save_parquet(self, file_path: Path, batch_data: List[Dict[str, Any]]) -> None:
        """保存为Parquet格式"""
        # 转换为DataFrame
        df = pd.DataFrame(batch_data)

        # 处理嵌套字典列
        for col in df.columns:
            if df[col].dtype == "object":
                # 将嵌套对象转换为JSON字符串
                df[col] = df[col].apply(lambda x: json.dumps(x, ensure_ascii=False) if isinstance(x, (dict, list)) else x)

        # 如果文件已存在，追加数据
        if file_path.exists():
            existing_df = pd.read_parquet(file_path)
            df = pd.concat([existing_df, df], ignore_index=True)

        # 写入Parquet文件
        df.to_parquet(file_path, compression="snappy", index=False)
        self.current_file_size = file_path.stat().st_size

    async def result_writer(self, total_records: int, progress: Progress, task_id: TaskID):
        """结果写入器 - 批量保存和checkpoint管理"""
        processed_count = 0
        expected_records = total_records - len(self.processed_request_ids)

        try:
            while processed_count < expected_records and not self._shutdown_event.is_set():
                try:
                    # 批量收集结果
                    batch_timeout = 5.0  # 5秒超时
                    batch_start_time = time.time()

                    while (
                        len(self.batch_buffer) < self.batch_size
                        and time.time() - batch_start_time < batch_timeout
                        and processed_count < expected_records
                        and not self._shutdown_event.is_set()
                    ):

                        try:
                            result = await asyncio.wait_for(self.output_queue.get(), timeout=1.0)
                            self.batch_buffer.append(result)
                            self.processed_request_ids.add(result["record"]["request_id"])

                            processed_count += 1
                            self.stats["processed"] = len(self.processed_request_ids)

                            # 更新进度条
                            progress.update(task_id, advance=1, description=f"[green]Processing records ({len(self.processed_request_ids)}/{total_records})")

                            self.output_queue.task_done()

                        except asyncio.TimeoutError:
                            break

                    # 保存批次数据
                    if self.batch_buffer:
                        await self._save_batch_to_file(self.batch_buffer)
                        self.batch_buffer.clear()

                    # 定期保存checkpoint
                    if time.time() - self.last_checkpoint_time > self.checkpoint_interval:
                        await self._save_checkpoint()

                except Exception as e:
                    logging.error(f"Result writer error: {e}")
                    continue

        except asyncio.CancelledError:
            logging.info("Result writer cancelled")
        except Exception as e:
            logging.error(f"Result writer fatal error: {e}")
        finally:
            # 保存最后的批次和checkpoint
            if self.batch_buffer:
                try:
                    await self._save_batch_to_file(self.batch_buffer)
                    self.batch_buffer.clear()
                except Exception as e:
                    logging.error(f"Failed to save final batch: {e}")

            try:
                await self._save_checkpoint()
            except Exception as e:
                logging.error(f"Failed to save final checkpoint: {e}")

            logging.info(f"Result writer completed. Total files: {self.stats['saved_files']}, Total processed: {self.stats['processed']}")

    async def merge_files(self, delete_source: bool = False) -> Path:
        """合并所有小文件为一个大文件"""
        merged_file = self.output_dir / "merged_results.jsonl"

        try:
            async with aiofiles.open(merged_file, "w", encoding="utf-8") as outfile:
                for i in range(self.current_file_index + 1):
                    small_file = self.output_dir / f"results_{i:04d}.jsonl"
                    if small_file.exists():
                        async with aiofiles.open(small_file, "r", encoding="utf-8") as infile:
                            async for line in infile:
                                await outfile.write(line)

                        if delete_source:
                            small_file.unlink()

            logging.info(f"Merged {self.current_file_index + 1} files into {merged_file}")
            return merged_file

        except Exception as e:
            logging.error(f"Failed to merge files: {e}")
            raise

    def get_output_files(self) -> List[Path]:
        """获取所有输出文件列表"""
        files = []
        for i in range(self.current_file_index + 1):
            file_path = self.output_dir / f"results_{i:04d}.jsonl"
            if file_path.exists():
                files.append(file_path)
        return files

    async def shutdown(self):
        """优雅关闭处理器"""
        logging.info("Initiating graceful shutdown...")
        self._shutdown_event.set()

        # 等待所有任务完成
        if self._tasks:
            try:
                # 给任务一些时间自然完成
                await asyncio.wait_for(asyncio.gather(*self._tasks, return_exceptions=True), timeout=30.0)
            except asyncio.TimeoutError:
                logging.warning("Some tasks didn't complete within timeout, cancelling...")
                for task in self._tasks:
                    if not task.done():
                        task.cancel()

                # 等待取消完成
                await asyncio.gather(*self._tasks, return_exceptions=True)

        # 最终保存
        try:
            if self.batch_buffer:
                await self._save_batch_to_file(self.batch_buffer)
                self.batch_buffer.clear()
            await self._save_checkpoint()
            logging.info("Final save completed during shutdown")
        except Exception as e:
            logging.error(f"Error during final save: {e}")

        logging.info("Graceful shutdown completed")

    async def run(self, records: pd.DataFrame, merge_files: bool = False):
        """运行处理器"""
        total_records = len(records) if not self.test_mode else min(10, len(records))
        skipped_count = len(self.processed_request_ids)

        logging.info(f"Starting processing: {total_records} total records, {skipped_count} already processed")

        progress = None
        producer_task = None
        consumer_tasks = []
        writer_task = None

        try:
            progress = Progress()
            progress.start()
            task_id = progress.add_task(f"[green]Processing records ({skipped_count}/{total_records})", total=total_records)

            # 更新进度条到已处理的数量
            progress.update(task_id, completed=skipped_count)

            # 启动生产者
            producer_task = asyncio.create_task(self.producer(records, progress, task_id))
            self._tasks.append(producer_task)

            # 启动消费者
            consumer_tasks = [asyncio.create_task(self.consumer(i)) for i in range(self.max_workers)]
            self._tasks.extend(consumer_tasks)

            # 启动结果写入器
            writer_task = asyncio.create_task(self.result_writer(total_records, progress, task_id))
            self._tasks.append(writer_task)

            try:
                # 等待生产者完成或关闭信号
                shutdown_wait = asyncio.create_task(self._shutdown_event.wait())

                _, pending = await asyncio.wait([producer_task, shutdown_wait], return_when=asyncio.FIRST_COMPLETED)

                # 取消未完成的等待任务
                for task in pending:
                    task.cancel()

                if self._shutdown_event.is_set():
                    logging.info("Shutdown signal received during processing")
                else:
                    logging.info("Producer completed normally")
                    # 等待一小段时间让消费者处理完成
                    await asyncio.sleep(2.0)

                    # 检查队列是否为空，而不是使用join()
                    queue_empty = self.input_queue.empty()
                    if not queue_empty:
                        logging.warning(f"Queue not empty: {self.input_queue.qsize()} items remaining")

                # 取消所有任务
                for task in consumer_tasks:
                    if not task.done():
                        task.cancel()

                if writer_task and not writer_task.done():
                    writer_task.cancel()

                # 等待任务完成
                all_tasks = [t for t in consumer_tasks + [writer_task] if t and not t.done()]
                if all_tasks:
                    try:
                        await asyncio.wait_for(asyncio.gather(*all_tasks, return_exceptions=True), timeout=10.0)
                    except asyncio.TimeoutError:
                        logging.warning("Task cleanup timeout")

            except asyncio.CancelledError:
                logging.info("Run cancelled")
                raise
            except Exception as e:
                logging.error(f"Error during processing: {e}")
                raise

            # 合并文件（可选）
            if merge_files and not self._shutdown_event.is_set():
                try:
                    merged_file = await self.merge_files(delete_source=True)
                    logging.info(f"Results merged into: {merged_file}")
                except Exception as e:
                    logging.error(f"Failed to merge files: {e}")

        except Exception as e:
            logging.error(f"Fatal error in run: {e}")
            raise
        finally:
            # 强制停止进度条
            if progress:
                try:
                    progress.stop()
                except Exception:
                    pass

            # 确保最终状态保存
            try:
                if self.batch_buffer:
                    await asyncio.wait_for(self._save_batch_to_file(self.batch_buffer), timeout=3.0)
                    self.batch_buffer.clear()
                await asyncio.wait_for(self._save_checkpoint(), timeout=3.0)
                logging.info("Final save completed")
            except asyncio.TimeoutError:
                logging.warning("Final save timeout")
            except Exception as e:
                logging.error(f"Error in final cleanup: {e}")

        return self.stats


async def main(test_mode: bool = False, max_workers: int = 10, batch_size: int = 100, merge_files: bool = False, storage_format: str = "jsonl"):
    """主函数"""

    os.makedirs(os.path.join(BASE_PATH, "logs_output"), exist_ok=True)
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[logging.StreamHandler(), logging.FileHandler(os.path.join(BASE_PATH, "logs_output", "async_processor.log"), encoding="utf-8")],
    )

    logger = logging.getLogger(__name__)
    logger.info(f"Starting async log processor (test_mode={test_mode}, max_workers={max_workers})")

    # 加载配置
    config_path = os.path.join(BASE_PATH, "control_log_diagnose", "logstore_config.yaml")
    with open(config_path, "r") as f:
        config = yaml.safe_load(f)

    # 加载数据
    data_path = os.path.join(BASE_PATH, "data", "control_log", "start_vm_records", "merged_start_vm_records.csv")
    records = pd.read_csv(data_path)
    records = records[::-1].reset_index(drop=True)  # 反转顺序

    # 加载区域映射
    region_mapping_path = os.path.join(BASE_PATH, "control_log_diagnose", "parsed_region_mapping.json")
    with open(region_mapping_path, "r") as f:
        region_project_mapping = json.load(f)
    config["region_project_mapping"] = region_project_mapping

    # 检查存储格式依赖
    if storage_format == "parquet" and not PARQUET_AVAILABLE:
        logger.warning("PyArrow not available, falling back to jsonl")
        storage_format = "jsonl"
    elif storage_format == "msgpack" and not MSGPACK_AVAILABLE:
        logger.warning("msgpack not available, falling back to jsonl")
        storage_format = "jsonl"

    # 创建处理器
    processor = AsyncLogProcessor(config, test_mode=test_mode, max_workers=max_workers, batch_size=batch_size, storage_format=storage_format)

    # 设置信号处理器
    def signal_handler(signum, _):
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        # 直接设置关闭事件，不创建新任务
        processor._shutdown_event.set()

    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        start_time = datetime.now()
        stats = await processor.run(records, merge_files=merge_files)
        end_time = datetime.now()

        # 打印统计信息
        duration = (end_time - start_time).total_seconds()
        logger.info(f"Processing completed in {duration:.2f} seconds")
        logger.info(f"Processed: {stats['processed']} records")
        logger.info(f"Skipped: {stats['skipped']} records (already processed)")
        logger.info(f"Saved files: {stats['saved_files']}")
        logger.info(f"Logstore - With logs: {stats['logstore_with_logs']}, Without logs: {stats['logstore_without_logs']}")
        logger.info(f"Logcluster - With logs: {stats['logcluster_with_logs']}, Without logs: {stats['logcluster_without_logs']}")

        # 显示输出文件信息
        output_files = processor.get_output_files()
        if output_files:
            logger.info(f"Output files ({len(output_files)}):")
            for file_path in output_files:
                size_mb = file_path.stat().st_size / (1024 * 1024)
                logger.info(f"  - {file_path.name} ({size_mb:.2f} MB)")

        if merge_files:
            merged_file = processor.output_dir / "merged_results.jsonl"
            if merged_file.exists():
                size_mb = merged_file.stat().st_size / (1024 * 1024)
                logger.info(f"Merged file: {merged_file.name} ({size_mb:.2f} MB)")

    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
        processor._shutdown_event.set()
    except Exception as e:
        logger.error(f"Fatal error in main: {e}")
        processor._shutdown_event.set()
        raise
    finally:
        logger.info("Main function completed")


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Async Log Processor")
    parser.add_argument("--test", action="store_true", help="Test mode - process only first 10 records")
    parser.add_argument("--workers", type=int, default=1, help="Number of worker threads")
    parser.add_argument("--batch-size", type=int, default=100, help="Batch size for saving results")
    parser.add_argument("--merge", action="store_true", help="Merge small files into one large file after processing")
    parser.add_argument("--storage", choices=["jsonl", "jsonl_gz", "msgpack", "parquet"], default="jsonl", help="Storage format (default: jsonl)")

    args = parser.parse_args()

    # 运行异步主函数
    asyncio.run(main(test_mode=args.test, max_workers=args.workers, batch_size=args.batch_size, merge_files=args.merge, storage_format=args.storage))
