#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LLM vs Drain方法对比脚本
"""

import os
from control_log_diagnose.src.template_extractor import LogTemplateExtractor

def main():
    # 设置数据路径
    BASE_PATH = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    data_path = os.path.join(BASE_PATH, "data/control_log/processed_data")
    
    # 初始化提取器
    extractor = LogTemplateExtractor()
    
    print("🚀 开始LLM vs Drain算法对比测试")
    
    # 可以指定处理特定的logstore
    logstores = ["console_log"]  # 可以修改为你要测试的logstore
    
    # 执行对比分析
    stats = extractor.extract_templates(
        data_path=data_path,
        output_dir=None,  # 使用默认输出目录
        logstores=logstores,
        max_iterations=3,  # 减少LLM迭代次数以节约时间
        verbose=False,  # 设置为True可看详细过程
        enable_preprocessing=True,  # 启用日志预处理
        compare_methods=True  # 启用方法对比
    )
    
    print(f"\n{'='*60}")
    print("🎯 对比测试完成！")
    
    # 汇总多个logstore的对比结果
    if stats:
        print("\n📊 汇总统计:")
        for logstore, stat in stats.items():
            if stat.get('comparison', False):
                llm_stats = stat['llm_stats']
                drain_stats = stat['drain_stats']
                print(f"\n{logstore}:")
                print(f"  LLM方法    - 模板数: {llm_stats['template_count']:<3}, 覆盖率: {llm_stats['coverage_rate']*100:5.1f}%")
                print(f"  Drain方法  - 模板数: {drain_stats['template_count']:<3}, 覆盖率: {drain_stats['coverage_rate']*100:5.1f}%")
            else:
                print(f"\n{logstore}: 仅LLM方法 - 覆盖率: {stat['coverage_rate']*100:.1f}%")

if __name__ == "__main__":
    main()