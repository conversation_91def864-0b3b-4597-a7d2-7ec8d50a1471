#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析Drain算法生成的pattern_id结果
"""

import pandas as pd
import json
from pathlib import Path


def analyze_drain_patterns():
    """分析Drain模板提取结果"""
    
    # 文件路径
    BASE_PATH = Path(__file__).parent.parent
    data_file = BASE_PATH / "data/control_log/drain_pattern_results/console_log_with_pattern_ids.csv"
    mapping_file = BASE_PATH / "data/control_log/drain_pattern_results/console_log_pattern_mapping.json"
    
    # 读取数据
    df = pd.read_csv(data_file)
    with open(mapping_file, 'r', encoding='utf-8') as f:
        mapping = json.load(f)
    
    print("📊 Drain模板分析结果")
    print("=" * 50)
    
    # 基本统计
    print(f"总日志数量: {len(df):,}")
    print(f"模板数量: {mapping['template_count']}")
    print(f"覆盖率: {mapping['statistics']['coverage_rate']*100:.1f}%")
    
    # Pattern ID分布
    print(f"\n🏷️  Pattern ID分布:")
    pattern_counts = df['pattern_id'].value_counts()
    for pattern_id, count in pattern_counts.items():
        template = mapping['pattern_mappings'].get(pattern_id, '未知模板')
        print(f"  {pattern_id}: {count:2d} 条日志")
        print(f"    模板: {template}")
        print()
    
    # 按标签分析
    print(f"📈 按标签(Label)分析:")
    for label in sorted(df['label'].unique()):
        label_data = df[df['label'] == label]
        print(f"\n  标签 {label} ({len(label_data)} 条日志):")
        label_patterns = label_data['pattern_id'].value_counts()
        for pattern_id, count in label_patterns.items():
            percentage = count / len(label_data) * 100
            print(f"    {pattern_id}: {count:2d} 条 ({percentage:5.1f}%)")
    
    # 显示每个模板的示例日志
    print(f"\n📝 模板示例:")
    for pattern_id in pattern_counts.index:
        template = mapping['pattern_mappings'].get(pattern_id, '未知模板')
        sample_logs = df[df['pattern_id'] == pattern_id]['content'].head(2)
        
        print(f"\n  {pattern_id}:")
        print(f"    模板: {template}")
        print(f"    示例日志:")
        for i, log in enumerate(sample_logs, 1):
            print(f"      {i}. {log[:100]}{'...' if len(log) > 100 else ''}")
    
    print("\n" + "=" * 50)
    print("🎯 分析完成！可以使用pattern_id进行后续的日志分析和处理。")
    
    return df, mapping


if __name__ == "__main__":
    analyze_drain_patterns()