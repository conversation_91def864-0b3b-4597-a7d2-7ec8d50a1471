# 启停诊断算法关联设计文档

### 数据拉取

根据启停记录保存的 （request_id、操作时间、实例 ID） 拉取时间前后的日志信息

相关的 LogStore 已经保存在配置文件 logstore_config.yaml 中

其中 request_id 字段描述了通过 request_id 是否能够完整拉取相关的日志。

- full：仅凭 request_id 即可获取所有相关日志
- half：凭 request_id 无法获取所有相关日志，还需通过 实例 ID 作为关键字查询来获取相关日志
- none：无 request_id 字段，只能通过 实例 ID 作为关键字 查询获取相关的日志

数据存储：以 request_id、操作时间、实例 ID 作为基准目录，其他各 logstore 存放对应的 jsonl 文件



### 常见启动流程

- 151: ecs_region_master，\_\_source__ 不是对应的 nc_ip
  - 但是可以完全相信 request_id
- 151 单元: ecs_pync_log，\_\_source__ 是对应的 nc_ip，所以可以限制nc_ip 来查询
  - 可以相信 request_id，但不完全。可以额外搜一下关键字
- ecs-xunjian/libvirt_log:
  - \_\_source__ 是对应的 nc_ip，可以限制 nc_ip 来查询
  - request_id 在日志 detail 中
  - \_\_source__ + (request_id OR instance_id)
  -
