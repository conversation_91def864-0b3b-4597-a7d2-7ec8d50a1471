# 不同 label（0，1）的 pattern_sequence 分布差异分析总结

## 数据概览

基于 `ecs_regionmaster_info_log_with_pattern_ids.parquet` 数据集的分析结果：

- **成功案例 (Label 0)**: 9,994 个序列
- **失败案例 (Label 1)**: 8,531 个序列
- **总共**: 18,525 个唯一的 request_id 序列

## 主要发现

### 1. 序列长度显著差异 ✨

- **成功案例平均长度**: 524.7 个 pattern
- **失败案例平均长度**: 335.7 个 pattern
- **统计显著性**: Mann-Whitney U 检验 p < 0.001 (高度显著)
- **结论**: 成功案例的 pattern 序列明显更长，表明成功的操作流程更复杂或包含更多步骤

### 2. Pattern 多样性差异 🎯

- **成功案例**: 使用了 12,483 种不同的 pattern
- **失败案例**: 使用了 5,401 种不同的 pattern
- **共同 pattern**: 仅 701 种
- **统计显著性**: p < 0.001 (高度显著)
- **结论**: 失败案例的 pattern 使用更集中，成功案例涉及更多样化的操作

### 3. 序列相似性分析 🔍

- **成功案例内部相似性**: 0.103 (低相似性，高多样性)
- **失败案例内部相似性**: 0.321 (中等相似性，更集中)
- **不同类别间相似性**: 0.070 (低相似性，说明两类有明显区别)
- **结论**: 失败案例更相似，成功案例更多样化

### 4. 起始和结束 Pattern 模式 🚀

**成功案例常见起始 pattern**:

- drain_pattern_1 (3,786 次)
- drain_pattern_187 (1,331 次)
- drain_pattern_112 (843 次)

**失败案例常见起始 pattern**:

- drain_pattern_1 (7,311 次) - 与成功案例共同
- drain_pattern_1743 (159 次)
- drain_pattern_292 (126 次)

**成功案例常见结束 pattern**:

- drain_pattern_77 (3,502 次)
- drain_pattern_10 (2,434 次)
- drain_pattern_219 (1,475 次)

**失败案例常见结束 pattern**:

- drain_pattern_1587 (5,409 次) - 强烈的失败指示
- drain_pattern_2 (2,285 次)
- drain_pattern_33 (323 次)

### 5. 关键区分性 Pattern 🎖️

**强烈预示成功的 pattern** (只在成功案例中出现):

- drain_pattern_1025, drain_pattern_561, drain_pattern_1480 等

**强烈预示失败的 pattern** (只在失败案例中出现):

- drain_pattern_1859, drain_pattern_4621, drain_pattern_1875 等

## 业务含义 💡

1. **故障预测**: 可以通过监控序列长度和特定 pattern 的出现来早期识别可能的失败
2. **运维优化**: 失败案例的高度集中性表明有共同的故障模式，可以针对性优化
3. **流程改进**: 成功案例的高多样性可能反映了多种有效的操作路径
4. **监控重点**: 特别关注以 drain_pattern_1587 结束的序列，这是强烈的失败指示器

## 可视化说明 📊

生成的图表包括：

1. **序列长度分布箱线图**: 显示两类的长度差异
2. **Pattern 频率热力图**: 展示高频 pattern 在两类中的分布
3. **长度-多样性散点图**: 探索长度与多样性的关系
4. **区分性 Pattern 条形图**: 突出最能区分成功/失败的 pattern

这些发现为日志分析、故障诊断和系统优化提供了强有力的数据支撑。
