import json
import os
import sys
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any
import yaml
from concurrent.futures import ThreadPoolExecutor, as_completed
import logging
import threading

# 尝试导入rich用于进度条，如果未安装则使用简单的计数器
try:
    from rich.progress import Progress

    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False
    print("Note: rich not installed. Progress bar will not be displayed.")

    # 保留tqdm作为备选方案
    try:
        from tqdm import tqdm

        TQDM_AVAILABLE = True
    except ImportError:
        TQDM_AVAILABLE = False
        print("Note: tqdm not installed. Progress bar will not be displayed.")

BASE_PATH = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if BASE_PATH not in sys.path:
    sys.path.append(BASE_PATH)

from connector.sls import get_related_logs
import re


class LogFieldExtractor:
    """根据配置提取日志字段的类"""

    def __init__(self, extraction_config: Dict[str, Any]):
        self.extraction_config = extraction_config
        self.logstore_configs = extraction_config.get("logstore_configs", {})
        self.default_config = extraction_config.get("default_config", {})

    def extract_fields(
        self, log_entry: Dict[str, Any], logstore_name: str, request_id: str, instance_id: str, action_type: str, action_time: str, nc_ip: str
    ) -> Dict[str, Any]:
        """
        根据logstore配置提取日志字段

        Args:
            log_entry: 原始日志条目
            logstore_name: logstore名称
            request_id: 请求ID
            instance_id: 实例ID
            action_type: 操作类型
            action_time: 操作时间
            nc_ip: NC IP地址

        Returns:
            提取后的字段字典
        """
        # 初始化结果字典，包含基础字段
        extracted_fields = {"request_id": request_id, "instance_id": instance_id, "action_type": action_type, "action_time": action_time, "nc_ip": nc_ip}

        log_info = {}
        # 获取logstore特定配置
        config = self.logstore_configs.get(logstore_name, {})

        # 如果没有特定配置，使用默认配置
        if not config:
            config = self.default_config

        # 提取内容字段
        content_field = config.get("content_field")
        if content_field and content_field in log_entry.contents:
            log_info["content"] = log_entry.contents[content_field]
        else:
            # 尝试从默认内容字段中查找
            content_fields = self.default_config.get("content_fields", ["content", "message", "detail"])
            for field in content_fields:
                if field in log_entry.contents:
                    log_info["content"] = log_entry.contents[field]
                    break

        # 提取时间字段
        log_info["log_time"] = log_entry.timestamp

        # 提取 source 字段
        log_info["source"] = log_entry.source
        # 提取额外字段
        additional_fields = config.get("additional_fields", [])
        for field in additional_fields:
            if field in log_entry.contents:
                log_info[field] = log_entry.contents.get(field)

        extracted_fields["log"] = log_info
        return extracted_fields


class ProjectMapper:
    """处理 project - region 映射关系的类"""

    def __init__(self, config: Dict[str, Any]):
        self.region_project_mapping = config.get("region_project_mapping", {})
        with open(os.path.join(BASE_PATH, "control_log_diagnose", "sls_region_conf.json"), "r") as f:
            self.region_endpoint_mapping = json.load(f)

    def get_project_by_region(self, project: str, region_name: str) -> str:
        """
        根据region_name和project选择对应的子project

        Args:
            project: 原始project名称
            region_name: 区域名称

        Returns:
            对应的子project名称
        """
        if region_name in self.region_project_mapping and project in self.region_project_mapping[region_name]:
            return self.region_project_mapping[region_name][project]
        else:
            return project

    def get_endpoint_by_region(self, region_name: str) -> str:
        """
        根据region_name获取对应的endpoint

        Args:
            region_name: 区域名称

        Returns:
            对应的endpoint
        """
        return self.region_endpoint_mapping.get(region_name, None)


class LogQueryGenerator:
    """生成日志查询语句的类"""

    @staticmethod
    def generate_logstore_query(item: Dict[str, Any], instance_id: str, request_id: str, nc_ip: str) -> str:
        """生成logstore查询语句"""
        query_str = "*"
        ipv4_pattern = r"^(\d{1,3}\.){3}\d{1,3}$"

        key_fields = item.get("key_fields", [])

        if item.get("request_id", "none") == "none":
            if re.match(ipv4_pattern, nc_ip):
                query_str += f" AND __source__: {nc_ip}"
            query_str += (
                f" | set session parallel_sql=true; select * where {key_fields[0]} LIKE '%{instance_id}%' OR {key_fields[0]} LIKE '%{request_id}%' limit 10000"
            )
        else:
            if item["logstore"] == "schedule_trace":
                query_str += f" and requestId: '{request_id}'"
            else:
                query_str += f" and request_id: '{request_id}'"

        return query_str

    @staticmethod
    def generate_logcluster_query(item: str, instance_id: str, request_id: str, nc_ip: str) -> str:
        """生成logcluster查询语句"""
        query = "*"
        if item.startswith("ecs_region_master"):
            query += f" | set session parallel_sql=true; select * where content LIKE '%{instance_id}%' limit 10000"
        else:
            # 检查nc_ip是否为IPv4地址格式
            ipv4_pattern = r"^(\d{1,3}\.){3}\d{1,3}$"
            if re.match(ipv4_pattern, nc_ip):
                query += f" AND nc: {nc_ip} | set session parallel_sql=true; select * where content LIKE '%{request_id}%' OR content LIKE '%{instance_id}%' limit 10000"
            else:
                query += f" | set session parallel_sql=true; select * where content LIKE '%{request_id}%' OR content LIKE '%{instance_id}%' limit 10000"
        return query


class LogRetriever:
    """负责从日志系统获取日志的类"""

    def __init__(self, project_mapper: ProjectMapper):
        self.project_mapper = project_mapper

    def retrieve_logstore_logs(
        self, item: Dict[str, Any], instance_id: str, request_id: str, start_time: float, end_time: float, nc_ip: str, region_name: str
    ) -> tuple:
        """获取logstore日志"""
        try:
            query_str = LogQueryGenerator.generate_logstore_query(item, instance_id, request_id, nc_ip)
            # Split region_name by '-' and try different combinations to find matching project
            region_parts = region_name.split("-")
            project = item["project"]  # default fallback

            # Try different combinations: 1-2, 1-3, 1-4 segments
            mapped_project = None
            mapped_region = None
            for i in range(2, min(5, len(region_parts) + 1)):
                region_key = "-".join(region_parts[:i])
                temp_mapped_project = self.project_mapper.get_project_by_region(item["project"], region_key)
                if temp_mapped_project != item["project"]:  # Found a mapping
                    mapped_project = temp_mapped_project
                    mapped_region = region_key
                    break
            else:
                # If no mapping found with any combination, use the full region_name
                temp_mapped_project = self.project_mapper.get_project_by_region(item["project"], region_name)
                if temp_mapped_project != item["project"]:
                    mapped_project = temp_mapped_project
                    mapped_region = region_name

            # Query original project first
            logs = get_related_logs(
                item["project"],
                item["logstore"],
                start_time,
                end_time,
                query_str=query_str,
                endpoint=self.project_mapper.get_endpoint_by_region(region_name),
                max_retries=5,  # 增加重试次数
                retry_delay=2,  # 增加重试延迟
            )

            # If mapped project exists and is different, query it too
            if mapped_project and mapped_project != item["project"]:
                # Use the mapped region to get the correct endpoint
                mapped_endpoint = (
                    self.project_mapper.get_endpoint_by_region(mapped_region) if mapped_region else self.project_mapper.get_endpoint_by_region(region_name)
                )
                mapped_logs = get_related_logs(
                    mapped_project,
                    item["logstore"],
                    start_time,
                    end_time,
                    query_str=query_str,
                    endpoint=mapped_endpoint,
                    max_retries=5,  # 增加重试次数
                    retry_delay=2,  # 增加重试延迟
                )
                logs.extend(mapped_logs)

            project = mapped_project if mapped_project else item["project"]
            return logs, project
        except Exception as e:
            return [], item["project"]

    def retrieve_logcluster_logs(self, item: str, value: str, instance_id: str, request_id: str, nc_ip: str, start_time: float, end_time: float) -> List[Any]:
        """获取logcluster日志"""
        try:
            query = LogQueryGenerator.generate_logcluster_query(item, instance_id, request_id, nc_ip)

            logs = get_related_logs(
                "log-cluster",
                value,
                start_time,
                end_time,
                query_str=query,
                max_retries=5,  # 增加重试次数
                retry_delay=2,  # 增加重试延迟
            )
            return logs
        except Exception as e:
            return []


class ResultTracker:
    """跟踪处理结果的类"""

    def __init__(self):
        self.logstore_with_logs = 0
        self.logstore_without_logs = 0
        self.logcluster_with_logs = 0
        self.logcluster_without_logs = 0
        self.result = {
            "logstore": {},
            "log_cluster": {},
        }

    def increment_logstore_with_logs(self):
        self.logstore_with_logs += 1

    def increment_logstore_without_logs(self):
        self.logstore_without_logs += 1

    def increment_logcluster_with_logs(self):
        self.logcluster_with_logs += 1

    def increment_logcluster_without_logs(self):
        self.logcluster_without_logs += 1

    def get_summary(self) -> Dict[str, Any]:
        return {
            "logstore_with_logs": self.logstore_with_logs,
            "logstore_without_logs": self.logstore_without_logs,
            "logcluster_with_logs": self.logcluster_with_logs,
            "logcluster_without_logs": self.logcluster_without_logs,
            "result": self.result,
        }


class LogProcessor:
    """主处理器类，协调各个组件完成日志处理"""

    def __init__(self, config: Dict[str, Any], verbose: bool = True, max_file_size: int = 1024 * 1024 * 1024, max_workers: int = 10, batch_size: int = 100):
        self.config = config
        self.project_mapper = ProjectMapper(config)
        self.log_retriever = LogRetriever(self.project_mapper)
        self.result_tracker = ResultTracker()
        self.verbose = verbose  # 控制详细输出的开关
        self.max_file_size = max_file_size  # 最大文件大小 (默认100MB)
        self.max_workers = max_workers  # 并发线程数
        self.batch_size = batch_size  # 批量写入大小
        # 创建日志保存目录
        self.log_output_dir = os.path.join(BASE_PATH, "logs_output")
        os.makedirs(self.log_output_dir, exist_ok=True)
        # 所有结果保存文件（JSON Lines格式）
        self.all_results_file = os.path.join(self.log_output_dir, "all_results.jsonl")
        # checkpoint文件，记录每个request_id的位置
        self.checkpoint_file = os.path.join(self.log_output_dir, "checkpoint.json")
        # 文件序号，用于当文件过大时创建新文件
        self.file_counter = 0
        # 需要保存的新结果
        self.new_results = {}
        # 线程锁，用于保护共享资源
        self.lock = threading.Lock()
        # 任务队列，用于控制并发任务数量
        self.task_semaphore = threading.Semaphore(max_workers * 2)  # 允许最多2倍于worker数量的任务在队列中
        # checkpoint批处理大小
        self.checkpoint_batch_size = 100
        # 待处理的checkpoint更新
        self.pending_checkpoint_updates = {}
        # 初始化日志记录器
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.INFO if verbose else logging.WARNING)

        # 防止日志传播到根logger，避免重复打印
        self.logger.propagate = False

        # 只在没有处理器的情况下添加处理器，避免重复日志
        if not self.logger.handlers:
            # 创建日志文件处理器
            log_file = os.path.join(self.log_output_dir, "log_processor.log")
            file_handler = logging.FileHandler(log_file, encoding="utf-8")
            file_handler.setLevel(logging.INFO)

            # 创建控制台处理器
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO if verbose else logging.WARNING)

            # 创建格式器并添加到处理器
            formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
            file_handler.setFormatter(formatter)
            console_handler.setFormatter(formatter)

            # 添加处理器到记录器
            self.logger.addHandler(file_handler)
            self.logger.addHandler(console_handler)

        # 加载日志字段提取配置
        extraction_config_path = os.path.join(BASE_PATH, "control_log_diagnose", "log_extraction_config.yaml")
        if os.path.exists(extraction_config_path):
            with open(extraction_config_path, "r", encoding="utf-8") as f:
                self.extraction_config = yaml.safe_load(f)
        else:
            self.extraction_config = {}
            self.logger.warning(f"Extraction config file not found: {extraction_config_path}")

        self.field_extractor = LogFieldExtractor(self.extraction_config)

        # 初始化checkpoint（必须在_load_all_results之前）
        self.processed_requests = self._load_checkpoint()
        # 初始化所有结果
        self.all_results = self._load_all_results()


    def _load_checkpoint(self) -> Dict[str, Any]:
        """加载checkpoint信息，记录已处理的request_id"""
        if os.path.exists(self.checkpoint_file):
            try:
                with open(self.checkpoint_file, "r") as f:
                    return json.load(f)
            except Exception as e:
                self.logger.warning(f"Failed to load checkpoint file: {e}")
                return {}
        return {}

    def _save_checkpoint(self, request_id: str, file_index: int):
        """保存checkpoint信息"""
        with self.lock:
            self.pending_checkpoint_updates[request_id] = file_index

            # 如果待处理的更新数量达到批处理大小，则执行批量更新
            if len(self.pending_checkpoint_updates) >= self.checkpoint_batch_size:
                self._flush_checkpoint_updates()
            # 如果没有待处理的更新但request_id不在已处理请求中，则直接更新
            elif request_id not in self.processed_requests:
                self.processed_requests[request_id] = file_index
                try:
                    with open(self.checkpoint_file, "w") as f:
                        json.dump(self.processed_requests, f, indent=2)
                except Exception as e:
                    self.logger.warning(f"Failed to save checkpoint file: {e}")

    def _flush_checkpoint_updates(self):
        """刷新checkpoint更新到文件"""
        with self.lock:
            if not self.pending_checkpoint_updates:
                return

            # 更新已处理请求字典
            self.processed_requests.update(self.pending_checkpoint_updates)

            # 清空待处理更新
            self.pending_checkpoint_updates.clear()

            # 写入文件
            try:
                with open(self.checkpoint_file, "w") as f:
                    json.dump(self.processed_requests, f, indent=2)
            except Exception as e:
                self.logger.warning(f"Failed to save checkpoint file: {e}")

    def _load_all_results(self) -> Dict[str, Any]:
        """加载所有结果（JSON Lines格式），包括分包的文件"""
        all_data = {}

        # 加载主文件
        if os.path.exists(self.all_results_file):
            try:
                if os.path.getsize(self.all_results_file) > 0:
                    with open(self.all_results_file, "r", encoding="utf-8") as f:
                        line_number = 0
                        for line in f:
                            line_number += 1
                            try:
                                data = json.loads(line.strip())
                                if isinstance(data, dict):
                                    all_data.update(data)
                                    # 如果checkpoint文件不存在，则从结果文件重建checkpoint
                                    if not os.path.exists(self.checkpoint_file):
                                        for request_id in data:
                                            self.processed_requests[request_id] = 0
                            except json.JSONDecodeError as e:
                                self.logger.warning(f"Failed to parse line {line_number} in main results file: {e}")
                                continue
                else:
                    self.logger.warning(f"Main results file is empty: {self.all_results_file}")
            except Exception as e:
                self.logger.warning(f"Failed to load all results file: {e}")

        # 加载分包文件
        base_name = os.path.splitext(self.all_results_file)[0]
        counter = 1
        while True:
            file_name = f"{base_name}_{counter}.jsonl"
            if not os.path.exists(file_name):
                break

            try:
                if os.path.getsize(file_name) > 0:
                    with open(file_name, "r", encoding="utf-8") as f:
                        line_number = 0
                        for line in f:
                            line_number += 1
                            try:
                                data = json.loads(line.strip())
                                if isinstance(data, dict):
                                    all_data.update(data)
                                    # 如果checkpoint文件不存在，则从结果文件重建checkpoint
                                    if not os.path.exists(self.checkpoint_file):
                                        for request_id in data:
                                            self.processed_requests[request_id] = counter
                            except json.JSONDecodeError as e:
                                self.logger.warning(f"Failed to parse line {line_number} in results file {file_name}: {e}")
                                continue
                else:
                    self.logger.warning(f"Results file is empty: {file_name}")
                counter += 1
            except Exception as e:
                self.logger.warning(f"Failed to load results file {file_name}: {e}")
                # 继续尝试加载下一个文件，而不是直接break
                counter += 1
                continue

        # 如果checkpoint文件不存在但从结果文件重建了checkpoint，保存它
        if not os.path.exists(self.checkpoint_file) and self.processed_requests:
            try:
                with open(self.checkpoint_file, "w") as f:
                    json.dump(self.processed_requests, f, indent=2)
                self.logger.info(f"Rebuilt checkpoint file with {len(self.processed_requests)} entries")
            except Exception as e:
                self.logger.warning(f"Failed to save rebuilt checkpoint file: {e}")

        return all_data

    def _merge_small_files(self):
        """合并小文件以减少文件数量"""
        try:
            base_name = os.path.splitext(self.all_results_file)[0]
            counter = 1
            files_to_merge = []

            # 收集所有小文件
            while True:
                file_name = f"{base_name}_{counter}.jsonl"
                if not os.path.exists(file_name):
                    break

                if os.path.getsize(file_name) < self.max_file_size // 10:  # 如果文件小于最大文件大小的1/10
                    files_to_merge.append(file_name)
                counter += 1

            # 如果有多个小文件需要合并
            if len(files_to_merge) > 1:
                merged_lines = []
                for file_name in files_to_merge:
                    try:
                        with open(file_name, "r", encoding="utf-8") as f:
                            # 读取所有行
                            lines = f.readlines()
                            merged_lines.extend(lines)
                        # 删除原文件
                        os.remove(file_name)
                    except Exception as e:
                        self.logger.warning(f"Failed to merge file {file_name}: {e}")

                # 保存合并后的数据到新文件
                if merged_lines:
                    new_file_name = f"{base_name}_{counter}.jsonl"
                    with open(new_file_name, "w", encoding="utf-8") as f:
                        f.writelines(merged_lines)
                    self.logger.info(f"Merged {len(files_to_merge)} small files into {new_file_name}")
        except Exception as e:
            self.logger.error(f"Failed to merge small files: {e}")


    def _save_all_results(self):
        """保存所有结果到一个文件（JSON Lines格式），当文件过大时创建新文件"""
        try:
            # 复制数据以减少锁的持有时间
            current_results = {}
            with self.lock:
                if not self.new_results:
                    return
                current_results = self.new_results.copy()
                self.new_results.clear()

            # 文件操作在锁外执行以避免长时间持有锁
            # 检查当前文件大小
            if os.path.exists(self.all_results_file):
                file_size = os.path.getsize(self.all_results_file)
                if file_size > self.max_file_size:
                    # 文件过大，创建新文件
                    self.file_counter += 1
                    base_name = os.path.splitext(self.all_results_file)[0]
                    new_file_name = f"{base_name}_{self.file_counter}.jsonl"
                    # 重命名当前文件
                    os.rename(self.all_results_file, new_file_name)
                    self.logger.info(f"Renamed large file to {new_file_name}")

            # 保存结果到文件
            saved_request_ids = []
            with open(self.all_results_file, "a", encoding="utf-8") as f:
                for request_id, data in current_results.items():
                    # 将每个request的结果作为一行JSON写入
                    json_line = json.dumps({request_id: data}, ensure_ascii=False)
                    f.write(json_line + "\n")
                    saved_request_ids.append(request_id)

            self.logger.info(f"Saved {len(current_results)} new results to {self.all_results_file}")

            # 更新checkpoint（使用独立的锁操作）
            with self.lock:
                for request_id in saved_request_ids:
                    self._save_checkpoint(request_id, 0)  # 0表示主文件
                # 刷新checkpoint更新
                self._flush_checkpoint_updates()

            # 定期合并小文件（在锁外执行）
            if self.file_counter % 10 == 0:  # 每10个文件合并一次
                self._merge_small_files()
                
        except Exception as e:
            self.logger.error(f"Failed to save all results to {self.all_results_file}: {e}")
            # 如果保存失败，将结果放回队列
            with self.lock:
                self.new_results.update(current_results)

    def process_record(self, row: pd.Series) -> Dict[str, Any]:
        """处理单条记录"""
        request_id = row["request_id"]
        instance_id = row["instance_id"]
        region_name = row["region_name"]
        action_time = gmt_create = row["gmt_create"]
        nc_ip = row["nc_ip"]
        action_type = row["action_type"]

        # 检查是否已经处理过该request_id
        if request_id in self.processed_requests:
            self.logger.info(f"SKIPPED: Already processed request: {request_id}")
            return self.result_tracker.get_summary()

        start_time = datetime.strptime(gmt_create, "%Y-%m-%d %H:%M:%S").timestamp() - 60 * 5
        end_time = start_time + 60 * 10

        self.logger.info(f"Processing request: {request_id}")
        self.logger.info(f"Instance ID: {instance_id} | NC IP: {nc_ip} | Region: {region_name} | Action Type: {action_type}")

        # 处理logstore日志
        self._process_logstores(request_id, instance_id, nc_ip, region_name, start_time, end_time, action_type, action_time)

        # 处理logcluster日志
        self._process_logclusters(request_id, instance_id, nc_ip, start_time, end_time, action_type, action_time)

        # 在所有处理完成后再保存结果（批量写入）
        if len(self.new_results) >= self.batch_size:
            # 使用同步保存避免异步复杂性和潜在的死锁问题
            self._save_all_results()
        # 注意：checkpoint保存移动到结果真正保存到文件时才执行

        # 打印摘要
        print("DEBUG: About to call _print_summary")
        self._print_summary(request_id)
        print("DEBUG: _print_summary completed")

        print("DEBUG: About to return summary")
        summary = self.result_tracker.get_summary()
        print("DEBUG: Got summary, returning")
        return summary

    def _save_result(self, request_id: str, log_type: str, key: str, extracted_logs: List[Dict], action_type: str):
        """通用的结果保存方法"""
        with self.lock:
            if request_id not in self.all_results:
                self.all_results[request_id] = {"logstore": {}, "log_cluster": {}, "action_type": action_type}
            self.all_results[request_id][log_type][key] = extracted_logs

            if request_id not in self.new_results:
                self.new_results[request_id] = {"logstore": {}, "log_cluster": {}, "action_type": action_type}
            self.new_results[request_id][log_type][key] = extracted_logs

    def finalize_results(self):
        """最终保存所有剩余的结果"""
        # 保存所有剩余的结果（同步方式，确保在程序结束前完成）
        self._save_all_results()

        # 确保所有checkpoint更新都已写入
        self._flush_checkpoint_updates()

    def _process_logstores(
        self, request_id: str, instance_id: str, nc_ip: str, region_name: str, start_time: float, end_time: float, action_type: str, action_time: str
    ):
        """处理logstore日志"""
        try:
            for item in self.config.get("logstores", []):
                logs, project = self.log_retriever.retrieve_logstore_logs(item, instance_id, request_id, start_time, end_time, nc_ip, region_name)

                # 提取字段后的日志
                extracted_logs = []
                if logs:
                    for log_entry in logs:
                        extracted_log = self.field_extractor.extract_fields(
                            log_entry, item["logstore"], request_id, instance_id, action_type, action_time, nc_ip
                        )
                        extracted_logs.append(extracted_log)

                if extracted_logs:
                    log_count = len(extracted_logs)
                    self.logger.info(f"FOUND LOGS: {log_count} logs for {request_id} in {project}/{item['logstore']}")
                    self.result_tracker.result["logstore"][item["logstore"]] = extracted_logs
                    self.result_tracker.increment_logstore_with_logs()
                else:
                    self.logger.info(f"NO LOGS: No logs found for {request_id} in {project}/{item['logstore']}")
                    self.result_tracker.result["logstore"][item["logstore"]] = []
                    self.result_tracker.increment_logstore_without_logs()

                self._save_result(request_id, "logstore", item["logstore"], extracted_logs, action_type)
        except Exception as e:
            self.logger.error(f"Error processing logstores for {request_id}: {e}")

    def _process_logclusters(self, request_id: str, instance_id: str, nc_ip: str, start_time: float, end_time: float, action_type: str, action_time: str):
        """处理logcluster日志"""
        try:
            self.logger.info(f"Log Cluster Search for request {request_id}")
            for item, value in self.config.get("log_clusters", {}).items():
                logs = self.log_retriever.retrieve_logcluster_logs(item, value, instance_id, request_id, nc_ip, start_time, end_time)

                # 提取字段后的日志
                extracted_logs = []
                if logs:
                    for log_entry in logs:
                        # 对于logcluster，我们使用value作为标识符
                        extracted_log = self.field_extractor.extract_fields(log_entry, value, request_id, instance_id, action_type, action_time, nc_ip)
                        extracted_logs.append(extracted_log)

                if extracted_logs:
                    log_count = len(extracted_logs)
                    self.logger.info(f"FOUND LOGS: {log_count} logs for {request_id} in log-cluster/{value}")
                    self.result_tracker.result["log_cluster"][item] = extracted_logs
                    self.result_tracker.increment_logcluster_with_logs()
                else:
                    self.logger.info(f"NO LOGS: No logs found for {request_id} in log-cluster/{value}")
                    self.result_tracker.result["log_cluster"][item] = []
                    self.result_tracker.increment_logcluster_without_logs()

                self._save_result(request_id, "log_cluster", item, extracted_logs, action_type)
        except Exception as e:
            self.logger.error(f"Error processing logclusters for {request_id}: {e}")

    def _print_summary(self, request_id: str):
        """打印处理摘要"""
        self.logger.info(f"SUMMARY for {request_id}:")
        self.logger.info(
            f"Logstore - Found logs in {self.result_tracker.logstore_with_logs} sources, No logs in {self.result_tracker.logstore_without_logs} sources"
        )
        self.logger.info(
            f"Logcluster - Found logs in {self.result_tracker.logcluster_with_logs} sources, No logs in {self.result_tracker.logcluster_without_logs} sources"
        )
        total_with_logs = self.result_tracker.logstore_with_logs + self.result_tracker.logcluster_with_logs
        total_without_logs = self.result_tracker.logstore_without_logs + self.result_tracker.logcluster_without_logs
        self.logger.info(f"TOTAL - Found logs in {total_with_logs} sources, No logs in {total_without_logs} sources")


def main(verbose: bool = True, max_workers: int = 10):
    """主函数，支持并发处理"""
    if not os.path.exists(os.path.join(BASE_PATH, "logs_output")):
        os.makedirs(os.path.join(BASE_PATH, "logs_output"))
    # 设置日志记录
    logging.basicConfig(
        level=logging.INFO if verbose else logging.WARNING,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[logging.StreamHandler(), logging.FileHandler(os.path.join(BASE_PATH, "logs_output", "main.log"), encoding="utf-8")],
    )
    logger = logging.getLogger(__name__)

    config = yaml.safe_load(open("control_log_diagnose/logstore_config.yaml", "r"))
    records = pd.read_csv("data/control_log/start_vm_records/merged_start_vm_records.csv")
    records = records[::-1].reset_index(drop=True)

    with open(os.path.join(BASE_PATH, "control_log_diagnose", "parsed_region_mapping.json"), "r") as f:
        region_project_mapping = json.load(f)
    config["region_project_mapping"] = region_project_mapping

    logger.info("Configuration loaded successfully.")

    # 创建处理器实例
    processor = LogProcessor(config, verbose=verbose, max_workers=max_workers, batch_size=3)
    total_records = len(records)
    logger.info(f"Processing {total_records} records...")

    # 使用线程池并发处理记录
    if max_workers > 1:
        logger.info(f"Using concurrent processing with {max_workers} workers")

        # 将记录转换为列表以便并发处理
        records_list = [row for _, row in records.iterrows()]

        # 使用线程池处理记录
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_index = {executor.submit(processor.process_record, row): i for i, row in enumerate(records_list)}

            # 处理完成的任务
            if RICH_AVAILABLE:
                with Progress() as progress:
                    task = progress.add_task(f"[green]Processing records (0/{total_records})...", total=total_records)
                    completed_count = 0
                    for future in as_completed(future_to_index):
                        index = future_to_index[future]
                        try:
                            future.result()
                            completed_count += 1
                            # 更新进度条描述显示当前进度
                            progress.update(task, advance=1, description=f"[green]Processing records ({completed_count}/{total_records})...")
                            if verbose:
                                logger.info(f"Processed record {index+1}/{total_records}")
                        except Exception as e:
                            completed_count += 1
                            progress.update(task, advance=1, description=f"[green]Processing records ({completed_count}/{total_records})...")
                            logger.error(f"Error processing record {index}: {e}")
                # 保存所有剩余的结果
                processor.finalize_results()
            elif TQDM_AVAILABLE:
                progress_bar = tqdm(total=total_records, desc="Processing records")
                for future in as_completed(future_to_index):
                    index = future_to_index[future]
                    try:
                        future.result()
                        if not verbose and TQDM_AVAILABLE:
                            progress_bar.update(1)
                        elif verbose:
                            logger.info(f"Processed record {index+1}/{total_records}")
                    except Exception as e:
                        logger.error(f"Error processing record {index}: {e}")
                        if TQDM_AVAILABLE:
                            progress_bar.update(1)
                if TQDM_AVAILABLE:
                    progress_bar.close()
                # 保存所有剩余的结果
                processor.finalize_results()
            else:
                processed_count = 0
                for future in as_completed(future_to_index):
                    index = future_to_index[future]
                    try:
                        future.result()
                        if verbose:
                            processed_count += 1
                            logger.info(f"Processed record {processed_count}/{total_records}")
                    except Exception as e:
                        logger.error(f"Error processing record {index}: {e}")
                        if verbose:
                            processed_count += 1
                # 保存所有剩余的结果
                processor.finalize_results()
    else:
        # 串行处理（向后兼容）
        logger.info("Using sequential processing")

        # 使用rich或tqdm显示进度条，如果未安装则显示简单的进度计数
        if RICH_AVAILABLE:
            with Progress() as progress:
                task = progress.add_task(f"[green]Processing records (0/{total_records})...", total=total_records)
                for index, row in enumerate(records.iterrows()):
                    if verbose:
                        logger.info(f"Processing record {index+1}/{total_records}")
                    processor.process_record(row[1])
                    # 更新进度条描述显示当前进度
                    progress.update(task, advance=1, description=f"[green]Processing records ({index+1}/{total_records})...")
        elif TQDM_AVAILABLE:
            record_iterator = tqdm(records.iterrows(), total=total_records, desc="Processing records")
            for index, row in record_iterator:
                if TQDM_AVAILABLE and verbose:
                    logger.info(f"Processing record {index+1}/{total_records}")
                processor.process_record(row[1])
        else:
            processed_count = 0
            for index, row in records.iterrows():
                if verbose:
                    processed_count += 1
                    logger.info(f"Processing record {processed_count}/{total_records}")
                processor.process_record(row)

            # 保存所有剩余的结果
            processor.finalize_results()


if __name__ == "__main__":
    main(
        verbose=True,
        max_workers=1,
    )
