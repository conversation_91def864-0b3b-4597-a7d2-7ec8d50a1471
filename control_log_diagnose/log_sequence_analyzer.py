#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志序列模式分析器
使用LLM提取日志模板，分析start_vm_success和start_vm_failed的时间序列模式
"""

import json
import os
import re
from collections import defaultdict, Counter
from typing import Dict, List, Set, Tuple, Optional
import pandas as pd
from datetime import datetime
from difflib import SequenceMatcher
import hashlib


class LogSequenceAnalyzer:
    def __init__(self, structured_logs_file: str):
        self.structured_logs_file = structured_logs_file
        self.structured_logs = []
        self.vm_sequences = defaultdict(list)  # vm_id -> [(timestamp, template, logstore)]
        self.success_sequences = []  # 成功的模板序列
        self.failed_sequences = []  # 失败的模板序列
        self.templates_cache = {}  # 缓存LLM提取的模板

    def load_structured_logs(self):
        """加载结构化日志数据"""
        with open(self.structured_logs_file, "r", encoding="utf-8") as f:
            self.structured_logs = json.load(f)
        print(f"📂 加载了 {len(self.structured_logs)} 条结构化日志")

    def extract_vm_id_from_content(self, content: str) -> Optional[str]:
        """从日志内容中提取VM ID"""
        # 匹配各种实例ID模式
        patterns = [
            r"\b(i-[a-zA-Z0-9]{17})\b",  # ECS实例ID
            r"\b(eci-[a-zA-Z0-9]{16,20})\b",  # ECI实例ID
        ]

        for pattern in patterns:
            match = re.search(pattern, content)
            if match:
                return match.group(1)

        # 如果没有找到明确的实例ID，尝试从文件名或其他字段提取
        if "instanceId" in content:
            # 提取instanceId=xxx的格式
            match = re.search(r"instanceId[=\s]*([a-zA-Z0-9-]+)", content)
            if match:
                return match.group(1)

        return None

    def extract_template_with_llm_style(self, content: str) -> str:
        """使用类似LLM的方式提取日志模板"""
        template = content

        # 高级模板提取规则 - 保留更多语义信息
        replacements = [
            # 实例ID替换
            (r"\b(i-[a-zA-Z0-9]{17})\b", "[INSTANCE_ID]"),
            (r"\b(eci-[a-zA-Z0-9]{16,20})\b", "[ECI_ID]"),
            (r"\b(eni-[a-zA-Z0-9]{17})\b", "[ENI_ID]"),
            # UUID和哈希值
            (r"\b[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}\b", "[UUID]"),
            (r"\b[A-F0-9]{8}-[A-F0-9]{4}-[A-F0-9]{4}-[A-F0-9]{4}-[A-F0-9]{12}\b", "[UUID]"),
            # IP地址
            (r"\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b", "[IP]"),
            # 时间戳模式
            (r"\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3}", "[TIMESTAMP_MS]"),
            (r"\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}", "[TIMESTAMP]"),
            (r"\d{4}/\d{2}/\d{2} \d{2}:\d{2}:\d{2}", "[TIMESTAMP]"),
            # 特定的数值模式
            (r"cost[=:\s]*\d+", "cost=[COST]"),
            (r"used \d+\.\d+", "used [TIME]"),
            (r"\b\d+ms\b", "[TIME_MS]"),
            (r"\b\d+ns\b", "[TIME_NS]"),
            # PID和线程ID
            (r"pid=\d+", "pid=[PID]"),
            (r"\[pid=\d+,iso=\d+\]", "[pid=[PID],iso=[ISO]]"),
            (r"\[\d+\]\[\s*\d+\]", "[[THREAD_ID]]"),
            # 数字ID和代码
            (r"code\s*[:-]\s*-?\d+", "code:[CODE]"),
            (r"uid\s*[:-]\s*\d+", "uid:[UID]"),
            (r"ncId:\s*\d+-\d+", "ncId:[NC_ID]"),
            # 版本和端口
            (r"\b\d+\.\d+\.\d+\b", "[VERSION]"),
            (r":\d{4,5}\b", ":[PORT]"),
            # 文件路径
            (r"/[a-zA-Z0-9_/.-]+\.[a-zA-Z0-9]+", "[FILE_PATH]"),
            (r"pangu://[^\s]+", "[PANGU_PATH]"),
            # 长数字和时间戳
            (r"\b\d{10,13}\b", "[TIMESTAMP_NUM]"),
            (r"\b\d{6,}\b", "[LARGE_NUM]"),
            (r"\b\d+\b", "[NUM]"),
            # 十六进制值
            (r"\b0x[a-fA-F0-9]+\b", "[HEX]"),
            # 清理多余空格
            (r"\s+", " "),
        ]

        for pattern, replacement in replacements:
            template = re.sub(pattern, replacement, template)

        return template.strip()

    def group_logs_by_vm_and_time(self):
        """按虚拟机ID和时间对日志进行分组"""
        print("🔍 按VM ID和时间分组日志...")

        for log in self.structured_logs:
            vm_id = self.extract_vm_id_from_content(log.get("original_content", ""))
            if not vm_id:
                continue

            timestamp = log.get("timestamp", "")
            content = log.get("original_content", "")
            logstore = log.get("logstore", "unknown")
            action_type = log.get("action_type", "unknown")

            # 提取日志模板
            template = self.extract_template_with_llm_style(content)

            # 为模板生成简短的标识符
            template_hash = hashlib.md5(template.encode()).hexdigest()[:8]
            template_id = f"{logstore}_{template_hash}"

            self.vm_sequences[vm_id].append(
                {
                    "timestamp": timestamp,
                    "template": template,
                    "template_id": template_id,
                    "logstore": logstore,
                    "action_type": action_type,
                    "original_content": content,
                }
            )

        # 按时间排序每个VM的日志序列
        for vm_id in self.vm_sequences:
            self.vm_sequences[vm_id].sort(key=lambda x: x["timestamp"])

        print(f"📊 找到 {len(self.vm_sequences)} 个VM的日志序列")

    def extract_action_sequences(self):
        """提取成功和失败的操作序列"""
        print("🎯 提取成功和失败的操作序列...")

        for vm_id, logs in self.vm_sequences.items():
            if not logs:
                continue

            # 确定最终结果
            final_action = logs[-1]["action_type"]

            # 提取模板序列
            template_sequence = []
            logstore_sequence = []

            for log in logs:
                template_sequence.append(log["template_id"])
                logstore_sequence.append(log["logstore"])

            sequence_info = {
                "vm_id": vm_id,
                "template_sequence": template_sequence,
                "logstore_sequence": logstore_sequence,
                "full_logs": logs,
                "sequence_length": len(template_sequence),
            }

            if "success" in final_action:
                self.success_sequences.append(sequence_info)
            elif "failed" in final_action:
                self.failed_sequences.append(sequence_info)

        print(f"✅ 成功序列: {len(self.success_sequences)} 个")
        print(f"❌ 失败序列: {len(self.failed_sequences)} 个")

    def analyze_sequence_patterns(self):
        """分析序列模式"""
        print("\n" + "=" * 80)
        print("📊 序列模式分析")
        print("=" * 80)

        # 分析成功序列的一致性
        self._analyze_consistency("成功", self.success_sequences)
        self._analyze_consistency("失败", self.failed_sequences)

        # 比较成功和失败序列的差异
        self._compare_success_vs_failed()

    def _analyze_consistency(self, label: str, sequences: List[Dict]):
        """分析序列的一致性"""
        if not sequences:
            print(f"\n{label}序列为空，跳过分析")
            return

        print(f"\n🔍 {label}序列一致性分析:")

        # 统计序列长度分布
        length_counter = Counter([seq["sequence_length"] for seq in sequences])
        print(f"  序列长度分布:")
        for length, count in sorted(length_counter.items()):
            percentage = (count / len(sequences)) * 100
            print(f"    长度 {length}: {count} 个序列 ({percentage:.1f}%)")

        # 统计最常见的模板序列
        sequence_counter = Counter()
        for seq in sequences:
            sequence_str = " -> ".join(seq["template_sequence"])
            sequence_counter[sequence_str] += 1

        print(f"\n  最常见的模板序列 (Top 10):")
        for sequence_str, count in sequence_counter.most_common(10):
            percentage = (count / len(sequences)) * 100
            print(f"    [{count}次, {percentage:.1f}%] {sequence_str}")

        # 分析logstore序列模式
        logstore_sequence_counter = Counter()
        for seq in sequences:
            logstore_sequence_str = " -> ".join(seq["logstore_sequence"])
            logstore_sequence_counter[logstore_sequence_str] += 1

        print(f"\n  最常见的LogStore序列 (Top 5):")
        for sequence_str, count in logstore_sequence_counter.most_common(5):
            percentage = (count / len(sequences)) * 100
            print(f"    [{count}次, {percentage:.1f}%] {sequence_str}")

    def _compare_success_vs_failed(self):
        """比较成功和失败序列的差异"""
        print(f"\n🔄 成功 vs 失败序列对比:")

        if not self.success_sequences or not self.failed_sequences:
            print("  缺少成功或失败序列数据，无法进行对比分析")
            return

        # 提取所有唯一的模板序列
        success_patterns = set()
        failed_patterns = set()

        for seq in self.success_sequences:
            pattern = " -> ".join(seq["template_sequence"])
            success_patterns.add(pattern)

        for seq in self.failed_sequences:
            pattern = " -> ".join(seq["template_sequence"])
            failed_patterns.add(pattern)

        # 分析共同模式和独有模式
        common_patterns = success_patterns & failed_patterns
        success_only = success_patterns - failed_patterns
        failed_only = failed_patterns - success_patterns

        print(f"  📊 模式统计:")
        print(f"    共同模式: {len(common_patterns)} 个")
        print(f"    仅成功模式: {len(success_only)} 个")
        print(f"    仅失败模式: {len(failed_only)} 个")

        # 显示失败独有的模式
        if failed_only:
            print(f"\n  ❌ 仅在失败中出现的模式:")
            for pattern in list(failed_only)[:5]:  # 显示前5个
                print(f"    {pattern}")

        # 显示成功独有的模式
        if success_only:
            print(f"\n  ✅ 仅在成功中出现的模式:")
            for pattern in list(success_only)[:5]:  # 显示前5个
                print(f"    {pattern}")

    def find_sequence_divergence_points(self):
        """寻找序列分歧点"""
        print(f"\n🔍 序列分歧点分析:")

        if not self.success_sequences or not self.failed_sequences:
            print("  缺少数据，无法分析分歧点")
            return

        # 寻找相似长度的成功和失败序列进行对比
        divergence_analysis = []

        for success_seq in self.success_sequences[:10]:  # 取前10个成功序列
            success_pattern = success_seq["template_sequence"]

            # 寻找最相似的失败序列
            best_match = None
            best_similarity = 0

            for failed_seq in self.failed_sequences:
                failed_pattern = failed_seq["template_sequence"]

                # 计算序列相似度
                similarity = SequenceMatcher(None, success_pattern, failed_pattern).ratio()

                if similarity > best_similarity and similarity > 0.5:  # 至少50%相似
                    best_similarity = similarity
                    best_match = failed_seq

            if best_match:
                divergence_analysis.append(
                    {
                        "success_vm": success_seq["vm_id"],
                        "failed_vm": best_match["vm_id"],
                        "similarity": best_similarity,
                        "success_sequence": success_pattern,
                        "failed_sequence": best_match["template_sequence"],
                    }
                )

        # 显示分歧分析结果
        if divergence_analysis:
            print(f"  找到 {len(divergence_analysis)} 对相似序列进行分歧分析:")

            for i, analysis in enumerate(divergence_analysis[:3], 1):  # 显示前3对
                print(f"\n  📋 分析对 {i} (相似度: {analysis['similarity']:.2f}):")
                print(f"    成功VM: {analysis['success_vm']}")
                print(f"    失败VM: {analysis['failed_vm']}")

                success_seq = analysis["success_sequence"]
                failed_seq = analysis["failed_sequence"]

                # 找到分歧点
                min_len = min(len(success_seq), len(failed_seq))
                divergence_point = min_len

                for j in range(min_len):
                    if success_seq[j] != failed_seq[j]:
                        divergence_point = j
                        break

                print(f"    分歧点: 位置 {divergence_point}")
                print(f"    共同前缀: {' -> '.join(success_seq[:divergence_point])}")

                if divergence_point < len(success_seq):
                    print(f"    成功分支: {' -> '.join(success_seq[divergence_point:])}")
                if divergence_point < len(failed_seq):
                    print(f"    失败分支: {' -> '.join(failed_seq[divergence_point:])}")

    def generate_template_mapping(self):
        """生成模板映射表"""
        print(f"\n📋 生成模板映射表...")

        template_mapping = {}

        # 收集所有模板
        all_logs = []
        for vm_id, logs in self.vm_sequences.items():
            all_logs.extend(logs)

        # 按template_id分组
        template_groups = defaultdict(list)
        for log in all_logs:
            template_groups[log["template_id"]].append(log)

        # 生成映射
        for template_id, logs in template_groups.items():
            if logs:
                template_mapping[template_id] = {
                    "template": logs[0]["template"],
                    "logstore": logs[0]["logstore"],
                    "count": len(logs),
                    "example": logs[0]["original_content"][:200] + "..." if len(logs[0]["original_content"]) > 200 else logs[0]["original_content"],
                }

        # 保存映射文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        mapping_file = f"template_mapping_{timestamp}.json"

        with open(mapping_file, "w", encoding="utf-8") as f:
            json.dump(template_mapping, f, ensure_ascii=False, indent=2)

        print(f"  💾 模板映射已保存: {mapping_file}")
        return template_mapping

    def save_sequence_analysis_results(self):
        """保存序列分析结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"sequence_analysis_results_{timestamp}.json"

        results = {
            "analysis_timestamp": timestamp,
            "total_vm_sequences": len(self.vm_sequences),
            "success_sequences_count": len(self.success_sequences),
            "failed_sequences_count": len(self.failed_sequences),
            "success_sequences": self.success_sequences,
            "failed_sequences": self.failed_sequences,
        }

        with open(results_file, "w", encoding="utf-8") as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        print(f"\n💾 序列分析结果已保存: {results_file}")

    def run_full_analysis(self):
        """运行完整的序列分析"""
        print("🚀 开始日志序列模式分析...")

        self.load_structured_logs()
        self.group_logs_by_vm_and_time()
        self.extract_action_sequences()
        self.analyze_sequence_patterns()
        self.find_sequence_divergence_points()

        template_mapping = self.generate_template_mapping()
        self.save_sequence_analysis_results()

        print("\n🎉 日志序列分析完成！")

        return {"success_sequences": self.success_sequences, "failed_sequences": self.failed_sequences, "template_mapping": template_mapping}


def main():
    """主函数"""
    import glob

    # 查找最新的结构化日志文件
    structured_files = glob.glob("structured_logs_*.json")
    if not structured_files:
        print("❌ 未找到结构化日志文件，请先运行 log_analysis_and_template_extraction.py")
        return

    latest_file = max(structured_files, key=os.path.getctime)
    print(f"📂 使用最新的结构化日志文件: {latest_file}")

    # 创建序列分析器
    analyzer = LogSequenceAnalyzer(latest_file)
    results = analyzer.run_full_analysis()

    return results


if __name__ == "__main__":
    main()
