# 日志Embedding聚类配置文件
# 使用方法: 修改下面的参数，然后运行聚类脚本

# DashScope API配置
api:
  # 在这里设置你的DashScope API Key，或使用环境变量 DASHSCOPE_API_KEY
  api_key: null  # "your-dashscope-api-key"
  model: "text-embedding-v4"
  dimension: 1024  # 可选: 512, 1024, 1536
  output_type: "dense"  # 可选: "dense", "dense&sparse"

# 数据配置
data:
  log_column: "log_content"  # 日志内容列名
  batch_size: 100  # 批处理大小，调整以平衡速度和API限制
  max_retries: 3  # API调用重试次数
  retry_delay: 1.0  # 重试延迟（秒）

# 预处理配置
preprocessing:
  enabled: true  # 是否启用日志预处理
  preprocess_before_embedding: true  # 在embedding前进行预处理
  preserve_structure: true  # 保留基本结构（如冒号、等号等）
  enable_content_replacement: true  # 启用内容替换（将显著内容替换为占位符）
  save_preprocessed_logs: true  # 保存预处理后的日志文件

# 聚类配置
clustering:
  # 选择要使用的聚类方法
  methods:
    - "kmeans"
    - "dbscan" 
    - "hierarchical"
    # - "optics"
    # - "gmm"
  
  auto_select_clusters: true  # 自动选择最优聚类数
  max_clusters: 50  # 最大聚类数限制
  
  # 特定算法参数（可选）
  kmeans:
    n_clusters: null  # null表示自动选择
    
  dbscan:
    eps: null  # null表示自动调参
    min_samples: null
    
  hierarchical:
    n_clusters: null
    linkage: "ward"

# 输出配置  
output:
  save_embeddings: true  # 保存embedding向量
  save_results: true  # 保存聚类结果
  output_dir: "clustering_results"  # 输出目录
  
  # 可视化配置
  visualization:
    enabled: true
    reduction_methods:
      - "tsne"  # t-SNE降维
      # - "pca"   # PCA降维  
      # - "umap"  # UMAP降维
    save_plots: true

# 评估配置
evaluation:
  metrics:
    - "silhouette_score"  # 轮廓系数
    - "calinski_harabasz_score"  # Calinski-Harabasz指数
    - "davies_bouldin_score"  # Davies-Bouldin指数
  
  cluster_summary:
    top_n_samples: 5  # 每个聚类显示的样本数

# 日志配置
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"