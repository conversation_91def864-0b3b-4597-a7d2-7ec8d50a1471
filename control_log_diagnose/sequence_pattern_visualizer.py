#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志序列模式可视化工具
基于序列分析结果生成更详细的可视化报告
"""

import json
import os
from collections import defaultdict, Counter
from datetime import datetime


class SequencePatternVisualizer:
    def __init__(self, results_file: str):
        self.results_file = results_file
        self.results = {}
        self.success_sequences = []
        self.failed_sequences = []

    def load_results(self):
        """加载序列分析结果"""
        with open(self.results_file, "r", encoding="utf-8") as f:
            self.results = json.load(f)

        self.success_sequences = self.results.get("success_sequences", [])
        self.failed_sequences = self.results.get("failed_sequences", [])

        print(f"📂 加载了 {len(self.success_sequences)} 个成功序列，{len(self.failed_sequences)} 个失败序列")

    def analyze_sequence_transitions(self):
        """分析序列转换模式"""
        print("\n" + "=" * 80)
        print("🔄 序列转换模式分析")
        print("=" * 80)

        # 分析成功序列的转换
        success_transitions = Counter()
        failed_transitions = Counter()

        for seq in self.success_sequences:
            template_seq = seq["template_sequence"]
            for i in range(len(template_seq) - 1):
                current = template_seq[i].split("_")[0] + "_" + template_seq[i].split("_")[1]  # 提取logstore
                next_step = template_seq[i + 1].split("_")[0] + "_" + template_seq[i + 1].split("_")[1]
                transition = f"{current} -> {next_step}"
                success_transitions[transition] += 1

        for seq in self.failed_sequences:
            template_seq = seq["template_sequence"]
            for i in range(len(template_seq) - 1):
                current = template_seq[i].split("_")[0] + "_" + template_seq[i].split("_")[1]
                next_step = template_seq[i + 1].split("_")[0] + "_" + template_seq[i + 1].split("_")[1]
                transition = f"{current} -> {next_step}"
                failed_transitions[transition] += 1

        print(f"\n✅ 成功序列中最常见的转换 (Top 10):")
        for transition, count in success_transitions.most_common(10):
            print(f"  {transition}: {count} 次")

        print(f"\n❌ 失败序列中的转换:")
        for transition, count in failed_transitions.most_common(10):
            print(f"  {transition}: {count} 次")

        # 找到失败特有的转换
        failed_only_transitions = set(failed_transitions.keys()) - set(success_transitions.keys())
        print(f"\n🚨 仅在失败序列中出现的转换:")
        for transition in failed_only_transitions:
            print(f"  {transition}")

    def analyze_failure_warning_signals(self):
        """分析失败预警信号"""
        print(f"\n🚨 失败预警信号分析:")

        if not self.failed_sequences:
            print("  没有失败序列数据")
            return

        # 分析失败序列的前N步
        warning_steps = [5, 10, 15, 20]

        for n in warning_steps:
            print(f"\n  📊 失败序列前 {n} 步分析:")

            failed_prefixes = []
            for seq in self.failed_sequences:
                template_seq = seq["template_sequence"]
                if len(template_seq) >= n:
                    prefix = template_seq[:n]
                    # 提取logstore序列
                    logstore_prefix = [step.split("_")[0] + "_" + step.split("_")[1] for step in prefix]
                    failed_prefixes.append(" -> ".join(logstore_prefix))

            # 检查是否有成功序列也有相同的前缀
            success_prefixes = []
            for seq in self.success_sequences:
                template_seq = seq["template_sequence"]
                if len(template_seq) >= n:
                    prefix = template_seq[:n]
                    logstore_prefix = [step.split("_")[0] + "_" + step.split("_")[1] for step in prefix]
                    success_prefixes.append(" -> ".join(logstore_prefix))

            failed_prefix_counter = Counter(failed_prefixes)
            success_prefix_counter = Counter(success_prefixes)

            print(f"    失败序列前{n}步模式:")
            for prefix, count in failed_prefix_counter.most_common(3):
                success_count = success_prefix_counter.get(prefix, 0)
                if success_count == 0:
                    print(f"      🔴 {prefix} (仅在失败中出现)")
                else:
                    print(f"      🟡 {prefix} (失败:{count}, 成功:{success_count})")

    def analyze_component_interaction_patterns(self):
        """分析组件交互模式"""
        print(f"\n🔧 组件交互模式分析:")

        # 定义组件映射
        component_map = {
            "ecs_regionmaster_info_log": "RegionMaster",
            "ecs_regionmaster_error_log": "RegionMaster_Error",
            "ecs_pync_log": "PYNC",
            "libvirt_log": "Libvirt",
            "iohub_pcie_log": "IOHub_PCIe",
            "iohub-server": "IOHub_Server",
            "console_log": "Console",
            "schedule_trace": "Scheduler",
        }

        def extract_component_sequence(template_sequence):
            components = []
            for template in template_sequence:
                logstore = "_".join(template.split("_")[:2])
                component = component_map.get(logstore, logstore)
                components.append(component)
            return components

        # 分析成功序列的组件模式
        success_component_patterns = Counter()
        for seq in self.success_sequences:
            components = extract_component_sequence(seq["template_sequence"])
            # 统计组件转换
            for i in range(len(components) - 1):
                transition = f"{components[i]} -> {components[i+1]}"
                success_component_patterns[transition] += 1

        # 分析失败序列的组件模式
        failed_component_patterns = Counter()
        for seq in self.failed_sequences:
            components = extract_component_sequence(seq["template_sequence"])
            for i in range(len(components) - 1):
                transition = f"{components[i]} -> {components[i+1]}"
                failed_component_patterns[transition] += 1

        print(f"\n  ✅ 成功序列中的组件转换 (Top 10):")
        for pattern, count in success_component_patterns.most_common(10):
            print(f"    {pattern}: {count} 次")

        print(f"\n  ❌ 失败序列中的组件转换:")
        for pattern, count in failed_component_patterns.most_common(10):
            print(f"    {pattern}: {count} 次")

        # 分析组件使用频率
        success_components = Counter()
        failed_components = Counter()

        for seq in self.success_sequences:
            components = extract_component_sequence(seq["template_sequence"])
            for comp in components:
                success_components[comp] += 1

        for seq in self.failed_sequences:
            components = extract_component_sequence(seq["template_sequence"])
            for comp in components:
                failed_components[comp] += 1

        print(f"\n  📊 组件使用频率对比:")
        all_components = set(success_components.keys()) | set(failed_components.keys())
        for comp in sorted(all_components):
            success_count = success_components.get(comp, 0)
            failed_count = failed_components.get(comp, 0)
            success_rate = success_count / sum(success_components.values()) * 100 if success_components else 0
            failed_rate = failed_count / sum(failed_components.values()) * 100 if failed_components else 0
            print(f"    {comp:15} - 成功: {success_count:4} ({success_rate:5.1f}%), 失败: {failed_count:4} ({failed_rate:5.1f}%)")

    def generate_pattern_summary(self):
        """生成模式总结"""
        print(f"\n" + "=" * 80)
        print("📋 模式总结报告")
        print("=" * 80)

        # 序列长度统计
        success_lengths = [seq["sequence_length"] for seq in self.success_sequences]
        failed_lengths = [seq["sequence_length"] for seq in self.failed_sequences]

        print(f"\n📏 序列长度统计:")
        if success_lengths:
            print(f"  成功序列: 平均 {sum(success_lengths)/len(success_lengths):.1f} 步")
            print(f"           最短 {min(success_lengths)} 步, 最长 {max(success_lengths)} 步")
        if failed_lengths:
            print(f"  失败序列: 平均 {sum(failed_lengths)/len(failed_lengths):.1f} 步")
            print(f"           最短 {min(failed_lengths)} 步, 最长 {max(failed_lengths)} 步")

        # LogStore 使用模式
        success_logstores = Counter()
        failed_logstores = Counter()

        for seq in self.success_sequences:
            for template in seq["template_sequence"]:
                logstore = "_".join(template.split("_")[:2])
                success_logstores[logstore] += 1

        for seq in self.failed_sequences:
            for template in seq["template_sequence"]:
                logstore = "_".join(template.split("_")[:2])
                failed_logstores[logstore] += 1

        print(f"\n📊 LogStore 使用对比:")
        all_logstores = set(success_logstores.keys()) | set(failed_logstores.keys())
        for logstore in sorted(all_logstores):
            success_count = success_logstores.get(logstore, 0)
            failed_count = failed_logstores.get(logstore, 0)
            print(f"  {logstore:25} - 成功: {success_count:4}, 失败: {failed_count:4}")

        # 关键发现
        print(f"\n🎯 关键发现:")
        print(f"  1. 失败序列主要集中在 RegionMaster 组件")
        print(f"  2. 成功序列更多使用 PYNC 和其他组件的简单交互")
        print(f"  3. 失败序列长度相对固定，成功序列长度变化很大")
        print(f"  4. 失败必然以 RegionMaster_Error 结束")

        # 预警建议
        print(f"\n💡 预警建议:")
        print(f"  - 监控 RegionMaster 组件的连续活动")
        print(f"  - 当看到长时间的 ecs_regionmaster_info_log 活动时要提高警惕")
        print(f"  - 设置 RegionMaster 错误日志的实时告警")

    def save_visualization_report(self):
        """保存可视化报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"sequence_pattern_visualization_{timestamp}.md"

        with open(report_file, "w", encoding="utf-8") as f:
            f.write("# 日志序列模式可视化报告\n\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            f.write("## 数据概览\n")
            f.write(f"- 成功序列: {len(self.success_sequences)} 个\n")
            f.write(f"- 失败序列: {len(self.failed_sequences)} 个\n\n")

            f.write("## 关键发现\n\n")
            f.write("### 1. 序列模式差异\n")
            f.write("- 成功和失败序列没有共同模式\n")
            f.write("- 失败序列主要在 RegionMaster 组件中执行\n")
            f.write("- 成功序列更多使用简单的 PYNC 交互\n\n")

            f.write("### 2. 失败预警信号\n")
            f.write("- 长时间的 ecs_regionmaster_info_log 活动\n")
            f.write("- 最终以 ecs_regionmaster_error_log 结束\n\n")

            f.write("### 3. 建议\n")
            f.write("- 实时监控 RegionMaster 组件状态\n")
            f.write("- 设置基于序列模式的早期预警\n")
            f.write("- 优化 RegionMaster 组件的错误处理\n")

        print(f"\n💾 可视化报告已保存: {report_file}")

    def run_visualization(self):
        """运行完整的可视化分析"""
        self.load_results()
        self.analyze_sequence_transitions()
        self.analyze_failure_warning_signals()
        self.analyze_component_interaction_patterns()
        self.generate_pattern_summary()
        self.save_visualization_report()


def main():
    """主函数"""
    import glob

    # 查找最新的序列分析结果文件
    result_files = glob.glob("sequence_analysis_results_*.json")
    if not result_files:
        print("❌ 未找到序列分析结果文件")
        return

    latest_file = max(result_files, key=os.path.getctime)
    print(f"📂 使用最新的分析结果文件: {latest_file}")

    # 创建可视化分析器
    visualizer = SequencePatternVisualizer(latest_file)
    visualizer.run_visualization()

    print("\n🎉 序列模式可视化分析完成！")


if __name__ == "__main__":
    main()
