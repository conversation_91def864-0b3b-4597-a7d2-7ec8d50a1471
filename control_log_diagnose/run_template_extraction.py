#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行日志模板提取的示例脚本
"""

import os
from control_log_diagnose.src.template_extractor import LogTemplateExtractor


def main():
    # 设置数据路径
    BASE_PATH = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    data_path = os.path.join(BASE_PATH, "data/control_log/processed_data")

    # 初始化提取器
    extractor = LogTemplateExtractor()

    # 运行测试
    # print("首先运行测试...")
    # extractor.test_template_matching()

    # 执行模板提取
    print("\n开始模板提取...")

    # 可以指定处理特定的logstore
    # logstores = ["your_logstore_name"]
    logstores = ["console_log"]  # 处理所有logstore

    stats = extractor.extract_templates(
        data_path=data_path,
        output_dir=None,  # 使用默认输出目录
        logstores=logstores,
        max_iterations=5,  # 减少迭代次数进行快速测试
        verbose=True,  # 显示详细信息
        enable_content_replacement=True,  # 启用内容替换
        enable_preprocessing=True,  # 启用日志预处理
        compare_methods=False,  # 不进行方法对比
    )

    print(f"\n{'='*50}")
    print("模板提取完成！")
    print("\n总体统计:")
    for logstore, stat in stats.items():
        match_rate = stat["matched_logs"] / stat["total_logs"] * 100 if stat["total_logs"] > 0 else 0
        print(f"{logstore}: {stat['matched_logs']}/{stat['total_logs']} ({match_rate:.1f}%)")


if __name__ == "__main__":
    main()
