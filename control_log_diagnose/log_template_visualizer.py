#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志模板可视化和分析工具
专注于生成更清晰的日志模板和分析报告
"""

import json
import os
import re
from collections import defaultdict, Counter
from typing import Dict, List, Set, Tuple
import pandas as pd
from datetime import datetime


class LogTemplateVisualizer:
    def __init__(self, structured_logs_file: str):
        self.structured_logs_file = structured_logs_file
        self.structured_logs = []
        self.logstore_templates = defaultdict(lambda: defaultdict(list))
        self.common_patterns = defaultdict(list)

    def load_structured_logs(self):
        """加载结构化日志数据"""
        with open(self.structured_logs_file, "r", encoding="utf-8") as f:
            self.structured_logs = json.load(f)
        print(f"加载了 {len(self.structured_logs)} 条结构化日志")

    def analyze_log_patterns(self):
        """分析日志模式"""
        print("开始分析日志模式...")

        # 按 logstore 和 action_type 分组
        grouped_logs = defaultdict(lambda: defaultdict(list))
        for log in self.structured_logs:
            logstore = log.get("logstore", "unknown")
            action_type = log.get("action_type", "unknown")
            content = log.get("original_content", "")
            if content:
                grouped_logs[logstore][action_type].append(content)

        # 为每个组生成模板
        for logstore in grouped_logs:
            for action_type in grouped_logs[logstore]:
                contents = grouped_logs[logstore][action_type]
                templates = self._extract_templates_from_contents(contents)
                self.logstore_templates[logstore][action_type] = templates

    def _extract_templates_from_contents(self, contents: List[str]) -> List[Dict]:
        """从日志内容中提取模板"""
        if not contents:
            return []

        # 统计相同模式的频率
        pattern_counts = Counter()
        pattern_examples = {}

        for content in contents[:100]:  # 限制处理数量以提高性能
            template = self._create_template(content)
            pattern_counts[template] += 1
            if template not in pattern_examples:
                pattern_examples[template] = content

        # 返回按频率排序的模板
        templates = []
        for template, count in pattern_counts.most_common(10):
            templates.append({"template": template, "count": count, "percentage": (count / len(contents)) * 100, "example": pattern_examples[template]})

        return templates

    def _create_template(self, content: str) -> str:
        """创建日志模板"""
        template = content

        # 定义替换规则（顺序很重要）
        replacements = [
            # 特定的阿里云实例ID模式
            (r"\beci-[a-zA-Z0-9]{16,20}\b", "{INSTANCE_ID}"),
            (r"\bi-[a-zA-Z0-9]{17}\b", "{INSTANCE_ID}"),
            (r"\beni-[a-zA-Z0-9]{17}\b", "{ENI_ID}"),
            # UUID 模式
            (r"\b[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}\b", "{UUID}"),
            # IP地址
            (r"\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b", "{IP}"),
            # 时间戳
            (r"\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}", "{TIMESTAMP}"),
            (r"\d{4}/\d{2}/\d{2} \d{2}:\d{2}:\d{2}", "{TIMESTAMP}"),
            # 数字模式
            (r"\b\d+\.\d+\.\d+\b", "{VERSION}"),  # 版本号
            (r"\bcost[=:]\s*\d+", "cost: {TIME}"),  # 耗时
            (r"\b\d+ms\b", "{TIME_MS}"),
            (r"\b\d+ns\b", "{TIME_NS}"),
            (r"\b\d{10,13}\b", "{TIMESTAMP_NUM}"),  # Unix时间戳
            (r"\b\d+\b", "{NUM}"),  # 普通数字
            # 十六进制数
            (r"\b0x[a-fA-F0-9]+\b", "{HEX}"),
            # 路径和文件名
            (r"/[a-zA-Z0-9_/.-]+\.[a-zA-Z0-9]+", "{FILE_PATH}"),
            (r"pangu://[a-zA-Z0-9_/.-]+", "{PANGU_PATH}"),
            (r"oss://[a-zA-Z0-9_/.-]+", "{OSS_PATH}"),
            # MAC地址
            (r"\b([0-9a-fA-F]{2}:){5}[0-9a-fA-F]{2}\b", "{MAC}"),
            # 密码和敏感信息
            (r'password["\']?\s*[:=]\s*["\'][^"\']+["\']', "password: {HIDDEN}"),
            (r'access_key["\']?\s*[:=]\s*["\'][^"\']+["\']', "access_key: {HIDDEN}"),
            # Java异常堆栈中的行号
            (r"\.java:\d+\)", ".java:{LINE})"),
            # 长字符串和签名
            (r"\b[A-Za-z0-9+/]{50,}=*\b", "{SIGNATURE}"),
        ]

        for pattern, replacement in replacements:
            template = re.sub(pattern, replacement, template)

        return template

    def generate_summary_report(self):
        """生成摘要报告"""
        print("\n" + "=" * 80)
        print("📊 日志模板分析摘要报告")
        print("=" * 80)

        # 统计总览
        total_logs = len(self.structured_logs)
        logstore_count = len(self.logstore_templates)

        print(f"\n📈 总览统计:")
        print(f"  - 总日志条数: {total_logs:,}")
        print(f"  - 日志存储类型: {logstore_count}")

        # 按logstore统计
        logstore_stats = defaultdict(int)
        action_type_stats = defaultdict(int)

        for log in self.structured_logs:
            logstore_stats[log.get("logstore", "unknown")] += 1
            action_type_stats[log.get("action_type", "unknown")] += 1

        print(f"\n📊 按LogStore分布:")
        for logstore, count in sorted(logstore_stats.items(), key=lambda x: x[1], reverse=True):
            percentage = (count / total_logs) * 100
            print(f"  - {logstore}: {count:,} ({percentage:.1f}%)")

        print(f"\n🎯 按操作类型分布:")
        for action_type, count in sorted(action_type_stats.items(), key=lambda x: x[1], reverse=True):
            percentage = (count / total_logs) * 100
            print(f"  - {action_type}: {count:,} ({percentage:.1f}%)")

    def generate_template_report(self):
        """生成详细的模板报告"""
        print("\n" + "=" * 80)
        print("🔍 详细日志模板分析")
        print("=" * 80)

        for logstore in sorted(self.logstore_templates.keys()):
            print(f"\n📁 LogStore: {logstore}")
            print("-" * 60)

            for action_type in sorted(self.logstore_templates[logstore].keys()):
                templates = self.logstore_templates[logstore][action_type]
                if not templates:
                    continue

                print(f"\n  🚀 Action: {action_type}")

                for i, template_info in enumerate(templates[:5], 1):  # 显示前5个模板
                    template = template_info["template"]
                    count = template_info["count"]
                    percentage = template_info["percentage"]
                    example = template_info["example"]

                    print(f"    {i}. [{count:,}次, {percentage:.1f}%] {template[:100]}{'...' if len(template) > 100 else ''}")
                    if i == 1:  # 只显示第一个模板的示例
                        print(f"       示例: {example[:150]}{'...' if len(example) > 150 else ''}")

                if len(templates) > 5:
                    print(f"    ... 还有 {len(templates) - 5} 个模板")

    def extract_key_patterns(self):
        """提取关键模式"""
        print("\n" + "=" * 80)
        print("🔑 关键模式分析")
        print("=" * 80)

        # 分析错误模式
        error_patterns = []
        success_patterns = []

        for log in self.structured_logs:
            content = log.get("original_content", "").lower()
            action_type = log.get("action_type", "")

            if "failed" in action_type or "error" in content:
                error_patterns.append(self._create_template(log.get("original_content", "")))
            elif "success" in action_type:
                success_patterns.append(self._create_template(log.get("original_content", "")))

        # 统计错误模式
        error_counter = Counter(error_patterns)
        success_counter = Counter(success_patterns)

        print(f"\n❌ 常见错误模式 (Top 10):")
        for pattern, count in error_counter.most_common(10):
            percentage = (count / len(error_patterns)) * 100 if error_patterns else 0
            print(f"  [{count:,}次, {percentage:.1f}%] {pattern[:120]}{'...' if len(pattern) > 120 else ''}")

        print(f"\n✅ 常见成功模式 (Top 10):")
        for pattern, count in success_counter.most_common(10):
            percentage = (count / len(success_patterns)) * 100 if success_patterns else 0
            print(f"  [{count:,}次, {percentage:.1f}%] {pattern[:120]}{'...' if len(pattern) > 120 else ''}")

    def extract_timing_patterns(self):
        """提取时间相关模式"""
        print("\n" + "=" * 80)
        print("⏱️ 时间和性能模式分析")
        print("=" * 80)

        timing_patterns = []
        cost_values = []

        for log in self.structured_logs:
            content = log.get("original_content", "")

            # 提取cost信息
            cost_match = re.search(r"cost[=:\s]*(\d+)", content.lower())
            if cost_match:
                cost_values.append(int(cost_match.group(1)))

            # 提取包含时间信息的日志
            if re.search(r"cost|time|duration|ms|ns|timeout", content.lower()):
                timing_patterns.append(self._create_template(content))

        # 统计时间模式
        timing_counter = Counter(timing_patterns)

        print(f"\n⏰ 时间相关模式 (Top 10):")
        for pattern, count in timing_counter.most_common(10):
            percentage = (count / len(timing_patterns)) * 100 if timing_patterns else 0
            print(f"  [{count:,}次, {percentage:.1f}%] {pattern[:120]}{'...' if len(pattern) > 120 else ''}")

        if cost_values:
            cost_values.sort()
            print(f"\n📊 Cost 值统计:")
            print(f"  - 最小值: {min(cost_values)}")
            print(f"  - 最大值: {max(cost_values)}")
            print(f"  - 平均值: {sum(cost_values) / len(cost_values):.2f}")
            print(f"  - 中位数: {cost_values[len(cost_values)//2]}")

    def generate_structured_template_file(self, output_file: str):
        """生成结构化的模板文件"""
        template_data = {"generated_at": datetime.now().isoformat(), "total_logs": len(self.structured_logs), "logstore_templates": {}}

        for logstore in self.logstore_templates:
            template_data["logstore_templates"][logstore] = {}
            for action_type in self.logstore_templates[logstore]:
                templates = self.logstore_templates[logstore][action_type]
                template_data["logstore_templates"][logstore][action_type] = templates

        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(template_data, f, ensure_ascii=False, indent=2)

        print(f"\n💾 结构化模板文件已保存: {output_file}")

    def run_analysis(self):
        """运行完整分析"""
        self.load_structured_logs()
        self.analyze_log_patterns()
        self.generate_summary_report()
        self.generate_template_report()
        self.extract_key_patterns()
        self.extract_timing_patterns()

        # 生成结构化模板文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        template_file = f"structured_templates_{timestamp}.json"
        self.generate_structured_template_file(template_file)


def main():
    """主函数"""
    import glob

    # 查找最新的结构化日志文件
    structured_files = glob.glob("structured_logs_*.json")
    if not structured_files:
        print("❌ 未找到结构化日志文件，请先运行 log_analysis_and_template_extraction.py")
        return

    latest_file = max(structured_files, key=os.path.getctime)
    print(f"📂 使用最新的结构化日志文件: {latest_file}")

    # 创建可视化分析器
    visualizer = LogTemplateVisualizer(latest_file)
    visualizer.run_analysis()

    print("\n🎉 日志模板可视化分析完成！")


if __name__ == "__main__":
    main()
