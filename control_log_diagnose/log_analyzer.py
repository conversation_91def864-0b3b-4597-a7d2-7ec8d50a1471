# %%
import os
import sys

import json

import pandas as pd
import numpy as np
import yaml

BASE_PATH = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if BASE_PATH not in sys.path:
    sys.path.append(BASE_PATH)

data_path = os.path.join(BASE_PATH, "data/control_log/reformatted_logs.jsonl")


def load_reformatted_logs(data_path):
    """加载重新格式化的日志数据"""
    logs = []
    if os.path.exists(data_path):
        with open(data_path, "r", encoding="utf-8") as f:
            for line in f:
                if line.strip():
                    try:
                        log_entry = json.loads(line.strip())
                        logs.append(log_entry)
                    except json.JSONDecodeError as e:
                        print(f"JSON解析错误: {e}")
    else:
        print(f"文件不存在: {data_path}")
    return logs


log_data = load_reformatted_logs(data_path)

# %%
logstores = set()
for entry in log_data:
    if "logstore" in entry:
        keys = list(entry["logstore"].keys())
        logstores.update(keys)

# %%
# Merge logs with labels

extract_config_path = os.path.join(BASE_PATH, "control_log_diagnose/config/log_extraction_config.yaml")
with open(extract_config_path, "r", encoding="utf-8") as f:
    extract_config = yaml.safe_load(f)


def parse_action_time(action_time):
    """解析并转换行为时间为时间戳"""
    if not isinstance(action_time, str):
        return np.nan
    try:
        return pd.to_datetime(action_time).timestamp()
    except ValueError:
        return np.nan


def extract_meta_info(entry):
    """提取日志条目的元信息"""
    meta = entry.get("meta", {})
    action_type = meta.get("action_type", "unknown")
    action_label = -1
    if action_type == "start_vm_success":
        action_label = 0
    elif action_type == "stop_vm_failed":
        action_label = 1
    instance_id = meta.get("instance_id", "unknown")
    action_time = meta.get("action_time", "unknown")
    action_timestamp = parse_action_time(action_time)
    request_id = meta.get("request_id", "unknown")
    return {
        "action_type": action_type,
        "action_label": action_label,
        "instance_id": instance_id,
        "action_time": action_time,
        "action_timestamp": action_timestamp,
        "request_id": request_id,
    }


def get_logstore_config(extract_config, logstore):
    """获取logstore的配置信息"""
    config = extract_config.get("logstore_configs", {}).get(logstore, {})
    return {"content_field": config.get("content_field"), "additional_fields": config.get("additional_fields", [])}


def create_log_item(log, config, meta_info):
    """创建单个日志项"""
    content_field = config["content_field"]

    if content_field not in log:
        return None

    log_item = {"content": log[content_field]}

    # 添加额外字段
    for field in config["additional_fields"]:
        log_item[field] = log.get(field)

    # 添加基础字段
    log_item.update(
        {
            "request_id": meta_info.get("request_id", "unknown"),
            "log_time": log.get("log_time", 0),
            "label": meta_info["action_label"],
            "timestamp": int(meta_info["action_timestamp"]) if not np.isnan(meta_info["action_timestamp"]) else 0,
        }
    )

    return log_item


def process_logstore_data(logstore_data, logstore, config, meta_info):
    """处理单个logstore的数据"""
    if logstore not in logstore_data:
        return []

    processed_logs = []
    for log in logstore_data[logstore]:
        log_item = create_log_item(log, config, meta_info)
        if log_item:
            processed_logs.append(log_item)

    return processed_logs


from tqdm import tqdm

merged_dict = {}
for logstore in logstores:
    merged_dict[logstore] = []
for entry in tqdm(log_data):
    meta_info = extract_meta_info(entry)
    logstore_data = entry.get("logstore", {})

    for logstore in logstores:
        config = get_logstore_config(extract_config, logstore)
        processed_logs = process_logstore_data(logstore_data, logstore, config, meta_info)
        # print(processed_logs.__len__())
        merged_dict[logstore].extend(processed_logs)

    logcluster_data = entry.get("log_cluster", {})
    # print(logcluster_data)

    for logstore in logstores:
        config = {"content_field": "content", "additional_fields": ["log_time"]}
        processed_logs = process_logstore_data(logcluster_data, logstore, config, meta_info)
        # print(logstore, processed_logs.__len__())
        merged_dict[logstore].extend(processed_logs)

    # if logcluster_data.__len__() > 0:
    #     break

# %%
for logstore, logs in merged_dict.items():
    df = pd.DataFrame(logs)
    if df.shape[0] == 0:
        print(f"No logs found for logstore: {logstore}")
        continue
    df.to_parquet(
        os.path.join(BASE_PATH, f"data/control_log/processed_data/{logstore}.parquet"),
        index=False,
        engine="pyarrow",
    )
    df.to_csv(
        os.path.join(BASE_PATH, f"data/control_log/processed_data/{logstore}.csv"),
        index=False,
        encoding="utf-8",
    )

    print(f"Save: {logstore} logs to parquet and csv files.")
    print(f"Total records in {logstore}: {df.shape[0]}")
