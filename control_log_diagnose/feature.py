import os
import sys
import logging

BASE_PATH = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if BASE_PATH not in sys.path:
    sys.path.append(BASE_PATH)

from control_log_diagnose.log_processor import main
from control_log_diagnose.log_analyzer import <PERSON>gAnalyzer
from control_log_diagnose.log_data_processor import LogDataProcessor


def run_analysis():
    """运行日志分析"""
    logger = logging.getLogger(__name__)
    logger.info("开始日志分析...")
    analyzer = LogAnalyzer()

    if analyzer.load_data():
        analyzer.print_statistics()
    else:
        logger.error("Failed to load data for analysis")


def run_data_processing():
    """运行数据处理"""
    logger = logging.getLogger(__name__)
    logger.info("开始数据处理...")
    processor = LogDataProcessor()
    processor.main()


def main_with_options(verbose: bool = True, max_workers: int = 10, run_processing: bool = False):
    """带选项的主函数"""
    # 设置日志记录
    logging.basicConfig(
        level=logging.INFO if verbose else logging.WARNING,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[logging.StreamHandler(), logging.FileHandler(os.path.join(BASE_PATH, "logs_output", "feature.log"), encoding="utf-8")],
    )
    logger = logging.getLogger(__name__)

    logger.info("开始运行日志处理流程...")

    # 运行日志处理
    main(verbose=verbose, max_workers=max_workers)

    # 运行日志分析
    run_analysis()

    # 如果需要，运行数据处理
    if run_processing:
        run_data_processing()

    logger.info("日志处理流程完成")


if __name__ == "__main__":
    # 默认以详细模式运行
    main_with_options(verbose=True, run_processing=True)
