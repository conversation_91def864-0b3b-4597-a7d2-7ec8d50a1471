# AI Coding Agent Instructions

## Project Overview

This is a **log analysis and machine learning algorithm workspace** consisting of two main projects:

1. **Control Log Diagnosis** (`control_log_diagnose/`) - ECS VM startup/shutdown failure diagnosis through log pattern analysis
2. **Change Correlation** (`change_correlation/`) - ML-based correlation prediction between deployments and anomalies

## Architecture & Data Flow

### Control Log Diagnosis Pipeline

```
SLS Log Sources → Data Collection → Pattern Extraction → Analysis → Results
```

**Key Components:**

- **`connector/sls.py`** - SLS (Simple Log Service) client for multi-region log retrieval using different AK/SK pairs
- **`control_log_diagnose/log_processor_parallel2.py`** - Main parallel log processor with async data collection
- **`control_log_diagnose/template_extractor.py`** - Log template extraction using Drain algorithm + LLM enhancement
- **`control_log_diagnose/src/log_analyzer.py`** - Log structuring and labeling (success=0, failure=1)

**Data Structure:**

- Raw logs → `data/control_log/reformatted_logs.jsonl` (structured by request_id)
- Processed → `data/control_log/processed_data/{logstore}.parquet` (labeled, timestamped)
- Templates → `data/control_log/template_results/` and `drain_pattern_results/`

### Change Correlation Pipeline

```
ODPS Data → Feature Engineering → ML Training → Prediction Model
```

**Key Components:**

- **`connector/download.py`** - ODPS downloader with resume capability and chunked processing
- **`change_correlation/src/feature.py`** - Advanced feature engineering (time, stats, semantic, LLM-enhanced)
- **`change_correlation/src/correlation_predictor.py`** - ML predictor with high performance (AUC: 0.9985)

## Essential Conventions

### Environment Setup

- Uses conda environment `algo` (see `environment.yml`)
- Python environment auto-configuration via `configure_python_environment` tool
- SLS credentials in `.env`: `XUNJIAN_AK/SK`, `151_AK/SK`

### Configuration Patterns

- **LogStore configs**: `control_log_diagnose/config/log_extraction_config.yaml` - defines content_field and additional_fields per logstore
- **Region mapping**: `control_log_diagnose/sls_region_conf.json` - maps regions to SLS endpoints
- **Project mapping**: `control_log_diagnose/region_project.json` - handles multi-region project routing

### Critical Data Structures

```python
# Log processing task
@dataclass
class LogTask:
    request_id: str
    instance_id: str
    region_name: str
    action_time: str
    nc_ip: str
    action_type: str  # start_vm_success/stop_vm_failed
    start_time: float
    end_time: float
```

### Running Key Workflows

**Log Processing (Main Task):**

```bash
conda run -n algo python control_log_diagnose/log_processor_parallel2.py
```

**Template Extraction:**

```bash
python control_log_diagnose/run_template_extraction.py
```

**Feature Engineering:**

```python
from change_correlation.src.feature import FeatureEngineer
engineer = FeatureEngineer()
enhanced_data = engineer.generate_feature()
```

## Integration Points

### SLS Multi-Region Access

- **Project routing**: Different projects use different AK/SK (xunjian vs 151)
- **Endpoint mapping**: Region-specific SLS endpoints in `sls_region_conf.json`
- **Query generation**: Dynamic LogStore queries based on request_id reliability levels (full/half/none)

### LLM Integration

- **DashScope API** for semantic analysis and template generation
- **Template extraction** combines Drain algorithm with LLM refinement
- **Feature enhancement** uses LLM for semantic similarity and content expansion

### Data Processing Patterns

- **Async processing** with ThreadPoolExecutor for parallel log collection
- **Chunked downloads** from ODPS with progress persistence
- **Incremental processing** with resumable sessions

## Key File Patterns

**Configuration Management:**

- YAML configs for log extraction rules per logstore
- JSON for region/project mappings
- Environment variables for credentials

**Data Processing:**

- DataFrames → Parquet for structured storage
- JSONL for raw log streaming
- CSV exports for analysis tools

**Error Handling:**

- Retry mechanisms with exponential backoff for SLS connections
- Progress persistence for long-running downloads
- Graceful degradation when logstores are unavailable

## Debugging

**Log Collection Issues:**

- Check SLS credentials and region endpoint mappings
- Verify logstore configurations in `log_extraction_config.yaml`
- Monitor connection retries in SLS client

**Template Extraction:**

- Use `verbose=True` in template extractor for detailed output
- Check Drain algorithm parameters in `drain3.ini`
- Validate LLM API connectivity for DashScope

**Model Performance:**

- Feature importance analysis in correlation predictor
- Cross-validation metrics tracking
- Case analysis tools in `experiments/` directory
