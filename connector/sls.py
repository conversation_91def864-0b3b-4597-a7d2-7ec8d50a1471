from datetime import datetime
from aliyun.log import LogClient, GetLogsRequest, GetHistogramsRequest
from aliyun.log.logexception import LogException
from time import time, sleep
import os
from dotenv import load_dotenv
import yaml
import pandas as pd

# Load environment variables from .env file
load_dotenv()

XUNJIAN_AK = os.getenv("XUNJIAN_AK")
XUNJIAN_SK = os.getenv("XUNJIAN_SK")

P151_AK = os.getenv("151_AK")
P151_SK = os.getenv("151_SK")


def get_related_logs(
    project,
    logstore,
    start_time,
    end_time,
    query_str="",
    endpoint=None,
    max_retries=3,
    retry_delay=1,
):
    # If endpoint is provided, use it; otherwise, use the default logic
    if endpoint is not None:
        if project in ("ecs-xunjian", "ecs-xunjian2", "151"):
            ak = XUNJIAN_AK if project in ("ecs-xunjian", "ecs-xunjian2") else P151_AK
            sk = XUNJIAN_SK if project in ("ecs-xunjian", "ecs-xunjian2") else P151_SK
        else:
            ak = XUNJIAN_AK
            sk = XUNJIAN_SK
    else:
        endpoint = "http://cn-hangzhou-corp.sls.aliyuncs.com"
        if project in ("ecs-xunjian"):
            ak = XUNJIAN_AK
            sk = XUNJIAN_SK
            endpoint = "http://cn-hangzhou-corp.sls.aliyuncs.com"
        elif project in ("ecs-xunjian2"):
            ak = XUNJIAN_AK
            sk = XUNJIAN_SK
            endpoint = "cn-wulanchabu-share.log.aliyuncs.com"
        elif project in ("151"):
            ak = P151_AK
            sk = P151_SK
            endpoint = "http://cn-hangzhou-corp.sls.aliyuncs.com"
        else:
            ak = XUNJIAN_AK
            sk = XUNJIAN_SK
            endpoint = "cn-zhangjiakou-2-share.log.aliyuncs.com"

    # 重试机制
    for attempt in range(max_retries + 1):
        try:
            client = LogClient(endpoint, ak, sk)
            request = GetLogsRequest(
                project,
                logstore,
                fromTime=start_time,
                toTime=end_time,
                topic="",
                query=query_str,
                line=10000,
                offset=0,
                reverse=False,
            )

            # hist = GetHistogramsRequest(
            #     project,
            #     logstore,
            #     fromTime=start_time,
            #     toTime=end_time,
            #     topic="",
            #     query=query_str,
            # )
            # hist = client.get_histograms(hist)
            # count = hist.count

            res = client.get_logs(request)
            logs = res.get_logs()

            return logs
        except LogException as e:
            if attempt < max_retries and ("Connection reset by peer" in str(e) or "Connection aborted" in str(e)):
                print(f"Warning: Connection error occurred (attempt {attempt + 1}/{max_retries + 1}): {e}")
                sleep(retry_delay * (2 ** attempt))  # 指数退避
                continue
            else:
                print(f"Error: Failed to retrieve logs after {max_retries + 1} attempts: {e}")
                return []
        except Exception as e:
            print(f"Error: Unexpected error occurred: {e}")
            return []
    
    return []


if __name__ == "__main__":
    config = yaml.safe_load(open("control_log_diagnose/logstore_config.yaml", "r"))
    records = pd.read_csv("data/control_log/start_vm_records/start_vm_success.csv")  # merged_start_vm_records

    for index, row in records.iterrows():
        action_type = row["action_type"]
        request_id = row["request_id"]
        instance_id = row["instance_id"]
        region_name = row["region_name"]
        action_time = gmt_create = row["gmt_create"]
        request_id = row["request_id"]
        nc_ip = row["nc_ip"]
        status = row.get("status", "none")
        start_time = datetime.strptime(gmt_create, "%Y-%m-%d %H:%M:%S").timestamp() - 60 * 5  # 5 minutes before the action time
        end_time = start_time + 60 * 10  # 10 minutes later
        result = {
            "logstore": {},
            "log_cluster": {},
        }
        for item in config.get("logstores"):
            query_str = "*"
            key_fields = item.get("key_fields", [])
            if item.get("request_id", "none") == "none":
                query_str += f" | set session parallel_sql=true; select * where {key_fields[0]} LIKE '%{instance_id}%' limit 10000"
            else:
                if item["logstore"] == "schedule_trace":
                    query_str += f" and requestId: '{request_id}'"
                else:
                    query_str += f" and request_id: '{request_id}'"
                pass

            logs = get_related_logs(
                item["project"],
                item["logstore"],
                start_time,
                end_time,
                query_str=query_str,
            )

            if logs:
                print(f"{action_time} Found logs for {request_id} in {item['project']}/{item['logstore']}")
                result["logstore"][item["logstore"]] = logs
            else:
                print(f"{action_time} No logs found for {request_id} in {item['project']}/{item['logstore']}")
                result["logstore"][item["logstore"]] = []
            continue

        if instance_id.startswith("i-"):
            print(f"Processing instance {instance_id} with action type {action_type}")
            # Here you can add further processing logic for the logs if needed

        for item, value in config.get("log_clusters").items():
            query = "*"
            if item.startswith("ecs_region_master"):
                query += f" | set session parallel_sql=true; select * where content LIKE '%{instance_id}%' limit 10000"
            else:
                query += f" AND nc: {nc_ip} | set session parallel_sql=true; select * where content LIKE '%{request_id}%' OR content LIKE '%{instance_id}%' limit 10000"

            logs = get_related_logs(
                "log-cluster",
                value,
                start_time,
                end_time,
                query_str=query,
            )
            if logs:
                print(f"{action_time} Found logs for {request_id} in log-cluster/{value}")
                result["log_cluster"][item] = logs
            else:
                print(f"{action_time} No logs found for {request_id} in log-cluster/{value}")
                result["log_cluster"][item] = []

        break
