import json
import os
import uuid
from odps import ODPS
from odps.tunnel import TableTunnel
import pandas as pd


BASE_PATH = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))


class ODPSDownloader:
    def __init__(self, config):
        self.config = config
        self.odps = ODPS(self.config["access_id"], self.config["access_key"], self.config["project"], self.config["endpoint"])
        self.progress_file = self.config.get("progress_file", "download_progress.json")
        self.temp_table = None
        self.downloaded = 0
        self.total_records = 0
        self.download_session = None

    def _save_progress(self):
        """保存下载进度到文件"""
        progress = {"downloaded": self.downloaded, "temp_table": self.temp_table}
        with open(self.progress_file, "w") as f:
            json.dump(progress, f)

    def _load_progress(self):
        """加载下载进度"""
        if os.path.exists(self.progress_file):
            with open(self.progress_file, "r") as f:
                progress = json.load(f)
                self.downloaded = progress["downloaded"]
                self.temp_table = progress["temp_table"]
                return True
        return False

    def _execute_sql_to_temp_table(self, sql_query=None):
        """执行SQL并将结果保存到临时表"""
        query = sql_query or self.config["sql_query"]
        self.temp_table = f"tmp_{uuid.uuid4().hex}"
        sql = f"CREATE TABLE {self.temp_table} LIFECYCLE 1 AS {query}"
        print(f"Creating temporary table {self.temp_table}...")
        self.odps.execute_sql(sql).wait_for_success()
        print("Temporary table created successfully.")

    def _initialize_download(self, sql_query=None):
        """初始化下载会话"""
        if not self._load_progress():
            self._execute_sql_to_temp_table(sql_query)
            self._save_progress()
        else:
            print(f"Resuming from temporary table {self.temp_table}...")

        tunnel = TableTunnel(self.odps)
        self.download_session = tunnel.create_download_session(self.temp_table)
        self.total_records = self.download_session.count

    def _cleanup(self):
        """清理临时资源"""
        try:
            if self.temp_table and self.odps.exist_table(self.temp_table):
                print(f"Dropping temporary table {self.temp_table}...")
                self.odps.delete_table(self.temp_table)
        except Exception as e:
            print(f"Cleanup failed: {str(e)}")
        if os.path.exists(self.progress_file):
            os.remove(self.progress_file)

    def download(self):
        """执行下载流程"""
        try:
            self._initialize_download()
            print(f"Total records to download: {self.total_records}")

            # 创建CSV文件并写入表头
            # if not os.path.exists(os.path.join(BASE_PATH, self.config["output_file"])):
            #     with self.download_session.open_record_reader(0, 0) as reader:
            #         columns = reader.schema.names
            #         pd.DataFrame(columns=columns).to_csv(os.path.join(BASE_PATH, self.config["output_file"]), index=False)

            chunk_size = self.config["chunk_size"]
            while self.downloaded < self.total_records:
                start = self.downloaded
                end = min(start + chunk_size, self.total_records)
                print(f"Downloading records {start}-{end}...")

                with self.download_session.open_record_reader(start, end - start) as reader:
                    records = []
                    for record in reader:
                        records.append(record.values)

                    df = pd.DataFrame(records, columns=reader.schema.names)
                    if start == 0:
                        df.to_csv(os.path.join(BASE_PATH, self.config["output_file"]), index=False)
                    else:
                        df.to_csv(os.path.join(BASE_PATH, self.config["output_file"]), mode="a", header=False, index=False)

                self.downloaded = end
                self._save_progress()
                print(f"Progress saved: {self.downloaded}/{self.total_records}")

        except Exception as e:
            print(f"Download failed: {str(e)}")
            raise
        finally:
            if self.downloaded >= self.total_records:
                self._cleanup()
                print("Download completed and resources cleaned up.")

    def download_with_custom_params(self, sql_query, output_dir=None, output_filename=None, chunk_size=None):
        """
        使用自定义参数执行下载流程

        Args:
            sql_query (str): 要执行的SQL查询语句
            output_dir (str, optional): 输出目录路径，默认为项目根目录
            output_filename (str, optional): 输出文件名，默认为 'custom_download.csv'
            chunk_size (int, optional): 每次下载的记录数，默认使用配置中的值
        """
        # 设置默认值
        if output_dir is None:
            output_dir = BASE_PATH
        if output_filename is None:
            output_filename = "custom_download.csv"
        if chunk_size is None:
            chunk_size = self.config.get("chunk_size", 10000)

        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)

        # 构建完整的输出文件路径
        output_path = os.path.join(output_dir, output_filename)

        # 重置下载状态
        self.downloaded = 0
        self.temp_table = None

        try:
            self._initialize_download(sql_query)
            print(f"Total records to download: {self.total_records}")
            print(f"Output file: {output_path}")

            while self.downloaded < self.total_records:
                start = self.downloaded
                end = min(start + chunk_size, self.total_records)
                print(f"Downloading records {start}-{end}...")

                with self.download_session.open_record_reader(start, end - start) as reader:
                    records = []
                    for record in reader:
                        records.append(record.values)

                    df = pd.DataFrame(records, columns=reader.schema.names)
                    if start == 0:
                        df.to_csv(output_path, index=False)
                    else:
                        df.to_csv(output_path, mode="a", header=False, index=False)

                self.downloaded = end
                self._save_progress()
                print(f"Progress saved: {self.downloaded}/{self.total_records}")

        except Exception as e:
            print(f"Download failed: {str(e)}")
            raise
        finally:
            if self.downloaded >= self.total_records:
                self._cleanup()
                print(f"Download completed and resources cleaned up.")
                print(f"File saved to: {output_path}")


# 示例配置
config = {
    "access_id": "LTAI5tHSnP1rfc5Nne9TtHPL",
    "access_key": "******************************",
    "project": "ecs_dw",
    "endpoint": "http://service-corp.odps.aliyun-inc.com/api",
    "sql_query": """
    """,
    "output_file": "log_distribution.csv",
    "chunk_size": 10000,
    "progress_file": "download_progress.json",
}

if __name__ == "__main__":
    # 使用原始配置的示例
    # downloader = ODPSDownloader(config)
    # downloader.download()

    # 使用自定义参数的示例
    downloader = ODPSDownloader(config)
    custom_sql = "SELECT * FROM ecs_dw.vm_control_info_of_regionmaster_log WHERE ds = '20250720' "
    downloader.download_with_custom_params(
        sql_query=custom_sql,
        output_dir="/Users/<USER>/GitFolders/algorithm/data/control_log",
        output_filename="vm_control_info_of_regionmaster_log.csv",
        chunk_size=10000,
    )
    pass
