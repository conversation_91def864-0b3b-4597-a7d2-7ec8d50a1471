#!/usr/bin/env python3
"""测试两种日志聚类方法"""

import sys
import os
import pandas as pd
import numpy as np

# 添加路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'control_log_diagnose/src2'))

from log_cluster import LogTemplateExtractor

def test_with_sample_logs():
    """使用示例日志测试聚类方法"""
    
    # 创建示例日志数据
    sample_logs = [
        "User login successful from *************",
        "User login successful from *************", 
        "User login successful from ********",
        "User login failed from *************",
        "User login failed from *********",
        "Database connection established to mysql://localhost:3306",
        "Database connection established to mysql://db.example.com:3306",
        "Database connection failed to mysql://localhost:3306",
        "Processing request 12345 with status success",
        "Processing request 67890 with status success",
        "Processing request 11111 with status failed",
        "System startup completed in 5.2 seconds",
        "System startup completed in 3.8 seconds",
        "Memory usage: 85% of 16GB",
        "Memory usage: 92% of 32GB",
        "CPU usage: 45% on 8 cores",
        "CPU usage: 78% on 16 cores",
        "Error: File not found /tmp/data.txt",
        "Error: File not found /var/log/app.log",
        "Warning: Disk space low on /dev/sda1"
    ]
    
    print(f"测试数据: {len(sample_logs)} 条示例日志")
    
    # 初始化聚类器
    clusterer = LogTemplateExtractor()
    
    # 预处理日志
    print("\n=== 预处理日志 ===")
    processed_logs = []
    for log in sample_logs:
        processed = clusterer.preprocess_log(log)
        processed_logs.append(processed)
        print(f"原始: {log}")
        print(f"处理: {processed}")
        print("-" * 40)
    
    print(f"\n预处理完成，共 {len(processed_logs)} 条日志")
    
    # 方法1: LLM模板聚类
    print("\n" + "="*60)
    print("方法1: LLM模板聚类")
    print("="*60)
    
    try:
        llm_clusters = clusterer.cluster_logs_with_llm_templates(
            processed_logs, 
            max_iterations=3, 
            min_match_ratio=0.8
        )
        
        print(f"\nLLM聚类结果:")
        for template, indices in llm_clusters.items():
            print(f"\n模板: {template}")
            print(f"匹配日志数: {len(indices)}")
            print("匹配的日志:")
            for idx in indices[:3]:  # 只显示前3个
                print(f"  - {sample_logs[idx]}")
            if len(indices) > 3:
                print(f"  ... 还有 {len(indices) - 3} 条")
            print("-" * 50)
            
    except Exception as e:
        print(f"LLM聚类失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 方法2: Embedding聚类 (简化版，不调用API)
    print("\n" + "="*60)
    print("方法2: 基于规则的简单聚类 (模拟embedding效果)")
    print("="*60)
    
    # 简单的基于关键词的聚类，模拟embedding聚类效果
    def simple_keyword_clustering(logs):
        """基于关键词的简单聚类"""
        clusters = {}
        
        for i, log in enumerate(logs):
            words = log.lower().split()
            
            # 定义一些关键词模式
            if any(word in words for word in ['login', 'user']):
                cluster_key = 'user_auth'
            elif any(word in words for word in ['database', 'mysql', 'connection']):
                cluster_key = 'database'
            elif any(word in words for word in ['processing', 'request']):
                cluster_key = 'request_processing'
            elif any(word in words for word in ['system', 'startup']):
                cluster_key = 'system_startup'
            elif any(word in words for word in ['memory', 'cpu', 'usage']):
                cluster_key = 'resource_monitoring'
            elif any(word in words for word in ['error', 'warning']):
                cluster_key = 'errors_warnings'
            else:
                cluster_key = 'other'
            
            if cluster_key not in clusters:
                clusters[cluster_key] = []
            clusters[cluster_key].append(i)
        
        return clusters
    
    simple_clusters = simple_keyword_clustering(processed_logs)
    
    print(f"\n简单聚类结果:")
    for cluster_name, indices in simple_clusters.items():
        print(f"\n聚类: {cluster_name}")
        print(f"日志数: {len(indices)}")
        print("示例日志:")
        for idx in indices[:3]:  # 只显示前3个
            print(f"  - {sample_logs[idx]}")
        if len(indices) > 3:
            print(f"  ... 还有 {len(indices) - 3} 条")
        print("-" * 50)
    
    return processed_logs, llm_clusters if 'llm_clusters' in locals() else None, simple_clusters

def analyze_clustering_results(processed_logs, llm_clusters, simple_clusters):
    """分析聚类结果"""
    print("\n" + "="*60)
    print("聚类结果分析")
    print("="*60)
    
    total_logs = len(processed_logs)
    
    if llm_clusters:
        llm_cluster_count = len(llm_clusters)
        llm_avg_size = np.mean([len(indices) for indices in llm_clusters.values()])
        print(f"LLM聚类:")
        print(f"  - 聚类数量: {llm_cluster_count}")
        print(f"  - 平均聚类大小: {llm_avg_size:.1f}")
        print(f"  - 聚类效果: {'良好' if llm_cluster_count < total_logs * 0.8 else '需要优化'}")
    
    simple_cluster_count = len(simple_clusters)
    simple_avg_size = np.mean([len(indices) for indices in simple_clusters.values()])
    print(f"\n简单聚类:")
    print(f"  - 聚类数量: {simple_cluster_count}")
    print(f"  - 平均聚类大小: {simple_avg_size:.1f}")
    print(f"  - 聚类效果: {'良好' if simple_cluster_count < total_logs * 0.8 else '需要优化'}")

if __name__ == "__main__":
    print("开始测试日志聚类方法...")
    
    processed_logs, llm_clusters, simple_clusters = test_with_sample_logs()
    analyze_clustering_results(processed_logs, llm_clusters, simple_clusters)
    
    print("\n测试完成！")
